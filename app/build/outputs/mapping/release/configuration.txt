# The proguard configuration file for the following section is C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
-keep class net.smbuhs.bbs.MainActivity { <init>(); }
-keep class android.support.v4.widget.NestedScrollView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v4.widget.Space { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.app.AlertController$RecycleListView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.view.menu.ActionMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.view.menu.ExpandedMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.view.menu.ListMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.ActionBarContainer { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.ActionBarContextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.ActionBarOverlayLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.ActionMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.ActivityChooserView$InnerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.AlertDialogLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.ButtonBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.ContentFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.FitWindowsFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.FitWindowsLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.SearchView$SearchAutoComplete { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.Toolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.support.v7.widget.ViewStubCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keepclassmembers class * { *** tryAgain(android.view.View); }


# End of content from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
# The proguard configuration file for the following section is C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.12.0
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
-allowaccessmodification

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.12.0
# The proguard configuration file for the following section is C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\proguard-rules.pro
# SMBUHS BBS App ProGuard Rules
# 优化配置，保持WebView功能完整性

# 保持WebView相关类
-keep class android.webkit.** { *; }
-keep class * extends android.webkit.WebViewClient { *; }
-keep class * extends android.webkit.WebChromeClient { *; }

# 保持主Activity
-keep class net.smbuhs.bbs.MainActivity { *; }

# 保持JavaScript接口
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# 保持WebView的JavaScript接口
-keepclassmembers class fqcn.of.javascript.interface.for.webview {
   public *;
}

# 保持调试信息
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# 保持异常信息
-keepattributes Exceptions

# 优化配置
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# 保持注解
-keepattributes *Annotation*

# 保持泛型
-keepattributes Signature

# 保持内部类
-keepattributes InnerClasses,EnclosingMethod

# 不混淆Support库
-keep class android.support.** { *; }
-keep interface android.support.** { *; }

# 不警告Support库的过时API
-dontwarn android.support.**

# End of content from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\proguard-rules.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\a8e524f4ec27edc178c95f987594865a\transformed\animated-vector-drawable-27.0.2\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# keep setters in VectorDrawables so that animations can still work.
-keepclassmembers class android.support.graphics.drawable.VectorDrawableCompat$* {
   void set*(***);
   *** get*();
}

# End of content from C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\a8e524f4ec27edc178c95f987594865a\transformed\animated-vector-drawable-27.0.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ff1b3407eaae6ab69ae765e8957f3a8b\transformed\support-core-ui-27.0.2\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Make sure we keep annotations for ViewPager's DecorView
-keepattributes *Annotation*

# End of content from C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ff1b3407eaae6ab69ae765e8957f3a8b\transformed\support-core-ui-27.0.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ed546bc30b72b27fb58252ae094793e1\transformed\runtime-1.0.3\proguard.txt
-keepattributes *Annotation*

-keepclassmembers enum android.arch.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements android.arch.lifecycle.LifecycleObserver {
}

-keep class * implements android.arch.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @android.arch.lifecycle.OnLifecycleEvent *;
}
# End of content from C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ed546bc30b72b27fb58252ae094793e1\transformed\runtime-1.0.3\proguard.txt
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>