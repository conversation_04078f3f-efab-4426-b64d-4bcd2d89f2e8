-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:2:1-22:12
INJECTED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:2:1-22:12
INJECTED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:2:1-22:12
INJECTED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:2:1-22:12
MERGED from [com.android.support:appcompat-v7:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support.constraint:constraint-layout:1.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\00e405b0cf73f315c97110fd1c7d2138\transformed\constraint-layout-1.0.2\AndroidManifest.xml:2:1-11:12
MERGED from [com.android.support:support-fragment:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\72f6a783120ad641286780438eba1ed8\transformed\support-fragment-27.0.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-core-utils:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\c6d4d4ae35c2d693d5859cc29fed57a7\transformed\support-core-utils-27.0.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:animated-vector-drawable:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\a8e524f4ec27edc178c95f987594865a\transformed\animated-vector-drawable-27.0.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-vector-drawable:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ba625c38c0d6b4897fae7ec1b1fa1ae5\transformed\support-vector-drawable-27.0.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-core-ui:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ff1b3407eaae6ab69ae765e8957f3a8b\transformed\support-core-ui-27.0.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-compat:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:runtime:1.0.3] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ed546bc30b72b27fb58252ae094793e1\transformed\runtime-1.0.3\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:3:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:3:22-64
application
ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:4:5-20:19
INJECTED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:4:5-20:19
MERGED from [com.android.support.constraint:constraint-layout:1.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\00e405b0cf73f315c97110fd1c7d2138\transformed\constraint-layout-1.0.2\AndroidManifest.xml:9:5-20
MERGED from [com.android.support.constraint:constraint-layout:1.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\00e405b0cf73f315c97110fd1c7d2138\transformed\constraint-layout-1.0.2\AndroidManifest.xml:9:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml
	android:supportsRtl
		ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:9:9-35
	android:label
		ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:7:9-41
	android:roundIcon
		ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:8:9-54
	android:icon
		ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:6:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:5:9-35
	android:theme
		ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:10:9-51
activity#com.hhilan.flarum.MainActivity
ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:11:9-19:20
	android:configChanges
		ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:12:13-74
	android:name
		ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:11:19-64
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:14:13-18:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:15:17-69
	android:name
		ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:15:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:17:17-77
	android:name
		ADDED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:17:27-74
uses-sdk
INJECTED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml
MERGED from [com.android.support:appcompat-v7:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:appcompat-v7:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.support.constraint:constraint-layout:1.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\00e405b0cf73f315c97110fd1c7d2138\transformed\constraint-layout-1.0.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.support.constraint:constraint-layout:1.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\00e405b0cf73f315c97110fd1c7d2138\transformed\constraint-layout-1.0.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.support:support-fragment:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\72f6a783120ad641286780438eba1ed8\transformed\support-fragment-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-fragment:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\72f6a783120ad641286780438eba1ed8\transformed\support-fragment-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-utils:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\c6d4d4ae35c2d693d5859cc29fed57a7\transformed\support-core-utils-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-utils:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\c6d4d4ae35c2d693d5859cc29fed57a7\transformed\support-core-utils-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:animated-vector-drawable:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\a8e524f4ec27edc178c95f987594865a\transformed\animated-vector-drawable-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:animated-vector-drawable:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\a8e524f4ec27edc178c95f987594865a\transformed\animated-vector-drawable-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-vector-drawable:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ba625c38c0d6b4897fae7ec1b1fa1ae5\transformed\support-vector-drawable-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-vector-drawable:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ba625c38c0d6b4897fae7ec1b1fa1ae5\transformed\support-vector-drawable-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-ui:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ff1b3407eaae6ab69ae765e8957f3a8b\transformed\support-core-ui-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-ui:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ff1b3407eaae6ab69ae765e8957f3a8b\transformed\support-core-ui-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-compat:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-compat:27.0.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:runtime:1.0.3] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ed546bc30b72b27fb58252ae094793e1\transformed\runtime-1.0.3\AndroidManifest.xml:20:5-22:41
MERGED from [android.arch.lifecycle:runtime:1.0.3] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ed546bc30b72b27fb58252ae094793e1\transformed\runtime-1.0.3\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml
