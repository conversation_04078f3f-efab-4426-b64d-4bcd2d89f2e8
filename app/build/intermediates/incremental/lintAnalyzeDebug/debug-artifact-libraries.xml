<libraries>
  <library
      name="com.android.support:appcompat-v7:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\jars\classes.jar"
      resolved="com.android.support:appcompat-v7:27.0.2"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support.constraint:constraint-layout:1.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\00e405b0cf73f315c97110fd1c7d2138\transformed\constraint-layout-1.0.2\jars\classes.jar"
      resolved="com.android.support.constraint:constraint-layout:1.0.2"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\00e405b0cf73f315c97110fd1c7d2138\transformed\constraint-layout-1.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-fragment:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\72f6a783120ad641286780438eba1ed8\transformed\support-fragment-27.0.2\jars\classes.jar"
      resolved="com.android.support:support-fragment:27.0.2"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\72f6a783120ad641286780438eba1ed8\transformed\support-fragment-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-core-utils:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\c6d4d4ae35c2d693d5859cc29fed57a7\transformed\support-core-utils-27.0.2\jars\classes.jar"
      resolved="com.android.support:support-core-utils:27.0.2"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\c6d4d4ae35c2d693d5859cc29fed57a7\transformed\support-core-utils-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:animated-vector-drawable:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\a8e524f4ec27edc178c95f987594865a\transformed\animated-vector-drawable-27.0.2\jars\classes.jar"
      resolved="com.android.support:animated-vector-drawable:27.0.2"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\a8e524f4ec27edc178c95f987594865a\transformed\animated-vector-drawable-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-vector-drawable:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ba625c38c0d6b4897fae7ec1b1fa1ae5\transformed\support-vector-drawable-27.0.2\jars\classes.jar"
      resolved="com.android.support:support-vector-drawable:27.0.2"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ba625c38c0d6b4897fae7ec1b1fa1ae5\transformed\support-vector-drawable-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-core-ui:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ff1b3407eaae6ab69ae765e8957f3a8b\transformed\support-core-ui-27.0.2\jars\classes.jar"
      resolved="com.android.support:support-core-ui:27.0.2"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ff1b3407eaae6ab69ae765e8957f3a8b\transformed\support-core-ui-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-compat:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\jars\classes.jar"
      resolved="com.android.support:support-compat:27.0.2"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-annotations:27.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.android.support\support-annotations\27.0.2\b9ef4342c934a1a8b107506273dc8061662a322\support-annotations-27.0.2.jar"
      resolved="com.android.support:support-annotations:27.0.2"/>
  <library
      name="android.arch.lifecycle:runtime:1.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ed546bc30b72b27fb58252ae094793e1\transformed\runtime-1.0.3\jars\classes.jar"
      resolved="android.arch.lifecycle:runtime:1.0.3"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ed546bc30b72b27fb58252ae094793e1\transformed\runtime-1.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.lifecycle:common:1.0.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\android.arch.lifecycle\common\1.0.3\7d7f60c4783872861222166f6164215f8951c7b1\common-1.0.3.jar"
      resolved="android.arch.lifecycle:common:1.0.3"/>
  <library
      name="android.arch.core:common:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\android.arch.core\common\1.0.0\a2d487452376193fc8c103dd2b9bd5f2b1b44563\common-1.0.0.jar"
      resolved="android.arch.core:common:1.0.0"/>
  <library
      name="com.android.support.constraint:constraint-layout-solver:1.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.android.support.constraint\constraint-layout-solver\1.0.2\b9cd8fc6bd15cb915735d34535db30ece0c44603\constraint-layout-solver-1.0.2.jar"
      resolved="com.android.support.constraint:constraint-layout-solver:1.0.2"/>
</libraries>
