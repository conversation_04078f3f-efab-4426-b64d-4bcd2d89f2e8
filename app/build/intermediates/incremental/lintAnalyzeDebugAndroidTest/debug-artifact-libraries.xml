<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="com.android.support.test.espresso:espresso-core:3.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\e930c47aad46c6d6610e28b35a4486bc\transformed\espresso-core-3.0.1\jars\classes.jar"
      resolved="com.android.support.test.espresso:espresso-core:3.0.1"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\e930c47aad46c6d6610e28b35a4486bc\transformed\espresso-core-3.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support.test:rules:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\f64fa2112cb9a7ae5a8e5c0d04fa825a\transformed\rules-1.0.1\jars\classes.jar"
      resolved="com.android.support.test:rules:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\f64fa2112cb9a7ae5a8e5c0d04fa825a\transformed\rules-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support.test:runner:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\c6a14293c1f9f80d05a17ffbdb8d3da5\transformed\runner-1.0.1\jars\classes.jar"
      resolved="com.android.support.test:runner:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\c6a14293c1f9f80d05a17ffbdb8d3da5\transformed\runner-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:appcompat-v7:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\jars\classes.jar"
      resolved="com.android.support:appcompat-v7:27.0.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support.constraint:constraint-layout:1.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\00e405b0cf73f315c97110fd1c7d2138\transformed\constraint-layout-1.0.2\jars\classes.jar"
      resolved="com.android.support.constraint:constraint-layout:1.0.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\00e405b0cf73f315c97110fd1c7d2138\transformed\constraint-layout-1.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-fragment:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\72f6a783120ad641286780438eba1ed8\transformed\support-fragment-27.0.2\jars\classes.jar"
      resolved="com.android.support:support-fragment:27.0.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\72f6a783120ad641286780438eba1ed8\transformed\support-fragment-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-core-utils:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\c6d4d4ae35c2d693d5859cc29fed57a7\transformed\support-core-utils-27.0.2\jars\classes.jar"
      resolved="com.android.support:support-core-utils:27.0.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\c6d4d4ae35c2d693d5859cc29fed57a7\transformed\support-core-utils-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:animated-vector-drawable:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\a8e524f4ec27edc178c95f987594865a\transformed\animated-vector-drawable-27.0.2\jars\classes.jar"
      resolved="com.android.support:animated-vector-drawable:27.0.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\a8e524f4ec27edc178c95f987594865a\transformed\animated-vector-drawable-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-vector-drawable:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ba625c38c0d6b4897fae7ec1b1fa1ae5\transformed\support-vector-drawable-27.0.2\jars\classes.jar"
      resolved="com.android.support:support-vector-drawable:27.0.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ba625c38c0d6b4897fae7ec1b1fa1ae5\transformed\support-vector-drawable-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-core-ui:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ff1b3407eaae6ab69ae765e8957f3a8b\transformed\support-core-ui-27.0.2\jars\classes.jar"
      resolved="com.android.support:support-core-ui:27.0.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ff1b3407eaae6ab69ae765e8957f3a8b\transformed\support-core-ui-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-compat:27.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\jars\classes.jar"
      resolved="com.android.support:support-compat:27.0.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-annotations:27.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.android.support\support-annotations\27.0.2\b9ef4342c934a1a8b107506273dc8061662a322\support-annotations-27.0.2.jar"
      resolved="com.android.support:support-annotations:27.0.2"/>
  <library
      name="junit:junit:4.12@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.12\2973d150c0dc1fefe998f834810d68f278ea58ec\junit-4.12.jar"
      resolved="junit:junit:4.12"/>
  <library
      name="org.hamcrest:hamcrest-integration:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-integration\1.3\5de0c73fef18917cd85d0ab70bb23818685e4dfd\hamcrest-integration-1.3.jar"
      resolved="org.hamcrest:hamcrest-integration:1.3"/>
  <library
      name="org.hamcrest:hamcrest-library:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-library\1.3\4785a3c21320980282f9f33d0d1264a69040538f\hamcrest-library-1.3.jar"
      resolved="org.hamcrest:hamcrest-library:1.3"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="net.sf.kxml:kxml2:2.3.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.sf.kxml\kxml2\2.3.0\ccbc77a5fd907ef863c29f3596c6f54ffa4e9442\kxml2-2.3.0.jar"
      resolved="net.sf.kxml:kxml2:2.3.0"/>
  <library
      name="com.android.support.test.espresso:espresso-idling-resource:3.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\a12152793cb62e899f92d79bdae18db7\transformed\espresso-idling-resource-3.0.1\jars\classes.jar"
      resolved="com.android.support.test.espresso:espresso-idling-resource:3.0.1"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\a12152793cb62e899f92d79bdae18db7\transformed\espresso-idling-resource-3.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup:javawriter:2.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup\javawriter\2.1.1\67ff45d9ae02e583d0f9b3432a5ebbe05c30c966\javawriter-2.1.1.jar"
      resolved="com.squareup:javawriter:2.1.1"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.code.findbugs:jsr305:2.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\2.0.1\516c03b21d50a644d538de0f0369c620989cd8f0\jsr305-2.0.1.jar"
      resolved="com.google.code.findbugs:jsr305:2.0.1"/>
  <library
      name="com.android.support.constraint:constraint-layout-solver:1.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.android.support.constraint\constraint-layout-solver\1.0.2\b9cd8fc6bd15cb915735d34535db30ece0c44603\constraint-layout-solver-1.0.2.jar"
      resolved="com.android.support.constraint:constraint-layout-solver:1.0.2"
      provided="true"/>
  <library
      name="android.arch.lifecycle:runtime:1.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ed546bc30b72b27fb58252ae094793e1\transformed\runtime-1.0.3\jars\classes.jar"
      resolved="android.arch.lifecycle:runtime:1.0.3"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ed546bc30b72b27fb58252ae094793e1\transformed\runtime-1.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.lifecycle:common:1.0.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\android.arch.lifecycle\common\1.0.3\7d7f60c4783872861222166f6164215f8951c7b1\common-1.0.3.jar"
      resolved="android.arch.lifecycle:common:1.0.3"
      provided="true"/>
  <library
      name="android.arch.core:common:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\android.arch.core\common\1.0.0\a2d487452376193fc8c103dd2b9bd5f2b1b44563\common-1.0.0.jar"
      resolved="android.arch.core:common:1.0.0"
      provided="true"/>
</libraries>
