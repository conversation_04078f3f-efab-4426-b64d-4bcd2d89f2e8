<dependencies>
  <compile
      roots=":@@:app::debug,com.android.support.test.espresso:espresso-core:3.0.1@aar,com.android.support.test:rules:1.0.1@aar,com.android.support.test:runner:1.0.1@aar,com.android.support:appcompat-v7:27.0.2@aar,com.android.support.constraint:constraint-layout:1.0.2@aar,com.android.support:support-fragment:27.0.2@aar,com.android.support:support-core-utils:27.0.2@aar,com.android.support:animated-vector-drawable:27.0.2@aar,com.android.support:support-vector-drawable:27.0.2@aar,com.android.support:support-core-ui:27.0.2@aar,com.android.support:support-compat:27.0.2@aar,com.android.support:support-annotations:27.0.2@jar,junit:junit:4.12@jar,org.hamcrest:hamcrest-integration:1.3@jar,org.hamcrest:hamcrest-library:1.3@jar,org.hamcrest:hamcrest-core:1.3@jar,net.sf.kxml:kxml2:2.3.0@jar,com.android.support.test.espresso:espresso-idling-resource:3.0.1@aar,com.squareup:javawriter:2.1.1@jar,javax.inject:javax.inject:1@jar,com.google.code.findbugs:jsr305:2.0.1@jar,com.android.support.constraint:constraint-layout-solver:1.0.2@jar,android.arch.lifecycle:runtime:1.0.3@aar,android.arch.lifecycle:common:1.0.3@jar,android.arch.core:common:1.0.0@jar">
    <dependency
        name=":@@:app::debug"
        simpleName="artifacts::app"/>
    <dependency
        name="com.android.support.test.espresso:espresso-core:3.0.1@aar"
        simpleName="com.android.support.test.espresso:espresso-core"/>
    <dependency
        name="com.android.support.test:rules:1.0.1@aar"
        simpleName="com.android.support.test:rules"/>
    <dependency
        name="com.android.support.test:runner:1.0.1@aar"
        simpleName="com.android.support.test:runner"/>
    <dependency
        name="com.android.support:appcompat-v7:27.0.2@aar"
        simpleName="com.android.support:appcompat-v7"/>
    <dependency
        name="com.android.support.constraint:constraint-layout:1.0.2@aar"
        simpleName="com.android.support.constraint:constraint-layout"/>
    <dependency
        name="com.android.support:support-fragment:27.0.2@aar"
        simpleName="com.android.support:support-fragment"/>
    <dependency
        name="com.android.support:support-core-utils:27.0.2@aar"
        simpleName="com.android.support:support-core-utils"/>
    <dependency
        name="com.android.support:animated-vector-drawable:27.0.2@aar"
        simpleName="com.android.support:animated-vector-drawable"/>
    <dependency
        name="com.android.support:support-vector-drawable:27.0.2@aar"
        simpleName="com.android.support:support-vector-drawable"/>
    <dependency
        name="com.android.support:support-core-ui:27.0.2@aar"
        simpleName="com.android.support:support-core-ui"/>
    <dependency
        name="com.android.support:support-compat:27.0.2@aar"
        simpleName="com.android.support:support-compat"/>
    <dependency
        name="com.android.support:support-annotations:27.0.2@jar"
        simpleName="com.android.support:support-annotations"/>
    <dependency
        name="junit:junit:4.12@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.hamcrest:hamcrest-integration:1.3@jar"
        simpleName="org.hamcrest:hamcrest-integration"/>
    <dependency
        name="org.hamcrest:hamcrest-library:1.3@jar"
        simpleName="org.hamcrest:hamcrest-library"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="net.sf.kxml:kxml2:2.3.0@jar"
        simpleName="net.sf.kxml:kxml2"/>
    <dependency
        name="com.android.support.test.espresso:espresso-idling-resource:3.0.1@aar"
        simpleName="com.android.support.test.espresso:espresso-idling-resource"/>
    <dependency
        name="com.squareup:javawriter:2.1.1@jar"
        simpleName="com.squareup:javawriter"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.code.findbugs:jsr305:2.0.1@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.android.support.constraint:constraint-layout-solver:1.0.2@jar"
        simpleName="com.android.support.constraint:constraint-layout-solver"/>
    <dependency
        name="android.arch.lifecycle:runtime:1.0.3@aar"
        simpleName="android.arch.lifecycle:runtime"/>
    <dependency
        name="android.arch.lifecycle:common:1.0.3@jar"
        simpleName="android.arch.lifecycle:common"/>
    <dependency
        name="android.arch.core:common:1.0.0@jar"
        simpleName="android.arch.core:common"/>
  </compile>
  <package
      roots="com.android.support.test.espresso:espresso-core:3.0.1@aar,com.android.support.test:rules:1.0.1@aar,com.android.support.test:runner:1.0.1@aar,com.android.support:support-annotations:27.0.2@jar,junit:junit:4.12@jar,net.sf.kxml:kxml2:2.3.0@jar,com.android.support.test.espresso:espresso-idling-resource:3.0.1@aar,com.squareup:javawriter:2.1.1@jar,javax.inject:javax.inject:1@jar,org.hamcrest:hamcrest-integration:1.3@jar,org.hamcrest:hamcrest-library:1.3@jar,com.google.code.findbugs:jsr305:2.0.1@jar,org.hamcrest:hamcrest-core:1.3@jar">
    <dependency
        name="com.android.support.test.espresso:espresso-core:3.0.1@aar"
        simpleName="com.android.support.test.espresso:espresso-core"/>
    <dependency
        name="com.android.support.test:rules:1.0.1@aar"
        simpleName="com.android.support.test:rules"/>
    <dependency
        name="com.android.support.test:runner:1.0.1@aar"
        simpleName="com.android.support.test:runner"/>
    <dependency
        name="com.android.support:support-annotations:27.0.2@jar"
        simpleName="com.android.support:support-annotations"/>
    <dependency
        name="junit:junit:4.12@jar"
        simpleName="junit:junit"/>
    <dependency
        name="net.sf.kxml:kxml2:2.3.0@jar"
        simpleName="net.sf.kxml:kxml2"/>
    <dependency
        name="com.android.support.test.espresso:espresso-idling-resource:3.0.1@aar"
        simpleName="com.android.support.test.espresso:espresso-idling-resource"/>
    <dependency
        name="com.squareup:javawriter:2.1.1@jar"
        simpleName="com.squareup:javawriter"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.hamcrest:hamcrest-integration:1.3@jar"
        simpleName="org.hamcrest:hamcrest-integration"/>
    <dependency
        name="org.hamcrest:hamcrest-library:1.3@jar"
        simpleName="org.hamcrest:hamcrest-library"/>
    <dependency
        name="com.google.code.findbugs:jsr305:2.0.1@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
  </package>
</dependencies>
