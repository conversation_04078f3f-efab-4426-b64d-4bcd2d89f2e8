[{"key": "android/support/fragment/BuildConfig.class", "name": "android/support/fragment/BuildConfig.class", "size": 585, "crc": -824697558}, {"key": "android/support/v4/app/BackStackRecord$Op.class", "name": "android/support/v4/app/BackStackRecord$Op.class", "size": 726, "crc": -919881483}, {"key": "android/support/v4/app/BackStackRecord.class", "name": "android/support/v4/app/BackStackRecord.class", "size": 21200, "crc": 1184552122}, {"key": "android/support/v4/app/BackStackState$1.class", "name": "android/support/v4/app/BackStackState$1.class", "size": 1184, "crc": -1758405856}, {"key": "android/support/v4/app/BackStackState.class", "name": "android/support/v4/app/BackStackState.class", "size": 5443, "crc": 1929212849}, {"key": "android/support/v4/app/BaseFragmentActivityApi14.class", "name": "android/support/v4/app/BaseFragmentActivityApi14.class", "size": 2250, "crc": -568899013}, {"key": "android/support/v4/app/BaseFragmentActivityApi16.class", "name": "android/support/v4/app/BaseFragmentActivityApi16.class", "size": 1875, "crc": 550837476}, {"key": "android/support/v4/app/DialogFragment$DialogStyle.class", "name": "android/support/v4/app/DialogFragment$DialogStyle.class", "size": 677, "crc": 1360693082}, {"key": "android/support/v4/app/DialogFragment.class", "name": "android/support/v4/app/DialogFragment.class", "size": 8757, "crc": 681205155}, {"key": "android/support/v4/app/Fragment$1.class", "name": "android/support/v4/app/Fragment$1.class", "size": 672, "crc": 1867089547}, {"key": "android/support/v4/app/Fragment$2.class", "name": "android/support/v4/app/Fragment$2.class", "size": 1578, "crc": 455121604}, {"key": "android/support/v4/app/Fragment$AnimationInfo.class", "name": "android/support/v4/app/Fragment$AnimationInfo.class", "size": 3152, "crc": -204162664}, {"key": "android/support/v4/app/Fragment$InstantiationException.class", "name": "android/support/v4/app/Fragment$InstantiationException.class", "size": 613, "crc": -929025801}, {"key": "android/support/v4/app/Fragment$OnStartEnterTransitionListener.class", "name": "android/support/v4/app/Fragment$OnStartEnterTransitionListener.class", "size": 316, "crc": 1849479725}, {"key": "android/support/v4/app/Fragment$SavedState$1.class", "name": "android/support/v4/app/Fragment$SavedState$1.class", "size": 1289, "crc": 916800895}, {"key": "android/support/v4/app/Fragment$SavedState.class", "name": "android/support/v4/app/Fragment$SavedState.class", "size": 1583, "crc": -1429746685}, {"key": "android/support/v4/app/Fragment.class", "name": "android/support/v4/app/Fragment.class", "size": 40972, "crc": -1202390701}, {"key": "android/support/v4/app/FragmentActivity$1.class", "name": "android/support/v4/app/FragmentActivity$1.class", "size": 1130, "crc": 1323350583}, {"key": "android/support/v4/app/FragmentActivity$HostCallbacks.class", "name": "android/support/v4/app/FragmentActivity$HostCallbacks.class", "size": 4862, "crc": 1286988973}, {"key": "android/support/v4/app/FragmentActivity$NonConfigurationInstances.class", "name": "android/support/v4/app/FragmentActivity$NonConfigurationInstances.class", "size": 767, "crc": 607909716}, {"key": "android/support/v4/app/FragmentActivity.class", "name": "android/support/v4/app/FragmentActivity.class", "size": 20968, "crc": -89594421}, {"key": "android/support/v4/app/FragmentContainer.class", "name": "android/support/v4/app/FragmentContainer.class", "size": 928, "crc": -809821368}, {"key": "android/support/v4/app/FragmentController.class", "name": "android/support/v4/app/FragmentController.class", "size": 8469, "crc": 733067140}, {"key": "android/support/v4/app/FragmentHostCallback.class", "name": "android/support/v4/app/FragmentHostCallback.class", "size": 10711, "crc": -1646500018}, {"key": "android/support/v4/app/FragmentManager$BackStackEntry.class", "name": "android/support/v4/app/FragmentManager$BackStackEntry.class", "size": 560, "crc": 1942629142}, {"key": "android/support/v4/app/FragmentManager$FragmentLifecycleCallbacks.class", "name": "android/support/v4/app/FragmentManager$FragmentLifecycleCallbacks.class", "size": 2554, "crc": 1964170855}, {"key": "android/support/v4/app/FragmentManager$OnBackStackChangedListener.class", "name": "android/support/v4/app/FragmentManager$OnBackStackChangedListener.class", "size": 300, "crc": 1866554827}, {"key": "android/support/v4/app/FragmentManager.class", "name": "android/support/v4/app/FragmentManager.class", "size": 3131, "crc": 657138230}, {"key": "android/support/v4/app/FragmentManagerImpl$1.class", "name": "android/support/v4/app/FragmentManagerImpl$1.class", "size": 712, "crc": 1645282249}, {"key": "android/support/v4/app/FragmentManagerImpl$2$1.class", "name": "android/support/v4/app/FragmentManagerImpl$2$1.class", "size": 1447, "crc": -59730553}, {"key": "android/support/v4/app/FragmentManagerImpl$2.class", "name": "android/support/v4/app/FragmentManagerImpl$2.class", "size": 1986, "crc": -169291953}, {"key": "android/support/v4/app/FragmentManagerImpl$3.class", "name": "android/support/v4/app/FragmentManagerImpl$3.class", "size": 1796, "crc": 704793208}, {"key": "android/support/v4/app/FragmentManagerImpl$4.class", "name": "android/support/v4/app/FragmentManagerImpl$4.class", "size": 1572, "crc": -1183950231}, {"key": "android/support/v4/app/FragmentManagerImpl$AnimateOnHWLayerIfNeededListener$1.class", "name": "android/support/v4/app/FragmentManagerImpl$AnimateOnHWLayerIfNeededListener$1.class", "size": 1116, "crc": 1973811289}, {"key": "android/support/v4/app/FragmentManagerImpl$AnimateOnHWLayerIfNeededListener.class", "name": "android/support/v4/app/FragmentManagerImpl$AnimateOnHWLayerIfNeededListener.class", "size": 1954, "crc": 2129442168}, {"key": "android/support/v4/app/FragmentManagerImpl$AnimationListenerWrapper.class", "name": "android/support/v4/app/FragmentManagerImpl$AnimationListenerWrapper.class", "size": 1693, "crc": 304500820}, {"key": "android/support/v4/app/FragmentManagerImpl$AnimationOrAnimator.class", "name": "android/support/v4/app/FragmentManagerImpl$AnimationOrAnimator.class", "size": 1563, "crc": 1674675565}, {"key": "android/support/v4/app/FragmentManagerImpl$AnimatorOnHWLayerIfNeededListener.class", "name": "android/support/v4/app/FragmentManagerImpl$AnimatorOnHWLayerIfNeededListener.class", "size": 1179, "crc": -371241102}, {"key": "android/support/v4/app/FragmentManagerImpl$FragmentTag.class", "name": "android/support/v4/app/FragmentManagerImpl$FragmentTag.class", "size": 689, "crc": 1256115420}, {"key": "android/support/v4/app/FragmentManagerImpl$OpGenerator.class", "name": "android/support/v4/app/FragmentManagerImpl$OpGenerator.class", "size": 444, "crc": -1695754682}, {"key": "android/support/v4/app/FragmentManagerImpl$PopBackStackState.class", "name": "android/support/v4/app/FragmentManagerImpl$PopBackStackState.class", "size": 1897, "crc": -1775556171}, {"key": "android/support/v4/app/FragmentManagerImpl$StartEnterTransitionListener.class", "name": "android/support/v4/app/FragmentManagerImpl$StartEnterTransitionListener.class", "size": 2734, "crc": -409488228}, {"key": "android/support/v4/app/FragmentManagerImpl.class", "name": "android/support/v4/app/FragmentManagerImpl.class", "size": 72564, "crc": -1447912568}, {"key": "android/support/v4/app/FragmentManagerNonConfig.class", "name": "android/support/v4/app/FragmentManagerNonConfig.class", "size": 1200, "crc": -1912119167}, {"key": "android/support/v4/app/FragmentManagerState$1.class", "name": "android/support/v4/app/FragmentManagerState$1.class", "size": 1220, "crc": -39856794}, {"key": "android/support/v4/app/FragmentManagerState.class", "name": "android/support/v4/app/FragmentManagerState.class", "size": 1863, "crc": -808451617}, {"key": "android/support/v4/app/FragmentPagerAdapter.class", "name": "android/support/v4/app/FragmentPagerAdapter.class", "size": 4069, "crc": 600459850}, {"key": "android/support/v4/app/FragmentState$1.class", "name": "android/support/v4/app/FragmentState$1.class", "size": 1176, "crc": -899309370}, {"key": "android/support/v4/app/FragmentState.class", "name": "android/support/v4/app/FragmentState.class", "size": 4580, "crc": -704928590}, {"key": "android/support/v4/app/FragmentStatePagerAdapter.class", "name": "android/support/v4/app/FragmentStatePagerAdapter.class", "size": 6691, "crc": -34043729}, {"key": "android/support/v4/app/FragmentTabHost$DummyTabFactory.class", "name": "android/support/v4/app/FragmentTabHost$DummyTabFactory.class", "size": 989, "crc": -279775214}, {"key": "android/support/v4/app/FragmentTabHost$SavedState$1.class", "name": "android/support/v4/app/FragmentTabHost$SavedState$1.class", "size": 1321, "crc": -239458498}, {"key": "android/support/v4/app/FragmentTabHost$SavedState.class", "name": "android/support/v4/app/FragmentTabHost$SavedState.class", "size": 1866, "crc": 337412703}, {"key": "android/support/v4/app/FragmentTabHost$TabInfo.class", "name": "android/support/v4/app/FragmentTabHost$TabInfo.class", "size": 1120, "crc": 1073578451}, {"key": "android/support/v4/app/FragmentTabHost.class", "name": "android/support/v4/app/FragmentTabHost.class", "size": 8833, "crc": -2111701834}, {"key": "android/support/v4/app/FragmentTransaction$Transit.class", "name": "android/support/v4/app/FragmentTransaction$Transit.class", "size": 684, "crc": -183670205}, {"key": "android/support/v4/app/FragmentTransaction.class", "name": "android/support/v4/app/FragmentTransaction.class", "size": 2903, "crc": -1387353993}, {"key": "android/support/v4/app/FragmentTransition$1.class", "name": "android/support/v4/app/FragmentTransition$1.class", "size": 847, "crc": -873337288}, {"key": "android/support/v4/app/FragmentTransition$2.class", "name": "android/support/v4/app/FragmentTransition$2.class", "size": 2347, "crc": -1067594754}, {"key": "android/support/v4/app/FragmentTransition$3.class", "name": "android/support/v4/app/FragmentTransition$3.class", "size": 1967, "crc": -2091142333}, {"key": "android/support/v4/app/FragmentTransition$4.class", "name": "android/support/v4/app/FragmentTransition$4.class", "size": 3473, "crc": 944793883}, {"key": "android/support/v4/app/FragmentTransition$FragmentContainerTransition.class", "name": "android/support/v4/app/FragmentTransition$FragmentContainerTransition.class", "size": 712, "crc": 1798969232}, {"key": "android/support/v4/app/FragmentTransition.class", "name": "android/support/v4/app/FragmentTransition.class", "size": 29641, "crc": -535033923}, {"key": "android/support/v4/app/FragmentTransitionCompat21$1.class", "name": "android/support/v4/app/FragmentTransitionCompat21$1.class", "size": 1059, "crc": 1088813667}, {"key": "android/support/v4/app/FragmentTransitionCompat21$2.class", "name": "android/support/v4/app/FragmentTransitionCompat21$2.class", "size": 1928, "crc": 1017805217}, {"key": "android/support/v4/app/FragmentTransitionCompat21$3.class", "name": "android/support/v4/app/FragmentTransitionCompat21$3.class", "size": 2081, "crc": -2031807773}, {"key": "android/support/v4/app/FragmentTransitionCompat21$4.class", "name": "android/support/v4/app/FragmentTransitionCompat21$4.class", "size": 1169, "crc": -654170577}, {"key": "android/support/v4/app/FragmentTransitionCompat21.class", "name": "android/support/v4/app/FragmentTransitionCompat21.class", "size": 8608, "crc": 2100191128}, {"key": "android/support/v4/app/FragmentTransitionImpl$1.class", "name": "android/support/v4/app/FragmentTransitionImpl$1.class", "size": 1502, "crc": 1983589950}, {"key": "android/support/v4/app/FragmentTransitionImpl$2.class", "name": "android/support/v4/app/FragmentTransitionImpl$2.class", "size": 1594, "crc": 1511240056}, {"key": "android/support/v4/app/FragmentTransitionImpl$3.class", "name": "android/support/v4/app/FragmentTransitionImpl$3.class", "size": 1596, "crc": 1638824289}, {"key": "android/support/v4/app/FragmentTransitionImpl.class", "name": "android/support/v4/app/FragmentTransitionImpl.class", "size": 9266, "crc": 77796679}, {"key": "android/support/v4/app/ListFragment$1.class", "name": "android/support/v4/app/ListFragment$1.class", "size": 781, "crc": 228945297}, {"key": "android/support/v4/app/ListFragment$2.class", "name": "android/support/v4/app/ListFragment$2.class", "size": 1191, "crc": -2018837548}, {"key": "android/support/v4/app/ListFragment.class", "name": "android/support/v4/app/ListFragment.class", "size": 7333, "crc": -1336973150}, {"key": "android/support/v4/app/LoaderManager$LoaderCallbacks.class", "name": "android/support/v4/app/LoaderManager$LoaderCallbacks.class", "size": 718, "crc": 1216405000}, {"key": "android/support/v4/app/LoaderManager.class", "name": "android/support/v4/app/LoaderManager.class", "size": 1280, "crc": 589544872}, {"key": "android/support/v4/app/LoaderManagerImpl$LoaderInfo.class", "name": "android/support/v4/app/LoaderManagerImpl$LoaderInfo.class", "size": 9816, "crc": 70029384}, {"key": "android/support/v4/app/LoaderManagerImpl.class", "name": "android/support/v4/app/LoaderManagerImpl.class", "size": 10700, "crc": -215252713}, {"key": "android/support/v4/app/OneShotPreDrawListener.class", "name": "android/support/v4/app/OneShotPreDrawListener.class", "size": 2005, "crc": -813879297}, {"key": "android/support/v4/app/SuperNotCalledException.class", "name": "android/support/v4/app/SuperNotCalledException.class", "size": 420, "crc": 168232450}, {"key": "META-INF/com.android.support_support-fragment.version", "name": "META-INF/com.android.support_support-fragment.version", "size": 7, "crc": 172221265}]