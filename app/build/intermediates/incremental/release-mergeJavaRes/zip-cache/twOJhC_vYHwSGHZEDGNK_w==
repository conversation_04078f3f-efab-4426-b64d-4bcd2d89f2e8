[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "android/support/constraint/solver/ArrayRow.class", "name": "android/support/constraint/solver/ArrayRow.class", "size": 9211, "crc": -1157092018}, {"key": "android/support/constraint/solver/Pools$Pool.class", "name": "android/support/constraint/solver/Pools$Pool.class", "size": 458, "crc": 560087521}, {"key": "android/support/constraint/solver/SolverVariable$1.class", "name": "android/support/constraint/solver/SolverVariable$1.class", "size": 958, "crc": -482842627}, {"key": "android/support/constraint/solver/Cache.class", "name": "android/support/constraint/solver/Cache.class", "size": 1075, "crc": 198471071}, {"key": "android/support/constraint/solver/ArrayLinkedVariables.class", "name": "android/support/constraint/solver/ArrayLinkedVariables.class", "size": 9702, "crc": 794528738}, {"key": "android/support/constraint/solver/Pools$SimplePool.class", "name": "android/support/constraint/solver/Pools$SimplePool.class", "size": 2009, "crc": 424749262}, {"key": "android/support/constraint/solver/SolverVariable$Type.class", "name": "android/support/constraint/solver/SolverVariable$Type.class", "size": 1400, "crc": 174024340}, {"key": "android/support/constraint/solver/Goal.class", "name": "android/support/constraint/solver/Goal.class", "size": 3659, "crc": 1838297834}, {"key": "android/support/constraint/solver/LinearSystem.class", "name": "android/support/constraint/solver/LinearSystem.class", "size": 19824, "crc": 372524407}, {"key": "android/support/constraint/solver/Pools.class", "name": "android/support/constraint/solver/Pools.class", "size": 526, "crc": -1772024198}, {"key": "android/support/constraint/solver/SolverVariable.class", "name": "android/support/constraint/solver/SolverVariable.class", "size": 4218, "crc": 348101894}, {"key": "android/support/constraint/solver/widgets/WidgetContainer.class", "name": "android/support/constraint/solver/widgets/WidgetContainer.class", "size": 6009, "crc": -1543712605}, {"key": "android/support/constraint/solver/widgets/ConstraintWidget$DimensionBehaviour.class", "name": "android/support/constraint/solver/widgets/ConstraintWidget$DimensionBehaviour.class", "size": 1519, "crc": 1488328337}, {"key": "android/support/constraint/solver/widgets/ConstraintWidget.class", "name": "android/support/constraint/solver/widgets/ConstraintWidget.class", "size": 40880, "crc": -391800405}, {"key": "android/support/constraint/solver/widgets/Optimizer.class", "name": "android/support/constraint/solver/widgets/Optimizer.class", "size": 11271, "crc": 1468601825}, {"key": "android/support/constraint/solver/widgets/ConstraintTableLayout$HorizontalSlice.class", "name": "android/support/constraint/solver/widgets/ConstraintTableLayout$HorizontalSlice.class", "size": 819, "crc": 742971308}, {"key": "android/support/constraint/solver/widgets/ConstraintTableLayout$VerticalSlice.class", "name": "android/support/constraint/solver/widgets/ConstraintTableLayout$VerticalSlice.class", "size": 852, "crc": 1453392787}, {"key": "android/support/constraint/solver/widgets/Guideline.class", "name": "android/support/constraint/solver/widgets/Guideline.class", "size": 7771, "crc": 1506572660}, {"key": "android/support/constraint/solver/widgets/ConstraintAnchor$ConnectionType.class", "name": "android/support/constraint/solver/widgets/ConstraintAnchor$ConnectionType.class", "size": 1369, "crc": 1222483661}, {"key": "android/support/constraint/solver/widgets/ConstraintWidgetContainer$2.class", "name": "android/support/constraint/solver/widgets/ConstraintWidgetContainer$2.class", "size": 1148, "crc": -1197435273}, {"key": "android/support/constraint/solver/widgets/ConstraintWidget$ContentAlignment.class", "name": "android/support/constraint/solver/widgets/ConstraintWidget$ContentAlignment.class", "size": 1684, "crc": 2095827424}, {"key": "android/support/constraint/solver/widgets/Guideline$1.class", "name": "android/support/constraint/solver/widgets/Guideline$1.class", "size": 1051, "crc": -310060042}, {"key": "android/support/constraint/solver/widgets/Snapshot$Connection.class", "name": "android/support/constraint/solver/widgets/Snapshot$Connection.class", "size": 2355, "crc": 985442908}, {"key": "android/support/constraint/solver/widgets/ConstraintWidgetContainer.class", "name": "android/support/constraint/solver/widgets/ConstraintWidgetContainer.class", "size": 36716, "crc": 1962937679}, {"key": "android/support/constraint/solver/widgets/Snapshot.class", "name": "android/support/constraint/solver/widgets/Snapshot.class", "size": 2460, "crc": 341162525}, {"key": "android/support/constraint/solver/widgets/ConstraintAnchor.class", "name": "android/support/constraint/solver/widgets/ConstraintAnchor.class", "size": 11310, "crc": 413115793}, {"key": "android/support/constraint/solver/widgets/ConstraintHorizontalLayout$ContentAlignment.class", "name": "android/support/constraint/solver/widgets/ConstraintHorizontalLayout$ContentAlignment.class", "size": 1764, "crc": -1164881911}, {"key": "android/support/constraint/solver/widgets/ConstraintAnchor$1.class", "name": "android/support/constraint/solver/widgets/ConstraintAnchor$1.class", "size": 1205, "crc": -1853968350}, {"key": "android/support/constraint/solver/widgets/ConstraintAnchor$Strength.class", "name": "android/support/constraint/solver/widgets/ConstraintAnchor$Strength.class", "size": 1371, "crc": 1087152430}, {"key": "android/support/constraint/solver/widgets/Rectangle.class", "name": "android/support/constraint/solver/widgets/Rectangle.class", "size": 1309, "crc": -239723610}, {"key": "android/support/constraint/solver/widgets/ConstraintTableLayout.class", "name": "android/support/constraint/solver/widgets/ConstraintTableLayout.class", "size": 11797, "crc": -771052122}, {"key": "android/support/constraint/solver/widgets/ConstraintWidget$1.class", "name": "android/support/constraint/solver/widgets/ConstraintWidget$1.class", "size": 1269, "crc": -1190668001}, {"key": "android/support/constraint/solver/widgets/ConstraintHorizontalLayout.class", "name": "android/support/constraint/solver/widgets/ConstraintHorizontalLayout.class", "size": 2967, "crc": -596160163}, {"key": "android/support/constraint/solver/widgets/ConstraintAnchor$Type.class", "name": "android/support/constraint/solver/widgets/ConstraintAnchor$Type.class", "size": 1646, "crc": -245918589}]