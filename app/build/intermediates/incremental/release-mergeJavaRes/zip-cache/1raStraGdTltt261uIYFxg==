[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "android/arch/core/internal/SafeIterableMap$ListIterator.class", "name": "android/arch/core/internal/SafeIterableMap$ListIterator.class", "size": 3005, "crc": 2085659780}, {"key": "android/arch/core/internal/SafeIterableMap$1.class", "name": "android/arch/core/internal/SafeIterableMap$1.class", "size": 247, "crc": 217955539}, {"key": "android/arch/core/internal/SafeIterableMap$SupportRemove.class", "name": "android/arch/core/internal/SafeIterableMap$SupportRemove.class", "size": 643, "crc": -2003644650}, {"key": "android/arch/core/internal/SafeIterableMap$Entry.class", "name": "android/arch/core/internal/SafeIterableMap$Entry.class", "size": 2267, "crc": 1098628861}, {"key": "android/arch/core/internal/FastSafeIterableMap.class", "name": "android/arch/core/internal/FastSafeIterableMap.class", "size": 2869, "crc": -1310794685}, {"key": "android/arch/core/internal/SafeIterableMap.class", "name": "android/arch/core/internal/SafeIterableMap.class", "size": 7302, "crc": -1570517390}, {"key": "android/arch/core/internal/SafeIterableMap$IteratorWithAdditions.class", "name": "android/arch/core/internal/SafeIterableMap$IteratorWithAdditions.class", "size": 2752, "crc": 975894541}, {"key": "android/arch/core/internal/SafeIterableMap$DescendingIterator.class", "name": "android/arch/core/internal/SafeIterableMap$DescendingIterator.class", "size": 1762, "crc": -583180105}, {"key": "android/arch/core/internal/SafeIterableMap$AscendingIterator.class", "name": "android/arch/core/internal/SafeIterableMap$AscendingIterator.class", "size": 1758, "crc": 204518234}]