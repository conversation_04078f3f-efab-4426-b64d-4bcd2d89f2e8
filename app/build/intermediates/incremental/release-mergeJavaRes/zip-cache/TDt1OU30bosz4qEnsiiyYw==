[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "android/support/annotation/RequiresPermission$Write.class", "name": "android/support/annotation/RequiresPermission$Write.class", "size": 586, "crc": -2036437371}, {"key": "android/support/annotation/CheckResult.class", "name": "android/support/annotation/CheckResult.class", "size": 513, "crc": -1893689438}, {"key": "android/support/annotation/StringRes.class", "name": "android/support/annotation/StringRes.class", "size": 488, "crc": 445339337}, {"key": "android/support/annotation/HalfFloat.class", "name": "android/support/annotation/HalfFloat.class", "size": 449, "crc": -397540830}, {"key": "android/support/annotation/Px.class", "name": "android/support/annotation/Px.class", "size": 574, "crc": -206259285}, {"key": "android/support/annotation/AttrRes.class", "name": "android/support/annotation/AttrRes.class", "size": 484, "crc": -974450519}, {"key": "android/support/annotation/StringDef.class", "name": "android/support/annotation/StringDef.class", "size": 467, "crc": -1543770944}, {"key": "android/support/annotation/AnyThread.class", "name": "android/support/annotation/AnyThread.class", "size": 467, "crc": 1647411953}, {"key": "android/support/annotation/IntegerRes.class", "name": "android/support/annotation/IntegerRes.class", "size": 490, "crc": 1502776048}, {"key": "android/support/annotation/PluralsRes.class", "name": "android/support/annotation/PluralsRes.class", "size": 490, "crc": 789853318}, {"key": "android/support/annotation/DrawableRes.class", "name": "android/support/annotation/DrawableRes.class", "size": 492, "crc": -2077263923}, {"key": "android/support/annotation/VisibleForTesting.class", "name": "android/support/annotation/VisibleForTesting.class", "size": 526, "crc": 1214635154}, {"key": "android/support/annotation/CallSuper.class", "name": "android/support/annotation/CallSuper.class", "size": 436, "crc": 1819383903}, {"key": "android/support/annotation/TransitionRes.class", "name": "android/support/annotation/TransitionRes.class", "size": 475, "crc": -913329620}, {"key": "android/support/annotation/IntRange.class", "name": "android/support/annotation/IntRange.class", "size": 559, "crc": 866891303}, {"key": "android/support/annotation/RequiresApi.class", "name": "android/support/annotation/RequiresApi.class", "size": 635, "crc": 1308287676}, {"key": "android/support/annotation/AnyRes.class", "name": "android/support/annotation/AnyRes.class", "size": 482, "crc": 867485192}, {"key": "android/support/annotation/LayoutRes.class", "name": "android/support/annotation/LayoutRes.class", "size": 488, "crc": -993775869}, {"key": "android/support/annotation/AnimRes.class", "name": "android/support/annotation/AnimRes.class", "size": 484, "crc": -1508270811}, {"key": "android/support/annotation/StyleRes.class", "name": "android/support/annotation/StyleRes.class", "size": 486, "crc": 1094403837}, {"key": "android/support/annotation/MainThread.class", "name": "android/support/annotation/MainThread.class", "size": 469, "crc": 724262250}, {"key": "android/support/annotation/RestrictTo$Scope.class", "name": "android/support/annotation/RestrictTo$Scope.class", "size": 1416, "crc": -157189434}, {"key": "android/support/annotation/RestrictTo.class", "name": "android/support/annotation/RestrictTo.class", "size": 627, "crc": 1232444855}, {"key": "android/support/annotation/NonNull.class", "name": "android/support/annotation/NonNull.class", "size": 522, "crc": 671934921}, {"key": "android/support/annotation/BinderThread.class", "name": "android/support/annotation/BinderThread.class", "size": 473, "crc": -462970610}, {"key": "android/support/annotation/RequiresPermission.class", "name": "android/support/annotation/RequiresPermission.class", "size": 832, "crc": 359264124}, {"key": "android/support/annotation/NavigationRes.class", "name": "android/support/annotation/NavigationRes.class", "size": 496, "crc": 1443366525}, {"key": "android/support/annotation/BoolRes.class", "name": "android/support/annotation/BoolRes.class", "size": 484, "crc": 199549863}, {"key": "android/support/annotation/ArrayRes.class", "name": "android/support/annotation/ArrayRes.class", "size": 486, "crc": -20176858}, {"key": "android/support/annotation/Nullable.class", "name": "android/support/annotation/Nullable.class", "size": 524, "crc": -1025283787}, {"key": "android/support/annotation/FractionRes.class", "name": "android/support/annotation/FractionRes.class", "size": 492, "crc": 2139304542}, {"key": "android/support/annotation/ColorLong.class", "name": "android/support/annotation/ColorLong.class", "size": 449, "crc": 26052019}, {"key": "android/support/annotation/MenuRes.class", "name": "android/support/annotation/MenuRes.class", "size": 484, "crc": -2096185528}, {"key": "android/support/annotation/WorkerThread.class", "name": "android/support/annotation/WorkerThread.class", "size": 473, "crc": 1303853433}, {"key": "android/support/annotation/FontRes.class", "name": "android/support/annotation/FontRes.class", "size": 484, "crc": 1615038359}, {"key": "android/support/annotation/XmlRes.class", "name": "android/support/annotation/XmlRes.class", "size": 482, "crc": 1051412885}, {"key": "android/support/annotation/IntDef.class", "name": "android/support/annotation/IntDef.class", "size": 479, "crc": -295289896}, {"key": "android/support/annotation/GuardedBy.class", "name": "android/support/annotation/GuardedBy.class", "size": 440, "crc": -537074250}, {"key": "android/support/annotation/ColorInt.class", "name": "android/support/annotation/ColorInt.class", "size": 446, "crc": -1486570551}, {"key": "android/support/annotation/IdRes.class", "name": "android/support/annotation/IdRes.class", "size": 480, "crc": -711484028}, {"key": "android/support/annotation/UiThread.class", "name": "android/support/annotation/UiThread.class", "size": 465, "crc": 1698852040}, {"key": "android/support/annotation/ColorRes.class", "name": "android/support/annotation/ColorRes.class", "size": 486, "crc": -326896857}, {"key": "android/support/annotation/FloatRange.class", "name": "android/support/annotation/FloatRange.class", "size": 638, "crc": -529573524}, {"key": "android/support/annotation/StyleableRes.class", "name": "android/support/annotation/StyleableRes.class", "size": 494, "crc": -734357422}, {"key": "android/support/annotation/Dimension.class", "name": "android/support/annotation/Dimension.class", "size": 659, "crc": -1787053607}, {"key": "android/support/annotation/RequiresPermission$Read.class", "name": "android/support/annotation/RequiresPermission$Read.class", "size": 584, "crc": -1963860093}, {"key": "android/support/annotation/AnimatorRes.class", "name": "android/support/annotation/AnimatorRes.class", "size": 492, "crc": 1001094282}, {"key": "android/support/annotation/RawRes.class", "name": "android/support/annotation/RawRes.class", "size": 482, "crc": -2103155110}, {"key": "android/support/annotation/DimenRes.class", "name": "android/support/annotation/DimenRes.class", "size": 486, "crc": -1326306981}, {"key": "android/support/annotation/Keep.class", "name": "android/support/annotation/Keep.class", "size": 468, "crc": -725507462}, {"key": "android/support/annotation/InterpolatorRes.class", "name": "android/support/annotation/InterpolatorRes.class", "size": 500, "crc": -649710801}, {"key": "android/support/annotation/Size.class", "name": "android/support/annotation/Size.class", "size": 614, "crc": 1131031463}]