<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.TextAppearance.AppCompat.Button">
        <item name="android:textSize">@dimen/abc_text_size_button_material</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Platform.AppCompat" parent="Platform.V14.AppCompat"/>
    <style name="Platform.AppCompat.Light" parent="Platform.V14.AppCompat.Light"/>
    <style name="Platform.V14.AppCompat" parent="Platform.V11.AppCompat">
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style>
    <style name="Platform.V14.AppCompat.Light" parent="Platform.V11.AppCompat.Light">
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style>
</resources>