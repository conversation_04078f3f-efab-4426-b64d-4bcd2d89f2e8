<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="com.android.support:support-compat:27.0.2$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="com.android.support:support-compat:27.0.2" from-dependency="true" generated-set="com.android.support:support-compat:27.0.2$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res"><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values\values.xml" qualifiers=""><color name="notification_action_color_filter">#ffffffff</color><color name="notification_icon_bg_color">#ff9e9e9e</color><color name="ripple_material_light">#1f000000</color><color name="secondary_text_default_material_light">#8a000000</color><declare-styleable name="FontFamily"><attr format="string" name="fontProviderAuthority"/><attr format="string" name="fontProviderPackage"/><attr format="string" name="fontProviderQuery"/><attr format="reference" name="fontProviderCerts"/><attr name="fontProviderFetchStrategy">
            
            <enum name="blocking" value="0"/>
            
            <enum name="async" value="1"/>
        </attr><attr format="integer" name="fontProviderFetchTimeout">
          
            <enum name="forever" value="-1"/>
        </attr></declare-styleable><declare-styleable name="FontFamilyFont"><attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr><attr format="reference" name="font"/><attr format="integer" name="fontWeight"/><attr name="android:fontStyle"/><attr name="android:font"/><attr name="android:fontWeight"/></declare-styleable><dimen name="compat_button_inset_horizontal_material">4dp</dimen><dimen name="compat_button_inset_vertical_material">6dp</dimen><dimen name="compat_button_padding_horizontal_material">8dp</dimen><dimen name="compat_button_padding_vertical_material">4dp</dimen><dimen name="compat_control_corner_material">2dp</dimen><dimen name="notification_action_icon_size">32dp</dimen><dimen name="notification_action_text_size">13sp</dimen><dimen name="notification_big_circle_margin">12dp</dimen><dimen name="notification_content_margin_start">8dp</dimen><dimen name="notification_large_icon_height">64dp</dimen><dimen name="notification_large_icon_width">64dp</dimen><dimen name="notification_main_column_padding_top">10dp</dimen><dimen name="notification_media_narrow_margin">@dimen/notification_content_margin_start</dimen><dimen name="notification_right_icon_size">16dp</dimen><dimen name="notification_right_side_padding_top">2dp</dimen><dimen name="notification_small_icon_background_padding">3dp</dimen><dimen name="notification_small_icon_size_as_large">24dp</dimen><dimen name="notification_subtext_size">13sp</dimen><dimen name="notification_top_pad">10dp</dimen><dimen name="notification_top_pad_large_text">5dp</dimen><drawable name="notification_template_icon_bg">#3333B5E5</drawable><drawable name="notification_template_icon_low_bg">#0cffffff</drawable><item name="line1" type="id"/><item name="line3" type="id"/><item name="tag_transition_group" type="id"/><item name="text" type="id"/><item name="text2" type="id"/><item name="title" type="id"/><integer name="status_bar_notification_info_maxnum">999</integer><string name="status_bar_notification_info_overflow">999+</string><style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent"/><style name="TextAppearance.Compat.Notification.Info">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style><style name="TextAppearance.Compat.Notification.Line2" parent="TextAppearance.Compat.Notification.Info"/><style name="TextAppearance.Compat.Notification.Time">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style><style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title"/><style name="Widget.Compat.NotificationActionContainer" parent=""/><style name="Widget.Compat.NotificationActionText" parent=""/></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-af\values-af.xml" qualifiers="af"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-am\values-am.xml" qualifiers="am"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-ar\values-ar.xml" qualifiers="ar"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"+999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-az\values-az.xml" qualifiers="az"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-b+sr+Latn\values-b+sr+Latn.xml" qualifiers="b+sr+Latn"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">">999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-be\values-be.xml" qualifiers="be"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"больш за 999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-bg\values-bg.xml" qualifiers="bg"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-bn\values-bn.xml" qualifiers="bn"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"৯৯৯+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-bs\values-bs.xml" qualifiers="bs"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">">999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-ca\values-ca.xml" qualifiers="ca"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"+999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-cs\values-cs.xml" qualifiers="cs"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-da\values-da.xml" qualifiers="da"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-de\values-de.xml" qualifiers="de"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-el\values-el.xml" qualifiers="el"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-en-rAU\values-en-rAU.xml" qualifiers="en-rAU"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-en-rGB\values-en-rGB.xml" qualifiers="en-rGB"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-en-rIN\values-en-rIN.xml" qualifiers="en-rIN"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-es\values-es.xml" qualifiers="es"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"+999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-es-rUS\values-es-rUS.xml" qualifiers="es-rUS"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-et\values-et.xml" qualifiers="et"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-eu\values-eu.xml" qualifiers="eu"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-fa\values-fa.xml" qualifiers="fa"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"۹۹۹+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-fi\values-fi.xml" qualifiers="fi"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-fr\values-fr.xml" qualifiers="fr"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">">999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-fr-rCA\values-fr-rCA.xml" qualifiers="fr-rCA"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">">999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-gl\values-gl.xml" qualifiers="gl"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">">999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-gu\values-gu.xml" qualifiers="gu"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-hi\values-hi.xml" qualifiers="hi"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-hr\values-hr.xml" qualifiers="hr"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-hu\values-hu.xml" qualifiers="hu"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-hy\values-hy.xml" qualifiers="hy"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-in\values-in.xml" qualifiers="in"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-is\values-is.xml" qualifiers="is"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-it\values-it.xml" qualifiers="it"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-iw\values-iw.xml" qualifiers="iw"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"‎999+‎"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-ja\values-ja.xml" qualifiers="ja"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-ka\values-ka.xml" qualifiers="ka"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-kk\values-kk.xml" qualifiers="kk"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-km\values-km.xml" qualifiers="km"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-kn\values-kn.xml" qualifiers="kn"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-ko\values-ko.xml" qualifiers="ko"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-ky\values-ky.xml" qualifiers="ky"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-lo\values-lo.xml" qualifiers="lo"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-lt\values-lt.xml" qualifiers="lt"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-lv\values-lv.xml" qualifiers="lv"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-mk\values-mk.xml" qualifiers="mk"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-ml\values-ml.xml" qualifiers="ml"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-mn\values-mn.xml" qualifiers="mn"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-mr\values-mr.xml" qualifiers="mr"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-ms\values-ms.xml" qualifiers="ms"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-my\values-my.xml" qualifiers="my"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"၉၉၉+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-nb\values-nb.xml" qualifiers="nb"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-ne\values-ne.xml" qualifiers="ne"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"९९९+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-nl\values-nl.xml" qualifiers="nl"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-pa\values-pa.xml" qualifiers="pa"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-pl\values-pl.xml" qualifiers="pl"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-port\values-port.xml" qualifiers="port"><bool name="abc_action_bar_embed_tabs">false</bool></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-pt\values-pt.xml" qualifiers="pt"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-pt-rBR\values-pt-rBR.xml" qualifiers="pt-rBR"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-pt-rPT\values-pt-rPT.xml" qualifiers="pt-rPT"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"+999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-ro\values-ro.xml" qualifiers="ro"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"˃999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-ru\values-ru.xml" qualifiers="ru"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">">999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-si\values-si.xml" qualifiers="si"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-sk\values-sk.xml" qualifiers="sk"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-sl\values-sl.xml" qualifiers="sl"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-sq\values-sq.xml" qualifiers="sq"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-sr\values-sr.xml" qualifiers="sr"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">">999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-sv\values-sv.xml" qualifiers="sv"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">">999"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-sw\values-sw.xml" qualifiers="sw"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-ta\values-ta.xml" qualifiers="ta"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-te\values-te.xml" qualifiers="te"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-th\values-th.xml" qualifiers="th"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-tl\values-tl.xml" qualifiers="tl"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-tr\values-tr.xml" qualifiers="tr"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-uk\values-uk.xml" qualifiers="uk"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-ur\values-ur.xml" qualifiers="ur"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"‎999+‎"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-uz\values-uz.xml" qualifiers="uz"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-v16\values-v16.xml" qualifiers="v16"><dimen name="notification_right_side_padding_top">4dp</dimen></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-v21\values-v21.xml" qualifiers="v21"><color name="notification_action_color_filter">@color/secondary_text_default_material_light</color><dimen name="notification_content_margin_start">0dp</dimen><dimen name="notification_main_column_padding_top">0dp</dimen><dimen name="notification_media_narrow_margin">12dp</dimen><style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.Material.Notification"/><style name="TextAppearance.Compat.Notification.Info" parent="@android:style/TextAppearance.Material.Notification.Info"/><style name="TextAppearance.Compat.Notification.Time" parent="@android:style/TextAppearance.Material.Notification.Time"/><style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.Material.Notification.Title"/><style name="Widget.Compat.NotificationActionContainer" parent="">
        <item name="android:background">@drawable/notification_action_background</item>
    </style><style name="Widget.Compat.NotificationActionText" parent="">
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:textColor">@color/secondary_text_default_material_light</item>
        <item name="android:textSize">@dimen/notification_action_text_size</item>
    </style></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-vi\values-vi.xml" qualifiers="vi"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-zh-rCN\values-zh-rCN.xml" qualifiers="zh-rCN"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-zh-rHK\values-zh-rHK.xml" qualifiers="zh-rHK"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999 +"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-zh-rTW\values-zh-rTW.xml" qualifiers="zh-rTW"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\812a19fc1a353943829c2538f5539f5e\transformed\support-compat-27.0.2\res\values-zu\values-zu.xml" qualifiers="zu"><string msgid="2869576371154716097" name="status_bar_notification_info_overflow">"999+"</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="com.android.support.constraint:constraint-layout:1.0.2$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\00e405b0cf73f315c97110fd1c7d2138\transformed\constraint-layout-1.0.2\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="com.android.support.constraint:constraint-layout:1.0.2" from-dependency="true" generated-set="com.android.support.constraint:constraint-layout:1.0.2$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\00e405b0cf73f315c97110fd1c7d2138\transformed\constraint-layout-1.0.2\res"><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\00e405b0cf73f315c97110fd1c7d2138\transformed\constraint-layout-1.0.2\res\values\values.xml" qualifiers=""><attr format="reference" name="constraintSet"/><attr format="integer" name="layout_constraintBaseline_creator"/><attr format="reference|enum" name="layout_constraintBaseline_toBaselineOf">
        <enum name="parent" value="0"/>
    </attr><attr format="integer" name="layout_constraintBottom_creator"/><attr format="reference|enum" name="layout_constraintBottom_toBottomOf">
        <enum name="parent" value="0"/>
    </attr><attr format="reference|enum" name="layout_constraintBottom_toTopOf">
        <enum name="parent" value="0"/>
    </attr><attr format="string" name="layout_constraintDimensionRatio"/><attr format="reference|enum" name="layout_constraintEnd_toEndOf">
        <enum name="parent" value="0"/>
    </attr><attr format="reference|enum" name="layout_constraintEnd_toStartOf">
        <enum name="parent" value="0"/>
    </attr><attr format="dimension" name="layout_constraintGuide_begin"/><attr format="dimension" name="layout_constraintGuide_end"/><attr format="float" name="layout_constraintGuide_percent"/><attr name="layout_constraintHeight_default">
        <enum name="spread" value="0"/>
        <enum name="wrap" value="1"/>
    </attr><attr format="dimension" name="layout_constraintHeight_max"/><attr format="dimension" name="layout_constraintHeight_min"/><attr format="float" name="layout_constraintHorizontal_bias"/><attr format="enum" name="layout_constraintHorizontal_chainStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr><attr format="float" name="layout_constraintHorizontal_weight"/><attr format="integer" name="layout_constraintLeft_creator"/><attr format="reference|enum" name="layout_constraintLeft_toLeftOf">
        <enum name="parent" value="0"/>
    </attr><attr format="reference|enum" name="layout_constraintLeft_toRightOf">
        <enum name="parent" value="0"/>
    </attr><attr format="integer" name="layout_constraintRight_creator"/><attr format="reference|enum" name="layout_constraintRight_toLeftOf">
        <enum name="parent" value="0"/>
    </attr><attr format="reference|enum" name="layout_constraintRight_toRightOf">
        <enum name="parent" value="0"/>
    </attr><attr format="reference|enum" name="layout_constraintStart_toEndOf">
        <enum name="parent" value="0"/>
    </attr><attr format="reference|enum" name="layout_constraintStart_toStartOf">
        <enum name="parent" value="0"/>
    </attr><attr format="integer" name="layout_constraintTop_creator"/><attr format="reference|enum" name="layout_constraintTop_toBottomOf">
        <enum name="parent" value="0"/>
    </attr><attr format="reference|enum" name="layout_constraintTop_toTopOf">
        <enum name="parent" value="0"/>
    </attr><attr format="float" name="layout_constraintVertical_bias"/><attr format="enum" name="layout_constraintVertical_chainStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr><attr format="float" name="layout_constraintVertical_weight"/><attr name="layout_constraintWidth_default">
        <enum name="spread" value="0"/>
        <enum name="wrap" value="1"/>
    </attr><attr format="dimension" name="layout_constraintWidth_max"/><attr format="dimension" name="layout_constraintWidth_min"/><attr format="dimension" name="layout_editor_absoluteX"/><attr format="dimension" name="layout_editor_absoluteY"/><attr format="dimension" name="layout_goneMarginBottom"/><attr format="dimension" name="layout_goneMarginEnd"/><attr format="dimension" name="layout_goneMarginLeft"/><attr format="dimension" name="layout_goneMarginRight"/><attr format="dimension" name="layout_goneMarginStart"/><attr format="dimension" name="layout_goneMarginTop"/><attr name="layout_optimizationLevel">
        <flag name="none" value="1"/>
        <flag name="all" value="2"/>
        <flag name="basic" value="4"/>
        <flag name="chains" value="8"/>
    </attr><declare-styleable name="ConstraintLayout_Layout"><attr name="android:orientation"/><attr name="android:minWidth"/><attr name="android:minHeight"/><attr name="android:maxWidth"/><attr name="android:maxHeight"/><attr name="layout_optimizationLevel"/><attr name="constraintSet"/><attr name="layout_constraintGuide_begin"/><attr name="layout_constraintGuide_end"/><attr name="layout_constraintGuide_percent"/><attr name="layout_constraintLeft_toLeftOf"/><attr name="layout_constraintLeft_toRightOf"/><attr name="layout_constraintRight_toLeftOf"/><attr name="layout_constraintRight_toRightOf"/><attr name="layout_constraintTop_toTopOf"/><attr name="layout_constraintTop_toBottomOf"/><attr name="layout_constraintBottom_toTopOf"/><attr name="layout_constraintBottom_toBottomOf"/><attr name="layout_constraintBaseline_toBaselineOf"/><attr name="layout_constraintStart_toEndOf"/><attr name="layout_constraintStart_toStartOf"/><attr name="layout_constraintEnd_toStartOf"/><attr name="layout_constraintEnd_toEndOf"/><attr name="layout_goneMarginLeft"/><attr name="layout_goneMarginTop"/><attr name="layout_goneMarginRight"/><attr name="layout_goneMarginBottom"/><attr name="layout_goneMarginStart"/><attr name="layout_goneMarginEnd"/><attr name="layout_constraintHorizontal_bias"/><attr name="layout_constraintVertical_bias"/><attr name="layout_constraintWidth_default"/><attr name="layout_constraintHeight_default"/><attr name="layout_constraintWidth_min"/><attr name="layout_constraintWidth_max"/><attr name="layout_constraintHeight_min"/><attr name="layout_constraintHeight_max"/><attr name="layout_constraintLeft_creator"/><attr name="layout_constraintTop_creator"/><attr name="layout_constraintRight_creator"/><attr name="layout_constraintBottom_creator"/><attr name="layout_constraintBaseline_creator"/><attr name="layout_constraintDimensionRatio"/><attr name="layout_constraintHorizontal_weight"/><attr name="layout_constraintVertical_weight"/><attr name="layout_constraintHorizontal_chainStyle"/><attr name="layout_constraintVertical_chainStyle"/><attr name="layout_editor_absoluteX"/><attr name="layout_editor_absoluteY"/></declare-styleable><declare-styleable name="ConstraintSet"><attr name="android:orientation"/><attr name="android:id"/><attr name="android:visibility"/><attr name="android:alpha"/><attr name="android:elevation"/><attr name="android:rotationX"/><attr name="android:rotationY"/><attr name="android:scaleX"/><attr name="android:scaleY"/><attr name="android:transformPivotX"/><attr name="android:transformPivotY"/><attr name="android:translationX"/><attr name="android:translationY"/><attr name="android:translationZ"/><attr name="android:layout_width"/><attr name="android:layout_height"/><attr name="android:layout_marginStart"/><attr name="android:layout_marginBottom"/><attr name="android:layout_marginTop"/><attr name="android:layout_marginEnd"/><attr name="android:layout_marginLeft"/><attr name="android:layout_marginRight"/><attr name="layout_constraintGuide_begin"/><attr name="layout_constraintGuide_end"/><attr name="layout_constraintGuide_percent"/><attr name="layout_constraintLeft_toLeftOf"/><attr name="layout_constraintLeft_toRightOf"/><attr name="layout_constraintRight_toLeftOf"/><attr name="layout_constraintRight_toRightOf"/><attr name="layout_constraintTop_toTopOf"/><attr name="layout_constraintTop_toBottomOf"/><attr name="layout_constraintBottom_toTopOf"/><attr name="layout_constraintBottom_toBottomOf"/><attr name="layout_constraintBaseline_toBaselineOf"/><attr name="layout_constraintStart_toEndOf"/><attr name="layout_constraintStart_toStartOf"/><attr name="layout_constraintEnd_toStartOf"/><attr name="layout_constraintEnd_toEndOf"/><attr name="layout_goneMarginLeft"/><attr name="layout_goneMarginTop"/><attr name="layout_goneMarginRight"/><attr name="layout_goneMarginBottom"/><attr name="layout_goneMarginStart"/><attr name="layout_goneMarginEnd"/><attr name="layout_constraintHorizontal_bias"/><attr name="layout_constraintVertical_bias"/><attr name="layout_constraintWidth_default"/><attr name="layout_constraintHeight_default"/><attr name="layout_constraintWidth_min"/><attr name="layout_constraintWidth_max"/><attr name="layout_constraintHeight_min"/><attr name="layout_constraintHeight_max"/><attr name="layout_constraintLeft_creator"/><attr name="layout_constraintTop_creator"/><attr name="layout_constraintRight_creator"/><attr name="layout_constraintBottom_creator"/><attr name="layout_constraintBaseline_creator"/><attr name="layout_constraintDimensionRatio"/><attr name="layout_constraintHorizontal_weight"/><attr name="layout_constraintVertical_weight"/><attr name="layout_constraintHorizontal_chainStyle"/><attr name="layout_constraintVertical_chainStyle"/><attr name="layout_editor_absoluteX"/><attr name="layout_editor_absoluteY"/></declare-styleable><declare-styleable name="LinearConstraintLayout"><attr name="android:orientation"/></declare-styleable></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="com.android.support:appcompat-v7:27.0.2$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="com.android.support:appcompat-v7:27.0.2" from-dependency="true" generated-set="com.android.support:appcompat-v7:27.0.2$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res"><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values\values.xml" qualifiers=""><attr format="reference" name="drawerArrowStyle"/><attr format="dimension" name="height"/><attr format="boolean" name="isLightTheme"/><attr format="string" name="title"/><bool name="abc_action_bar_embed_tabs">true</bool><bool name="abc_allow_stacked_button_bar">true</bool><bool name="abc_config_actionMenuItemAllCaps">true</bool><bool name="abc_config_closeDialogWhenTouchOutside">true</bool><bool name="abc_config_showMenuShortcutsWhenKeyboardPresent">false</bool><color name="abc_input_method_navigation_guard">@android:color/black</color><color name="abc_search_url_text_normal">#7fa87f</color><color name="abc_search_url_text_pressed">@android:color/black</color><color name="abc_search_url_text_selected">@android:color/black</color><color name="accent_material_dark">@color/material_deep_teal_200</color><color name="accent_material_light">@color/material_deep_teal_500</color><color name="background_floating_material_dark">@color/material_grey_800</color><color name="background_floating_material_light">@android:color/white</color><color name="background_material_dark">@color/material_grey_850</color><color name="background_material_light">@color/material_grey_50</color><color name="bright_foreground_disabled_material_dark">#80ffffff</color><color name="bright_foreground_disabled_material_light">#80000000</color><color name="bright_foreground_inverse_material_dark">@color/bright_foreground_material_light</color><color name="bright_foreground_inverse_material_light">@color/bright_foreground_material_dark</color><color name="bright_foreground_material_dark">@android:color/white</color><color name="bright_foreground_material_light">@android:color/black</color><color name="button_material_dark">#ff5a595b</color><color name="button_material_light">#ffd6d7d7</color><color name="dim_foreground_disabled_material_dark">#80bebebe</color><color name="dim_foreground_disabled_material_light">#80323232</color><color name="dim_foreground_material_dark">#ffbebebe</color><color name="dim_foreground_material_light">#ff323232</color><color name="error_color_material">#F4511E</color><color name="foreground_material_dark">@android:color/white</color><color name="foreground_material_light">@android:color/black</color><color name="highlighted_text_material_dark">#6680cbc4</color><color name="highlighted_text_material_light">#66009688</color><color name="material_blue_grey_800">#ff37474f</color><color name="material_blue_grey_900">#ff263238</color><color name="material_blue_grey_950">#ff21272b</color><color name="material_deep_teal_200">#ff80cbc4</color><color name="material_deep_teal_500">#ff009688</color><color name="material_grey_100">#fff5f5f5</color><color name="material_grey_300">#ffe0e0e0</color><color name="material_grey_50">#fffafafa</color><color name="material_grey_600">#ff757575</color><color name="material_grey_800">#ff424242</color><color name="material_grey_850">#ff303030</color><color name="material_grey_900">#ff212121</color><color name="primary_dark_material_dark">@android:color/black</color><color name="primary_dark_material_light">@color/material_grey_600</color><color name="primary_material_dark">@color/material_grey_900</color><color name="primary_material_light">@color/material_grey_100</color><color name="primary_text_default_material_dark">#ffffffff</color><color name="primary_text_default_material_light">#de000000</color><color name="primary_text_disabled_material_dark">#4Dffffff</color><color name="primary_text_disabled_material_light">#39000000</color><color name="ripple_material_dark">#33ffffff</color><color name="ripple_material_light">#1f000000</color><color name="secondary_text_default_material_dark">#b3ffffff</color><color name="secondary_text_default_material_light">#8a000000</color><color name="secondary_text_disabled_material_dark">#36ffffff</color><color name="secondary_text_disabled_material_light">#24000000</color><color name="switch_thumb_disabled_material_dark">#ff616161</color><color name="switch_thumb_disabled_material_light">#ffbdbdbd</color><color name="switch_thumb_normal_material_dark">#ffbdbdbd</color><color name="switch_thumb_normal_material_light">#fff1f1f1</color><color name="tooltip_background_dark">#e6616161</color><color name="tooltip_background_light">#e6FFFFFF</color><declare-styleable name="ActionBar"><attr name="navigationMode">
            
            <enum name="normal" value="0"/>
            
            <enum name="listMode" value="1"/>
            
            <enum name="tabMode" value="2"/>
        </attr><attr name="displayOptions">
            <flag name="none" value="0"/>
            <flag name="useLogo" value="0x1"/>
            <flag name="showHome" value="0x2"/>
            <flag name="homeAsUp" value="0x4"/>
            <flag name="showTitle" value="0x8"/>
            <flag name="showCustom" value="0x10"/>
            <flag name="disableHome" value="0x20"/>
        </attr><attr name="title"/><attr format="string" name="subtitle"/><attr format="reference" name="titleTextStyle"/><attr format="reference" name="subtitleTextStyle"/><attr format="reference" name="icon"/><attr format="reference" name="logo"/><attr format="reference" name="divider"/><attr format="reference" name="background"/><attr format="reference|color" name="backgroundStacked"/><attr format="reference|color" name="backgroundSplit"/><attr format="reference" name="customNavigationLayout"/><attr name="height"/><attr format="reference" name="homeLayout"/><attr format="reference" name="progressBarStyle"/><attr format="reference" name="indeterminateProgressStyle"/><attr format="dimension" name="progressBarPadding"/><attr name="homeAsUpIndicator"/><attr format="dimension" name="itemPadding"/><attr format="boolean" name="hideOnContentScroll"/><attr format="dimension" name="contentInsetStart"/><attr format="dimension" name="contentInsetEnd"/><attr format="dimension" name="contentInsetLeft"/><attr format="dimension" name="contentInsetRight"/><attr format="dimension" name="contentInsetStartWithNavigation"/><attr format="dimension" name="contentInsetEndWithActions"/><attr format="dimension" name="elevation"/><attr format="reference" name="popupTheme"/></declare-styleable><declare-styleable name="ActionBarLayout"><attr name="android:layout_gravity"/></declare-styleable><declare-styleable name="ActionMenuItemView"><attr name="android:minWidth"/></declare-styleable><declare-styleable name="ActionMenuView"/><declare-styleable name="ActionMode"><attr name="titleTextStyle"/><attr name="subtitleTextStyle"/><attr name="background"/><attr name="backgroundSplit"/><attr name="height"/><attr format="reference" name="closeItemLayout"/></declare-styleable><declare-styleable name="ActivityChooserView"><attr format="string" name="initialActivityCount"/><attr format="reference" name="expandActivityOverflowButtonDrawable"/></declare-styleable><declare-styleable name="AlertDialog"><attr name="android:layout"/><attr format="reference" name="buttonPanelSideLayout"/><attr format="reference" name="listLayout"/><attr format="reference" name="multiChoiceItemLayout"/><attr format="reference" name="singleChoiceItemLayout"/><attr format="reference" name="listItemLayout"/><attr format="boolean" name="showTitle"/></declare-styleable><declare-styleable name="AppCompatImageView"><attr name="android:src"/><attr format="reference" name="srcCompat"/><attr format="color" name="tint"/><attr name="tintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr></declare-styleable><declare-styleable name="AppCompatSeekBar"><attr name="android:thumb"/><attr format="reference" name="tickMark"/><attr format="color" name="tickMarkTint"/><attr name="tickMarkTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr></declare-styleable><declare-styleable name="AppCompatTextHelper"><attr name="android:drawableLeft"/><attr name="android:drawableTop"/><attr name="android:drawableRight"/><attr name="android:drawableBottom"/><attr name="android:drawableStart"/><attr name="android:drawableEnd"/><attr name="android:textAppearance"/></declare-styleable><declare-styleable name="AppCompatTextView"><attr format="reference|boolean" name="textAllCaps"/><attr name="android:textAppearance"/><attr format="enum" name="autoSizeTextType">
            
            <enum name="none" value="0"/>
            
            <enum name="uniform" value="1"/>
        </attr><attr format="dimension" name="autoSizeStepGranularity"/><attr format="reference" name="autoSizePresetSizes"/><attr format="dimension" name="autoSizeMinTextSize"/><attr format="dimension" name="autoSizeMaxTextSize"/><attr format="string" name="fontFamily"/></declare-styleable><declare-styleable name="AppCompatTheme"><attr format="boolean" name="windowActionBar"/><attr format="boolean" name="windowNoTitle"/><attr format="boolean" name="windowActionBarOverlay"/><attr format="boolean" name="windowActionModeOverlay"/><attr format="dimension|fraction" name="windowFixedWidthMajor"/><attr format="dimension|fraction" name="windowFixedHeightMinor"/><attr format="dimension|fraction" name="windowFixedWidthMinor"/><attr format="dimension|fraction" name="windowFixedHeightMajor"/><attr format="dimension|fraction" name="windowMinWidthMajor"/><attr format="dimension|fraction" name="windowMinWidthMinor"/><attr name="android:windowIsFloating"/><attr name="android:windowAnimationStyle"/><attr format="reference" name="actionBarTabStyle"/><attr format="reference" name="actionBarTabBarStyle"/><attr format="reference" name="actionBarTabTextStyle"/><attr format="reference" name="actionOverflowButtonStyle"/><attr format="reference" name="actionOverflowMenuStyle"/><attr format="reference" name="actionBarPopupTheme"/><attr format="reference" name="actionBarStyle"/><attr format="reference" name="actionBarSplitStyle"/><attr format="reference" name="actionBarTheme"/><attr format="reference" name="actionBarWidgetTheme"/><attr format="dimension" name="actionBarSize">
            <enum name="wrap_content" value="0"/>
        </attr><attr format="reference" name="actionBarDivider"/><attr format="reference" name="actionBarItemBackground"/><attr format="reference" name="actionMenuTextAppearance"/><attr format="color|reference" name="actionMenuTextColor"/><attr format="reference" name="actionModeStyle"/><attr format="reference" name="actionModeCloseButtonStyle"/><attr format="reference" name="actionModeBackground"/><attr format="reference" name="actionModeSplitBackground"/><attr format="reference" name="actionModeCloseDrawable"/><attr format="reference" name="actionModeCutDrawable"/><attr format="reference" name="actionModeCopyDrawable"/><attr format="reference" name="actionModePasteDrawable"/><attr format="reference" name="actionModeSelectAllDrawable"/><attr format="reference" name="actionModeShareDrawable"/><attr format="reference" name="actionModeFindDrawable"/><attr format="reference" name="actionModeWebSearchDrawable"/><attr format="reference" name="actionModePopupWindowStyle"/><attr format="reference" name="textAppearanceLargePopupMenu"/><attr format="reference" name="textAppearanceSmallPopupMenu"/><attr format="reference" name="textAppearancePopupMenuHeader"/><attr format="reference" name="dialogTheme"/><attr format="dimension" name="dialogPreferredPadding"/><attr format="reference" name="listDividerAlertDialog"/><attr format="reference" name="actionDropDownStyle"/><attr format="dimension" name="dropdownListPreferredItemHeight"/><attr format="reference" name="spinnerDropDownItemStyle"/><attr format="reference" name="homeAsUpIndicator"/><attr format="reference" name="actionButtonStyle"/><attr format="reference" name="buttonBarStyle"/><attr format="reference" name="buttonBarButtonStyle"/><attr format="reference" name="selectableItemBackground"/><attr format="reference" name="selectableItemBackgroundBorderless"/><attr format="reference" name="borderlessButtonStyle"/><attr format="reference" name="dividerVertical"/><attr format="reference" name="dividerHorizontal"/><attr format="reference" name="activityChooserViewStyle"/><attr format="reference" name="toolbarStyle"/><attr format="reference" name="toolbarNavigationButtonStyle"/><attr format="reference" name="popupMenuStyle"/><attr format="reference" name="popupWindowStyle"/><attr format="reference|color" name="editTextColor"/><attr format="reference" name="editTextBackground"/><attr format="reference" name="imageButtonStyle"/><attr format="reference" name="textAppearanceSearchResultTitle"/><attr format="reference" name="textAppearanceSearchResultSubtitle"/><attr format="reference|color" name="textColorSearchUrl"/><attr format="reference" name="searchViewStyle"/><attr format="dimension" name="listPreferredItemHeight"/><attr format="dimension" name="listPreferredItemHeightSmall"/><attr format="dimension" name="listPreferredItemHeightLarge"/><attr format="dimension" name="listPreferredItemPaddingLeft"/><attr format="dimension" name="listPreferredItemPaddingRight"/><attr format="reference" name="dropDownListViewStyle"/><attr format="reference" name="listPopupWindowStyle"/><attr format="reference" name="textAppearanceListItem"/><attr format="reference" name="textAppearanceListItemSecondary"/><attr format="reference" name="textAppearanceListItemSmall"/><attr format="reference" name="panelBackground"/><attr format="dimension" name="panelMenuListWidth"/><attr format="reference" name="panelMenuListTheme"/><attr format="reference" name="listChoiceBackgroundIndicator"/><attr format="color" name="colorPrimary"/><attr format="color" name="colorPrimaryDark"/><attr format="color" name="colorAccent"/><attr format="color" name="colorControlNormal"/><attr format="color" name="colorControlActivated"/><attr format="color" name="colorControlHighlight"/><attr format="color" name="colorButtonNormal"/><attr format="color" name="colorSwitchThumbNormal"/><attr format="reference" name="controlBackground"/><attr format="color" name="colorBackgroundFloating"/><attr format="reference" name="alertDialogStyle"/><attr format="reference" name="alertDialogButtonGroupStyle"/><attr format="boolean" name="alertDialogCenterButtons"/><attr format="reference" name="alertDialogTheme"/><attr format="reference|color" name="textColorAlertDialogListItem"/><attr format="reference" name="buttonBarPositiveButtonStyle"/><attr format="reference" name="buttonBarNegativeButtonStyle"/><attr format="reference" name="buttonBarNeutralButtonStyle"/><attr format="reference" name="autoCompleteTextViewStyle"/><attr format="reference" name="buttonStyle"/><attr format="reference" name="buttonStyleSmall"/><attr format="reference" name="checkboxStyle"/><attr format="reference" name="checkedTextViewStyle"/><attr format="reference" name="editTextStyle"/><attr format="reference" name="radioButtonStyle"/><attr format="reference" name="ratingBarStyle"/><attr format="reference" name="ratingBarStyleIndicator"/><attr format="reference" name="ratingBarStyleSmall"/><attr format="reference" name="seekBarStyle"/><attr format="reference" name="spinnerStyle"/><attr format="reference" name="switchStyle"/><attr format="reference" name="listMenuViewStyle"/><attr format="reference" name="tooltipFrameBackground"/><attr format="reference|color" name="tooltipForegroundColor"/><attr format="reference|color" name="colorError"/></declare-styleable><declare-styleable name="ButtonBarLayout"><attr format="boolean" name="allowStacking"/></declare-styleable><declare-styleable name="ColorStateListItem"><attr name="android:color"/><attr format="float" name="alpha"/><attr name="android:alpha"/></declare-styleable><declare-styleable name="CompoundButton"><attr name="android:button"/><attr format="color" name="buttonTint"/><attr name="buttonTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr></declare-styleable><declare-styleable name="DrawerArrowToggle"><attr format="color" name="color"/><attr format="boolean" name="spinBars"/><attr format="dimension" name="drawableSize"/><attr format="dimension" name="gapBetweenBars"/><attr format="dimension" name="arrowHeadLength"/><attr format="dimension" name="arrowShaftLength"/><attr format="dimension" name="barLength"/><attr format="dimension" name="thickness"/></declare-styleable><declare-styleable name="LinearLayoutCompat"><attr name="android:orientation"/><attr name="android:gravity"/><attr name="android:baselineAligned"/><attr name="android:baselineAlignedChildIndex"/><attr name="android:weightSum"/><attr format="boolean" name="measureWithLargestChild"/><attr name="divider"/><attr name="showDividers">
            <flag name="none" value="0"/>
            <flag name="beginning" value="1"/>
            <flag name="middle" value="2"/>
            <flag name="end" value="4"/>
        </attr><attr format="dimension" name="dividerPadding"/></declare-styleable><declare-styleable name="LinearLayoutCompat_Layout"><attr name="android:layout_width"/><attr name="android:layout_height"/><attr name="android:layout_weight"/><attr name="android:layout_gravity"/></declare-styleable><declare-styleable name="ListPopupWindow"><attr name="android:dropDownVerticalOffset"/><attr name="android:dropDownHorizontalOffset"/></declare-styleable><declare-styleable name="MenuGroup"><attr name="android:id"/><attr name="android:menuCategory"/><attr name="android:orderInCategory"/><attr name="android:checkableBehavior"/><attr name="android:visible"/><attr name="android:enabled"/></declare-styleable><declare-styleable name="MenuItem"><attr name="android:id"/><attr name="android:menuCategory"/><attr name="android:orderInCategory"/><attr name="android:title"/><attr name="android:titleCondensed"/><attr name="android:icon"/><attr name="android:alphabeticShortcut"/><attr name="alphabeticModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr><attr name="android:numericShortcut"/><attr name="numericModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr><attr name="android:checkable"/><attr name="android:checked"/><attr name="android:visible"/><attr name="android:enabled"/><attr name="android:onClick"/><attr name="showAsAction">
            
            <flag name="never" value="0"/>
            
            <flag name="ifRoom" value="1"/>
            
            <flag name="always" value="2"/>
            
            <flag name="withText" value="4"/>
            
            <flag name="collapseActionView" value="8"/>
        </attr><attr format="reference" name="actionLayout"/><attr format="string" name="actionViewClass"/><attr format="string" name="actionProviderClass"/><attr format="string" name="contentDescription"/><attr format="string" name="tooltipText"/><attr format="color" name="iconTint"/><attr name="iconTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr></declare-styleable><declare-styleable name="MenuView"><attr name="android:itemTextAppearance"/><attr name="android:horizontalDivider"/><attr name="android:verticalDivider"/><attr name="android:headerBackground"/><attr name="android:itemBackground"/><attr name="android:windowAnimationStyle"/><attr name="android:itemIconDisabledAlpha"/><attr format="boolean" name="preserveIconSpacing"/><attr format="reference" name="subMenuArrow"/></declare-styleable><declare-styleable name="PopupWindow"><attr format="boolean" name="overlapAnchor"/><attr name="android:popupBackground"/><attr name="android:popupAnimationStyle"/></declare-styleable><declare-styleable name="PopupWindowBackgroundState"><attr format="boolean" name="state_above_anchor"/></declare-styleable><declare-styleable name="RecycleListView"><attr format="dimension" name="paddingBottomNoButtons"/><attr format="dimension" name="paddingTopNoTitle"/></declare-styleable><declare-styleable name="SearchView"><attr format="reference" name="layout"/><attr format="boolean" name="iconifiedByDefault"/><attr name="android:maxWidth"/><attr format="string" name="queryHint"/><attr format="string" name="defaultQueryHint"/><attr name="android:imeOptions"/><attr name="android:inputType"/><attr format="reference" name="closeIcon"/><attr format="reference" name="goIcon"/><attr format="reference" name="searchIcon"/><attr format="reference" name="searchHintIcon"/><attr format="reference" name="voiceIcon"/><attr format="reference" name="commitIcon"/><attr format="reference" name="suggestionRowLayout"/><attr format="reference" name="queryBackground"/><attr format="reference" name="submitBackground"/><attr name="android:focusable"/></declare-styleable><declare-styleable name="Spinner"><attr name="android:prompt"/><attr name="popupTheme"/><attr name="android:popupBackground"/><attr name="android:dropDownWidth"/><attr name="android:entries"/></declare-styleable><declare-styleable name="SwitchCompat"><attr name="android:thumb"/><attr format="color" name="thumbTint"/><attr name="thumbTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr><attr format="reference" name="track"/><attr format="color" name="trackTint"/><attr name="trackTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr><attr name="android:textOn"/><attr name="android:textOff"/><attr format="dimension" name="thumbTextPadding"/><attr format="reference" name="switchTextAppearance"/><attr format="dimension" name="switchMinWidth"/><attr format="dimension" name="switchPadding"/><attr format="boolean" name="splitTrack"/><attr format="boolean" name="showText"/></declare-styleable><declare-styleable name="TextAppearance"><attr name="android:textSize"/><attr name="android:textColor"/><attr name="android:textColorHint"/><attr name="android:textColorLink"/><attr name="android:textStyle"/><attr name="android:typeface"/><attr name="android:fontFamily"/><attr name="fontFamily"/><attr name="textAllCaps"/><attr name="android:shadowColor"/><attr name="android:shadowDy"/><attr name="android:shadowDx"/><attr name="android:shadowRadius"/></declare-styleable><declare-styleable name="Toolbar"><attr format="reference" name="titleTextAppearance"/><attr format="reference" name="subtitleTextAppearance"/><attr name="title"/><attr name="subtitle"/><attr name="android:gravity"/><attr format="dimension" name="titleMargin"/><attr format="dimension" name="titleMarginStart"/><attr format="dimension" name="titleMarginEnd"/><attr format="dimension" name="titleMarginTop"/><attr format="dimension" name="titleMarginBottom"/><attr format="dimension" name="titleMargins"/><attr name="contentInsetStart"/><attr name="contentInsetEnd"/><attr name="contentInsetLeft"/><attr name="contentInsetRight"/><attr name="contentInsetStartWithNavigation"/><attr name="contentInsetEndWithActions"/><attr format="dimension" name="maxButtonHeight"/><attr name="buttonGravity">
            
            <flag name="top" value="0x30"/>
            
            <flag name="bottom" value="0x50"/>
        </attr><attr format="reference" name="collapseIcon"/><attr format="string" name="collapseContentDescription"/><attr name="popupTheme"/><attr format="reference" name="navigationIcon"/><attr format="string" name="navigationContentDescription"/><attr name="logo"/><attr format="string" name="logoDescription"/><attr format="color" name="titleTextColor"/><attr format="color" name="subtitleTextColor"/><attr name="android:minHeight"/></declare-styleable><declare-styleable name="View"><attr format="dimension" name="paddingStart"/><attr format="dimension" name="paddingEnd"/><attr name="android:focusable"/><attr format="reference" name="theme"/><attr name="android:theme"/></declare-styleable><declare-styleable name="ViewBackgroundHelper"><attr name="android:background"/><attr format="color" name="backgroundTint"/><attr name="backgroundTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr></declare-styleable><declare-styleable name="ViewStubCompat"><attr name="android:layout"/><attr name="android:inflatedId"/><attr name="android:id"/></declare-styleable><dimen name="abc_action_bar_content_inset_material">16dp</dimen><dimen name="abc_action_bar_content_inset_with_nav">72dp</dimen><dimen name="abc_action_bar_default_height_material">56dp</dimen><dimen name="abc_action_bar_default_padding_end_material">0dp</dimen><dimen name="abc_action_bar_default_padding_start_material">0dp</dimen><dimen name="abc_action_bar_elevation_material">4dp</dimen><dimen name="abc_action_bar_icon_vertical_padding_material">16dp</dimen><dimen name="abc_action_bar_overflow_padding_end_material">10dp</dimen><dimen name="abc_action_bar_overflow_padding_start_material">6dp</dimen><dimen name="abc_action_bar_progress_bar_size">40dp</dimen><dimen name="abc_action_bar_stacked_max_height">48dp</dimen><dimen name="abc_action_bar_stacked_tab_max_width">180dp</dimen><dimen name="abc_action_bar_subtitle_bottom_margin_material">5dp</dimen><dimen name="abc_action_bar_subtitle_top_margin_material">-3dp</dimen><dimen name="abc_action_button_min_height_material">48dp</dimen><dimen name="abc_action_button_min_width_material">48dp</dimen><dimen name="abc_action_button_min_width_overflow_material">36dp</dimen><dimen name="abc_alert_dialog_button_bar_height">48dp</dimen><dimen name="abc_button_inset_horizontal_material">@dimen/abc_control_inset_material</dimen><dimen name="abc_button_inset_vertical_material">6dp</dimen><dimen name="abc_button_padding_horizontal_material">8dp</dimen><dimen name="abc_button_padding_vertical_material">@dimen/abc_control_padding_material</dimen><dimen name="abc_cascading_menus_min_smallest_width">720dp</dimen><dimen name="abc_config_prefDialogWidth">320dp</dimen><dimen name="abc_control_corner_material">2dp</dimen><dimen name="abc_control_inset_material">4dp</dimen><dimen name="abc_control_padding_material">4dp</dimen><item name="abc_dialog_fixed_height_major" type="dimen">80%</item><item name="abc_dialog_fixed_height_minor" type="dimen">100%</item><item name="abc_dialog_fixed_width_major" type="dimen">320dp</item><item name="abc_dialog_fixed_width_minor" type="dimen">320dp</item><dimen name="abc_dialog_list_padding_bottom_no_buttons">8dp</dimen><dimen name="abc_dialog_list_padding_top_no_title">8dp</dimen><item name="abc_dialog_min_width_major" type="dimen">65%</item><item name="abc_dialog_min_width_minor" type="dimen">95%</item><dimen name="abc_dialog_padding_material">24dp</dimen><dimen name="abc_dialog_padding_top_material">18dp</dimen><dimen name="abc_dialog_title_divider_material">8dp</dimen><item format="float" name="abc_disabled_alpha_material_dark" type="dimen">0.30</item><item format="float" name="abc_disabled_alpha_material_light" type="dimen">0.26</item><dimen name="abc_dropdownitem_icon_width">32dip</dimen><dimen name="abc_dropdownitem_text_padding_left">8dip</dimen><dimen name="abc_dropdownitem_text_padding_right">8dip</dimen><dimen name="abc_edit_text_inset_bottom_material">7dp</dimen><dimen name="abc_edit_text_inset_horizontal_material">4dp</dimen><dimen name="abc_edit_text_inset_top_material">10dp</dimen><dimen name="abc_floating_window_z">16dp</dimen><dimen name="abc_list_item_padding_horizontal_material">@dimen/abc_action_bar_content_inset_material</dimen><dimen name="abc_panel_menu_list_width">296dp</dimen><dimen name="abc_progress_bar_height_material">4dp</dimen><dimen name="abc_search_view_preferred_height">48dip</dimen><dimen name="abc_search_view_preferred_width">320dip</dimen><dimen name="abc_seekbar_track_background_height_material">2dp</dimen><dimen name="abc_seekbar_track_progress_height_material">2dp</dimen><dimen name="abc_select_dialog_padding_start_material">20dp</dimen><dimen name="abc_switch_padding">3dp</dimen><dimen name="abc_text_size_body_1_material">14sp</dimen><dimen name="abc_text_size_body_2_material">14sp</dimen><dimen name="abc_text_size_button_material">14sp</dimen><dimen name="abc_text_size_caption_material">12sp</dimen><dimen name="abc_text_size_display_1_material">34sp</dimen><dimen name="abc_text_size_display_2_material">45sp</dimen><dimen name="abc_text_size_display_3_material">56sp</dimen><dimen name="abc_text_size_display_4_material">112sp</dimen><dimen name="abc_text_size_headline_material">24sp</dimen><dimen name="abc_text_size_large_material">22sp</dimen><dimen name="abc_text_size_medium_material">18sp</dimen><dimen name="abc_text_size_menu_header_material">14sp</dimen><dimen name="abc_text_size_menu_material">16sp</dimen><dimen name="abc_text_size_small_material">14sp</dimen><dimen name="abc_text_size_subhead_material">16sp</dimen><dimen name="abc_text_size_subtitle_material_toolbar">16dp</dimen><dimen name="abc_text_size_title_material">20sp</dimen><dimen name="abc_text_size_title_material_toolbar">20dp</dimen><item format="float" name="disabled_alpha_material_dark" type="dimen">0.30</item><item format="float" name="disabled_alpha_material_light" type="dimen">0.26</item><item format="float" name="highlight_alpha_material_colored" type="dimen">0.26</item><item format="float" name="highlight_alpha_material_dark" type="dimen">0.20</item><item format="float" name="highlight_alpha_material_light" type="dimen">0.12</item><item format="float" name="hint_alpha_material_dark" type="dimen">0.50</item><item format="float" name="hint_alpha_material_light" type="dimen">0.38</item><item format="float" name="hint_pressed_alpha_material_dark" type="dimen">0.70</item><item format="float" name="hint_pressed_alpha_material_light" type="dimen">0.54</item><dimen name="tooltip_corner_radius">2dp</dimen><dimen name="tooltip_horizontal_padding">16dp</dimen><dimen name="tooltip_margin">8dp</dimen><dimen name="tooltip_precise_anchor_extra_offset">8dp</dimen><dimen name="tooltip_precise_anchor_threshold">96dp</dimen><dimen name="tooltip_vertical_padding">6.5dp</dimen><dimen name="tooltip_y_offset_non_touch">0dp</dimen><dimen name="tooltip_y_offset_touch">16dp</dimen><item name="action_bar_activity_content" type="id"/><item name="action_bar_spinner" type="id"/><item name="action_menu_divider" type="id"/><item name="action_menu_presenter" type="id"/><item name="home" type="id"/><item name="progress_circular" type="id"/><item name="progress_horizontal" type="id"/><item name="split_action_bar" type="id"/><item name="up" type="id"/><integer name="abc_config_activityDefaultDur">220</integer><integer name="abc_config_activityShortDur">150</integer><integer name="cancel_button_image_alpha">127</integer><integer name="config_tooltipAnimTime">150</integer><string name="abc_action_bar_home_description">Navigate home</string><string name="abc_action_bar_up_description">Navigate up</string><string name="abc_action_menu_overflow_description">More options</string><string name="abc_action_mode_done">Done</string><string name="abc_activity_chooser_view_see_all">See all</string><string name="abc_activitychooserview_choose_application">Choose an app</string><string name="abc_capital_off">OFF</string><string name="abc_capital_on">ON</string><string name="abc_font_family_body_1_material">sans-serif</string><string name="abc_font_family_body_2_material">sans-serif-medium</string><string name="abc_font_family_button_material">sans-serif-medium</string><string name="abc_font_family_caption_material">sans-serif</string><string name="abc_font_family_display_1_material">sans-serif</string><string name="abc_font_family_display_2_material">sans-serif</string><string name="abc_font_family_display_3_material">sans-serif</string><string name="abc_font_family_display_4_material">sans-serif-light</string><string name="abc_font_family_headline_material">sans-serif</string><string name="abc_font_family_menu_material">sans-serif</string><string name="abc_font_family_subhead_material">sans-serif</string><string name="abc_font_family_title_material">sans-serif-medium</string><string name="abc_search_hint">Search…</string><string name="abc_searchview_description_clear">Clear query</string><string name="abc_searchview_description_query">Search query</string><string name="abc_searchview_description_search">Search</string><string name="abc_searchview_description_submit">Submit query</string><string name="abc_searchview_description_voice">Voice search</string><string name="abc_shareactionprovider_share_with">Share with</string><string name="abc_shareactionprovider_share_with_application">Share with <ns1:g example="Mail" id="application_name">%s</ns1:g></string><string name="abc_toolbar_collapse_description">Collapse</string><string name="search_menu_title">Search</string><style name="AlertDialog.AppCompat" parent="Base.AlertDialog.AppCompat"/><style name="AlertDialog.AppCompat.Light" parent="Base.AlertDialog.AppCompat.Light"/><style name="Animation.AppCompat.Dialog" parent="Base.Animation.AppCompat.Dialog"/><style name="Animation.AppCompat.DropDownUp" parent="Base.Animation.AppCompat.DropDownUp"/><style name="Animation.AppCompat.Tooltip" parent="Base.Animation.AppCompat.Tooltip"/><style name="Base.AlertDialog.AppCompat" parent="android:Widget">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
    </style><style name="Base.AlertDialog.AppCompat.Light" parent="Base.AlertDialog.AppCompat"/><style name="Base.Animation.AppCompat.Dialog" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style><style name="Base.Animation.AppCompat.DropDownUp" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style><style name="Base.Animation.AppCompat.Tooltip" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/tooltip_enter</item>
        <item name="android:windowExitAnimation">@anim/tooltip_exit</item>
    </style><style name="Base.DialogWindowTitle.AppCompat" parent="android:Widget">
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
    </style><style name="Base.DialogWindowTitleBackground.AppCompat" parent="android:Widget">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
        <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
    </style><style name="Base.TextAppearance.AppCompat" parent="android:TextAppearance">
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
        <item name="android:textColorHighlight">?android:textColorHighlight</item>
        <item name="android:textColorLink">?android:textColorLink</item>
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
    </style><style name="Base.TextAppearance.AppCompat.Body1">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Body2">
        <item name="android:textSize">@dimen/abc_text_size_body_2_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Button">
        <item name="android:textSize">@dimen/abc_text_size_button_material</item>
        <item name="textAllCaps">true</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Caption">
        <item name="android:textSize">@dimen/abc_text_size_caption_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Display1">
        <item name="android:textSize">@dimen/abc_text_size_display_1_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Display2">
        <item name="android:textSize">@dimen/abc_text_size_display_2_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Display3">
        <item name="android:textSize">@dimen/abc_text_size_display_3_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Display4">
        <item name="android:textSize">@dimen/abc_text_size_display_4_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Headline">
        <item name="android:textSize">@dimen/abc_text_size_headline_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Large">
        <item name="android:textSize">@dimen/abc_text_size_large_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Large.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Medium">
        <item name="android:textSize">@dimen/abc_text_size_medium_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Medium.Inverse">
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Menu">
        <item name="android:textSize">@dimen/abc_text_size_menu_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.SearchResult" parent="">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
    </style><style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.SearchResult.Title">
        <item name="android:textSize">18sp</item>
    </style><style name="Base.TextAppearance.AppCompat.Small">
        <item name="android:textSize">@dimen/abc_text_size_small_material</item>
        <item name="android:textColor">?android:attr/textColorTertiary</item>
    </style><style name="Base.TextAppearance.AppCompat.Small.Inverse">
        <item name="android:textColor">?android:attr/textColorTertiaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subhead_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Tooltip">
        <item name="android:textSize">14sp</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="TextAppearance.AppCompat.Button">
        <item name="android:textColor">?attr/actionMenuTextColor</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/><style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="TextAppearance.AppCompat.Widget.ActionBar.Title"/><style name="Base.TextAppearance.AppCompat.Widget.Button" parent="TextAppearance.AppCompat.Button"/><style name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.Button.Colored">
        <item name="android:textColor">@color/abc_btn_colored_text_material</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="TextAppearance.AppCompat.Button">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.DropDownItem" parent="android:TextAppearance.Small">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?attr/colorAccent</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="TextAppearance.AppCompat.Menu"/><style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="TextAppearance.AppCompat.Menu"/><style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="TextAppearance.AppCompat.Button"/><style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="TextAppearance.AppCompat.Menu"/><style name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="android:TextAppearance.Medium">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style><style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style><style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style><style name="Base.Theme.AppCompat" parent="Base.V7.Theme.AppCompat">
    </style><style name="Base.Theme.AppCompat.CompactMenu" parent="">
        <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
    </style><style name="Base.Theme.AppCompat.Dialog" parent="Base.V7.Theme.AppCompat.Dialog"/><style name="Base.Theme.AppCompat.Dialog.Alert">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.Theme.AppCompat.Dialog.FixedSize">
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
    </style><style name="Base.Theme.AppCompat.Dialog.MinWidth">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.Theme.AppCompat.DialogWhenLarge" parent="Theme.AppCompat"/><style name="Base.Theme.AppCompat.Light" parent="Base.V7.Theme.AppCompat.Light">
    </style><style name="Base.Theme.AppCompat.Light.DarkActionBar" parent="Base.Theme.AppCompat.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>

        
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
    </style><style name="Base.Theme.AppCompat.Light.Dialog" parent="Base.V7.Theme.AppCompat.Light.Dialog"/><style name="Base.Theme.AppCompat.Light.Dialog.Alert">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.Theme.AppCompat.Light.Dialog.FixedSize">
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
    </style><style name="Base.Theme.AppCompat.Light.Dialog.MinWidth">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="Theme.AppCompat.Light"/><style name="Base.ThemeOverlay.AppCompat" parent="Platform.ThemeOverlay.AppCompat"/><style name="Base.ThemeOverlay.AppCompat.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style><style name="Base.ThemeOverlay.AppCompat.Dark" parent="Platform.ThemeOverlay.AppCompat.Dark">
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>

        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>

        
        <item name="isLightTheme">false</item>
    </style><style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style><style name="Base.ThemeOverlay.AppCompat.Dialog" parent="Base.V7.ThemeOverlay.AppCompat.Dialog"/><style name="Base.ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.ThemeOverlay.AppCompat.Light" parent="Platform.ThemeOverlay.AppCompat.Light">
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>

        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>

        
        <item name="isLightTheme">true</item>
    </style><style name="Base.V7.Theme.AppCompat" parent="Platform.AppCompat">
        <item name="windowNoTitle">false</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="actionBarPopupTheme">@null</item>

        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>

        
        <item name="isLightTheme">false</item>

        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>

        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>

        
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>

        
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>

        
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>

        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>

        
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

        
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="listPreferredItemHeight">64dp</item>
        <item name="listPreferredItemHeightSmall">48dp</item>
        <item name="listPreferredItemHeightLarge">80dp</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>

        
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>

        
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>

        
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>

        
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>

        
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>

        
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>

        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>

        
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorAccent">@color/accent_material_dark</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>

        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>

        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>

        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>

        
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>

        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>

        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>

        
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>

        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="listDividerAlertDialog">@null</item>

        
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>

        <item name="colorError">@color/error_color_material</item>
    </style><style name="Base.V7.Theme.AppCompat.Dialog" parent="Base.Theme.AppCompat">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:colorBackgroundCacheHint">@null</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>
    </style><style name="Base.V7.Theme.AppCompat.Light" parent="Platform.AppCompat.Light">
        <item name="windowNoTitle">false</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="actionBarPopupTheme">@null</item>

        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>

        
        <item name="isLightTheme">true</item>

        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>

        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>

        
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>

        
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>

        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>

        
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>

        
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>

        
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="listPreferredItemHeight">64dp</item>
        <item name="listPreferredItemHeightSmall">48dp</item>
        <item name="listPreferredItemHeightLarge">80dp</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>

        
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>

        
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>

        
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>

        
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>

        
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>

        
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>

        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>

        
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorAccent">@color/accent_material_light</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>

        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>

        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>

        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>

        
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>

        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>

        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>

        
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>

        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="listDividerAlertDialog">@null</item>

        
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_dark</item>
        <item name="tooltipForegroundColor">@color/foreground_material_dark</item>

        <item name="colorError">@color/error_color_material</item>
    </style><style name="Base.V7.Theme.AppCompat.Light.Dialog" parent="Base.Theme.AppCompat.Light">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:colorBackgroundCacheHint">@null</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>
    </style><style name="Base.V7.ThemeOverlay.AppCompat.Dialog" parent="Base.ThemeOverlay.AppCompat">
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
    </style><style name="Base.V7.Widget.AppCompat.AutoCompleteTextView" parent="android:Widget.AutoCompleteTextView">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
    </style><style name="Base.V7.Widget.AppCompat.EditText" parent="android:Widget.EditText">
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
    </style><style name="Base.V7.Widget.AppCompat.Toolbar" parent="android:Widget">
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="titleMargin">4dp</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="buttonGravity">top</item>
        <item name="collapseIcon">?attr/homeAsUpIndicator</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="contentInsetStart">16dp</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
    </style><style name="Base.Widget.AppCompat.ActionBar" parent="">
        <item name="displayOptions">showTitle</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="height">?attr/actionBarSize</item>

        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>

        <item name="background">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="backgroundSplit">@null</item>

        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>

        <item name="android:gravity">center_vertical</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="elevation">@dimen/abc_action_bar_elevation_material</item>
        <item name="popupTheme">?attr/actionBarPopupTheme</item>
    </style><style name="Base.Widget.AppCompat.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
    </style><style name="Base.Widget.AppCompat.ActionBar.TabBar" parent="">
        <item name="divider">?attr/actionBarDivider</item>
        <item name="showDividers">middle</item>
        <item name="dividerPadding">8dip</item>
    </style><style name="Base.Widget.AppCompat.ActionBar.TabText" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:maxLines">2</item>
        <item name="android:maxWidth">180dp</item>
        <item name="textAllCaps">true</item>
    </style><style name="Base.Widget.AppCompat.ActionBar.TabView" parent="">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
        <item name="android:gravity">center_horizontal</item>
        <item name="android:paddingLeft">16dip</item>
        <item name="android:paddingRight">16dip</item>
        <item name="android:layout_width">0dip</item>
        <item name="android:layout_weight">1</item>
        <item name="android:minWidth">80dip</item>
    </style><style name="Base.Widget.AppCompat.ActionButton" parent="RtlUnderlay.Widget.AppCompat.ActionButton">
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:scaleType">center</item>
        <item name="android:gravity">center</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style><style name="Base.Widget.AppCompat.ActionButton.CloseMode">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
    </style><style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow">
        <item name="srcCompat">@drawable/abc_ic_menu_overflow_material</item>
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:contentDescription">@string/abc_action_menu_overflow_description</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_overflow_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
    </style><style name="Base.Widget.AppCompat.ActionMode" parent="">
        <item name="background">?attr/actionModeBackground</item>
        <item name="backgroundSplit">?attr/actionModeSplitBackground</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>
    </style><style name="Base.Widget.AppCompat.ActivityChooserView" parent="">
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/abc_ab_share_pack_mtrl_alpha</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="showDividers">middle</item>
        <item name="dividerPadding">6dip</item>
    </style><style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="Base.V7.Widget.AppCompat.AutoCompleteTextView"/><style name="Base.Widget.AppCompat.Button" parent="android:Widget">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:minHeight">48dip</item>
        <item name="android:minWidth">88dip</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center_vertical|center_horizontal</item>
    </style><style name="Base.Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/abc_btn_borderless_material</item>
    </style><style name="Base.Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored</item>
    </style><style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:minWidth">64dp</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
    </style><style name="Base.Widget.AppCompat.Button.Colored">
        <item name="android:background">@drawable/abc_btn_colored_material</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Colored</item>
    </style><style name="Base.Widget.AppCompat.Button.Small">
        <item name="android:minHeight">48dip</item>
        <item name="android:minWidth">48dip</item>
    </style><style name="Base.Widget.AppCompat.ButtonBar" parent="android:Widget">
        <item name="android:background">@null</item>
    </style><style name="Base.Widget.AppCompat.ButtonBar.AlertDialog"/><style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="android:Widget.CompoundButton.CheckBox">
        <item name="android:button">?android:attr/listChoiceIndicatorMultiple</item>
        <item name="android:background">?attr/controlBackground</item>
    </style><style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="android:Widget.CompoundButton.RadioButton">
        <item name="android:button">?android:attr/listChoiceIndicatorSingle</item>
        <item name="android:background">?attr/controlBackground</item>
    </style><style name="Base.Widget.AppCompat.CompoundButton.Switch" parent="android:Widget.CompoundButton">
        <item name="track">@drawable/abc_switch_track_mtrl_alpha</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="android:background">?attr/controlBackground</item>
        <item name="showText">false</item>
        <item name="switchPadding">@dimen/abc_switch_padding</item>
        <item name="android:textOn">@string/abc_capital_on</item>
        <item name="android:textOff">@string/abc_capital_off</item>
    </style><style name="Base.Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle.Common">
        <item name="barLength">18dp</item>
        <item name="gapBetweenBars">3dp</item>
        <item name="drawableSize">24dp</item>
    </style><style name="Base.Widget.AppCompat.DrawerArrowToggle.Common" parent="">
        <item name="color">?android:attr/textColorSecondary</item>
        <item name="spinBars">true</item>
        <item name="thickness">2dp</item>
        <item name="arrowShaftLength">16dp</item>
        <item name="arrowHeadLength">8dp</item>
    </style><style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.DropDownItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:gravity">center_vertical</item>
    </style><style name="Base.Widget.AppCompat.EditText" parent="Base.V7.Widget.AppCompat.EditText"/><style name="Base.Widget.AppCompat.ImageButton" parent="android:Widget.ImageButton">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
    </style><style name="Base.Widget.AppCompat.Light.ActionBar" parent="Base.Widget.AppCompat.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style><style name="Base.Widget.AppCompat.Light.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
    </style><style name="Base.Widget.AppCompat.Light.ActionBar.TabBar" parent="Base.Widget.AppCompat.ActionBar.TabBar">
    </style><style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="Base.Widget.AppCompat.ActionBar.TabText">
    </style><style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="Base.Widget.AppCompat.Light.ActionBar.TabText">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium.Inverse</item>
    </style><style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="Base.Widget.AppCompat.ActionBar.TabView">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
    </style><style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style><style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
        <item name="overlapAnchor">true</item>
        <item name="android:dropDownHorizontalOffset">-4dip</item>
    </style><style name="Base.Widget.AppCompat.ListMenuView" parent="android:Widget">
        <item name="subMenuArrow">@drawable/abc_ic_arrow_drop_right_black_24dp</item>
    </style><style name="Base.Widget.AppCompat.ListPopupWindow" parent="">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownVerticalOffset">0dip</item>
        <item name="android:dropDownHorizontalOffset">0dip</item>
        <item name="android:dropDownWidth">wrap_content</item>
    </style><style name="Base.Widget.AppCompat.ListView" parent="android:Widget.ListView">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
    </style><style name="Base.Widget.AppCompat.ListView.DropDown">
        <item name="android:divider">@null</item>
    </style><style name="Base.Widget.AppCompat.ListView.Menu" parent="android:Widget.ListView.Menu">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:divider">?attr/dividerHorizontal</item>
    </style><style name="Base.Widget.AppCompat.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style><style name="Base.Widget.AppCompat.PopupMenu.Overflow">
        <item name="overlapAnchor">true</item>
        <item name="android:dropDownHorizontalOffset">-4dip</item>
    </style><style name="Base.Widget.AppCompat.PopupWindow" parent="android:Widget.PopupWindow">
    </style><style name="Base.Widget.AppCompat.ProgressBar" parent="android:Widget.ProgressBar">
        <item name="android:minWidth">@dimen/abc_action_bar_progress_bar_size</item>
        <item name="android:maxWidth">@dimen/abc_action_bar_progress_bar_size</item>
        <item name="android:minHeight">@dimen/abc_action_bar_progress_bar_size</item>
        <item name="android:maxHeight">@dimen/abc_action_bar_progress_bar_size</item>
    </style><style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="android:Widget.ProgressBar.Horizontal">
    </style><style name="Base.Widget.AppCompat.RatingBar" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_material</item>
    </style><style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:minHeight">36dp</item>
        <item name="android:maxHeight">36dp</item>
        <item name="android:isIndicator">true</item>
        <item name="android:thumb">@null</item>
    </style><style name="Base.Widget.AppCompat.RatingBar.Small" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:minHeight">16dp</item>
        <item name="android:maxHeight">16dp</item>
        <item name="android:isIndicator">true</item>
        <item name="android:thumb">@null</item>
    </style><style name="Base.Widget.AppCompat.SearchView" parent="android:Widget">
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="closeIcon">@drawable/abc_ic_clear_material</item>
        <item name="searchIcon">@drawable/abc_ic_search_api_material</item>
        <item name="searchHintIcon">@drawable/abc_ic_search_api_material</item>
        <item name="goIcon">@drawable/abc_ic_go_search_api_material</item>
        <item name="voiceIcon">@drawable/abc_ic_voice_search_api_material</item>
        <item name="commitIcon">@drawable/abc_ic_commit_search_api_mtrl_alpha</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
    </style><style name="Base.Widget.AppCompat.SearchView.ActionBar">
        <item name="queryBackground">@null</item>
        <item name="submitBackground">@null</item>
        <item name="searchHintIcon">@null</item>
        <item name="defaultQueryHint">@string/abc_search_hint</item>
    </style><style name="Base.Widget.AppCompat.SeekBar" parent="android:Widget">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:thumb">@drawable/abc_seekbar_thumb_material</item>
        <item name="android:focusable">true</item>
        <item name="android:paddingLeft">16dip</item>
        <item name="android:paddingRight">16dip</item>
    </style><style name="Base.Widget.AppCompat.SeekBar.Discrete">
        <item name="tickMark">@drawable/abc_seekbar_tick_mark_material</item>
    </style><style name="Base.Widget.AppCompat.Spinner" parent="Platform.Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/abc_spinner_mtrl_am_alpha</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:dropDownVerticalOffset">0dip</item>
        <item name="android:dropDownHorizontalOffset">0dip</item>
        <item name="android:dropDownWidth">wrap_content</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">left|start|center_vertical</item>
        <item name="overlapAnchor">true</item>
    </style><style name="Base.Widget.AppCompat.Spinner.Underlined">
        <item name="android:background">@drawable/abc_spinner_textfield_background_material</item>
    </style><style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="android:Widget.TextView.SpinnerItem">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
    </style><style name="Base.Widget.AppCompat.Toolbar" parent="Base.V7.Widget.AppCompat.Toolbar"/><style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="android:Widget">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
        <item name="android:scaleType">center</item>
    </style><style name="Platform.AppCompat" parent="android:Theme">
        <item name="android:windowNoTitle">true</item>

        
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_dark</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_dark</item>

        
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorLink">?attr/colorAccent</item>

        
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_dark</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_dark</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_dark</item>
    </style><style name="Platform.AppCompat.Light" parent="android:Theme.Light">
        <item name="android:windowNoTitle">true</item>

        
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_light</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_light</item>

        
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorLink">?attr/colorAccent</item>

        
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_light</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_light</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_light</item>
    </style><style name="Platform.ThemeOverlay.AppCompat" parent=""/><style name="Platform.ThemeOverlay.AppCompat.Dark">
        
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>

        
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
    </style><style name="Platform.ThemeOverlay.AppCompat.Light">
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>

        
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.Light.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
    </style><style name="Platform.Widget.AppCompat.Spinner" parent="android:Widget.Spinner"/><style name="RtlOverlay.DialogWindowTitle.AppCompat" parent="Base.DialogWindowTitle.AppCompat">
    </style><style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="android:Widget">
        <item name="android:layout_gravity">center_vertical|left</item>
        <item name="android:paddingRight">8dp</item>
    </style><style name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" parent="android:Widget">
        <item name="android:layout_marginRight">8dp</item>
    </style><style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="android:Widget">
        <item name="android:paddingRight">16dp</item>
    </style><style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style><style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="android:Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="android:Widget">
        <item name="android:paddingLeft">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingRight">4dp</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="android:Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="android:Widget">
        <item name="android:layout_toLeftOf">@id/edit_query</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="android:Widget">
        <item name="android:layout_alignParentRight">true</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toLeftOf">@android:id/icon2</item>
        <item name="android:layout_toRightOf">@android:id/icon1</item>
    </style><style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="android:Widget">
        <item name="android:layout_marginLeft">@dimen/abc_dropdownitem_text_padding_left</item>
    </style><style name="RtlUnderlay.Widget.AppCompat.ActionButton" parent="android:Widget">
        <item name="android:paddingLeft">12dp</item>
        <item name="android:paddingRight">12dp</item>
    </style><style name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingLeft">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style><style name="TextAppearance.AppCompat" parent="Base.TextAppearance.AppCompat"/><style name="TextAppearance.AppCompat.Body1" parent="Base.TextAppearance.AppCompat.Body1"/><style name="TextAppearance.AppCompat.Body2" parent="Base.TextAppearance.AppCompat.Body2"/><style name="TextAppearance.AppCompat.Button" parent="Base.TextAppearance.AppCompat.Button"/><style name="TextAppearance.AppCompat.Caption" parent="Base.TextAppearance.AppCompat.Caption"/><style name="TextAppearance.AppCompat.Display1" parent="Base.TextAppearance.AppCompat.Display1"/><style name="TextAppearance.AppCompat.Display2" parent="Base.TextAppearance.AppCompat.Display2"/><style name="TextAppearance.AppCompat.Display3" parent="Base.TextAppearance.AppCompat.Display3"/><style name="TextAppearance.AppCompat.Display4" parent="Base.TextAppearance.AppCompat.Display4"/><style name="TextAppearance.AppCompat.Headline" parent="Base.TextAppearance.AppCompat.Headline"/><style name="TextAppearance.AppCompat.Inverse" parent="Base.TextAppearance.AppCompat.Inverse"/><style name="TextAppearance.AppCompat.Large" parent="Base.TextAppearance.AppCompat.Large"/><style name="TextAppearance.AppCompat.Large.Inverse" parent="Base.TextAppearance.AppCompat.Large.Inverse"/><style name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" parent="TextAppearance.AppCompat.SearchResult.Subtitle"/><style name="TextAppearance.AppCompat.Light.SearchResult.Title" parent="TextAppearance.AppCompat.SearchResult.Title"/><style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="TextAppearance.AppCompat.Widget.PopupMenu.Large"/><style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="TextAppearance.AppCompat.Widget.PopupMenu.Small"/><style name="TextAppearance.AppCompat.Medium" parent="Base.TextAppearance.AppCompat.Medium"/><style name="TextAppearance.AppCompat.Medium.Inverse" parent="Base.TextAppearance.AppCompat.Medium.Inverse"/><style name="TextAppearance.AppCompat.Menu" parent="Base.TextAppearance.AppCompat.Menu"/><style name="TextAppearance.AppCompat.SearchResult.Subtitle" parent="Base.TextAppearance.AppCompat.SearchResult.Subtitle">
    </style><style name="TextAppearance.AppCompat.SearchResult.Title" parent="Base.TextAppearance.AppCompat.SearchResult.Title">
    </style><style name="TextAppearance.AppCompat.Small" parent="Base.TextAppearance.AppCompat.Small"/><style name="TextAppearance.AppCompat.Small.Inverse" parent="Base.TextAppearance.AppCompat.Small.Inverse"/><style name="TextAppearance.AppCompat.Subhead" parent="Base.TextAppearance.AppCompat.Subhead"/><style name="TextAppearance.AppCompat.Subhead.Inverse" parent="Base.TextAppearance.AppCompat.Subhead.Inverse"/><style name="TextAppearance.AppCompat.Title" parent="Base.TextAppearance.AppCompat.Title"/><style name="TextAppearance.AppCompat.Title.Inverse" parent="Base.TextAppearance.AppCompat.Title.Inverse"/><style name="TextAppearance.AppCompat.Tooltip" parent="Base.TextAppearance.AppCompat.Tooltip"/><style name="TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu">
    </style><style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/><style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse">
    </style><style name="TextAppearance.AppCompat.Widget.ActionBar.Title" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Title"/><style name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
    </style><style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style><style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" parent="TextAppearance.AppCompat.Widget.ActionMode.Subtitle"/><style name="TextAppearance.AppCompat.Widget.ActionMode.Title" parent="Base.TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style><style name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" parent="TextAppearance.AppCompat.Widget.ActionMode.Title"/><style name="TextAppearance.AppCompat.Widget.Button" parent="Base.TextAppearance.AppCompat.Widget.Button"/><style name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored"/><style name="TextAppearance.AppCompat.Widget.Button.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button.Colored"/><style name="TextAppearance.AppCompat.Widget.Button.Inverse" parent="Base.TextAppearance.AppCompat.Widget.Button.Inverse"/><style name="TextAppearance.AppCompat.Widget.DropDownItem" parent="Base.TextAppearance.AppCompat.Widget.DropDownItem">
    </style><style name="TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header"/><style name="TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large"/><style name="TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small"/><style name="TextAppearance.AppCompat.Widget.Switch" parent="Base.TextAppearance.AppCompat.Widget.Switch"/><style name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem"/><style name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item">
    </style><style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
    </style><style name="TextAppearance.Widget.AppCompat.Toolbar.Title" parent="Base.TextAppearance.Widget.AppCompat.Toolbar.Title">
    </style><style name="Theme.AppCompat" parent="Base.Theme.AppCompat"/><style name="Theme.AppCompat.CompactMenu" parent="Base.Theme.AppCompat.CompactMenu"/><style name="Theme.AppCompat.DayNight" parent="Theme.AppCompat.Light"/><style name="Theme.AppCompat.DayNight.DarkActionBar" parent="Theme.AppCompat.Light.DarkActionBar"/><style name="Theme.AppCompat.DayNight.Dialog" parent="Theme.AppCompat.Light.Dialog"/><style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="Theme.AppCompat.Light.Dialog.Alert"/><style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="Theme.AppCompat.Light.Dialog.MinWidth"/><style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="Theme.AppCompat.Light.DialogWhenLarge"/><style name="Theme.AppCompat.DayNight.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar"/><style name="Theme.AppCompat.Dialog" parent="Base.Theme.AppCompat.Dialog"/><style name="Theme.AppCompat.Dialog.Alert" parent="Base.Theme.AppCompat.Dialog.Alert"/><style name="Theme.AppCompat.Dialog.MinWidth" parent="Base.Theme.AppCompat.Dialog.MinWidth"/><style name="Theme.AppCompat.DialogWhenLarge" parent="Base.Theme.AppCompat.DialogWhenLarge">
    </style><style name="Theme.AppCompat.Light" parent="Base.Theme.AppCompat.Light"/><style name="Theme.AppCompat.Light.DarkActionBar" parent="Base.Theme.AppCompat.Light.DarkActionBar"/><style name="Theme.AppCompat.Light.Dialog" parent="Base.Theme.AppCompat.Light.Dialog"/><style name="Theme.AppCompat.Light.Dialog.Alert" parent="Base.Theme.AppCompat.Light.Dialog.Alert"/><style name="Theme.AppCompat.Light.Dialog.MinWidth" parent="Base.Theme.AppCompat.Light.Dialog.MinWidth"/><style name="Theme.AppCompat.Light.DialogWhenLarge" parent="Base.Theme.AppCompat.Light.DialogWhenLarge">
    </style><style name="Theme.AppCompat.Light.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.AppCompat.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="ThemeOverlay.AppCompat" parent="Base.ThemeOverlay.AppCompat"/><style name="ThemeOverlay.AppCompat.ActionBar" parent="Base.ThemeOverlay.AppCompat.ActionBar"/><style name="ThemeOverlay.AppCompat.Dark" parent="Base.ThemeOverlay.AppCompat.Dark"/><style name="ThemeOverlay.AppCompat.Dark.ActionBar" parent="Base.ThemeOverlay.AppCompat.Dark.ActionBar"/><style name="ThemeOverlay.AppCompat.Dialog" parent="Base.ThemeOverlay.AppCompat.Dialog"/><style name="ThemeOverlay.AppCompat.Dialog.Alert" parent="Base.ThemeOverlay.AppCompat.Dialog.Alert"/><style name="ThemeOverlay.AppCompat.Light" parent="Base.ThemeOverlay.AppCompat.Light"/><style name="Widget.AppCompat.ActionBar" parent="Base.Widget.AppCompat.ActionBar">
    </style><style name="Widget.AppCompat.ActionBar.Solid" parent="Base.Widget.AppCompat.ActionBar.Solid">
    </style><style name="Widget.AppCompat.ActionBar.TabBar" parent="Base.Widget.AppCompat.ActionBar.TabBar">
    </style><style name="Widget.AppCompat.ActionBar.TabText" parent="Base.Widget.AppCompat.ActionBar.TabText">
    </style><style name="Widget.AppCompat.ActionBar.TabView" parent="Base.Widget.AppCompat.ActionBar.TabView">
    </style><style name="Widget.AppCompat.ActionButton" parent="Base.Widget.AppCompat.ActionButton"/><style name="Widget.AppCompat.ActionButton.CloseMode" parent="Base.Widget.AppCompat.ActionButton.CloseMode"/><style name="Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton.Overflow"/><style name="Widget.AppCompat.ActionMode" parent="Base.Widget.AppCompat.ActionMode">
    </style><style name="Widget.AppCompat.ActivityChooserView" parent="Base.Widget.AppCompat.ActivityChooserView">
    </style><style name="Widget.AppCompat.AutoCompleteTextView" parent="Base.Widget.AppCompat.AutoCompleteTextView">
    </style><style name="Widget.AppCompat.Button" parent="Base.Widget.AppCompat.Button"/><style name="Widget.AppCompat.Button.Borderless" parent="Base.Widget.AppCompat.Button.Borderless"/><style name="Widget.AppCompat.Button.Borderless.Colored" parent="Base.Widget.AppCompat.Button.Borderless.Colored"/><style name="Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog"/><style name="Widget.AppCompat.Button.Colored" parent="Base.Widget.AppCompat.Button.Colored"/><style name="Widget.AppCompat.Button.Small" parent="Base.Widget.AppCompat.Button.Small"/><style name="Widget.AppCompat.ButtonBar" parent="Base.Widget.AppCompat.ButtonBar"/><style name="Widget.AppCompat.ButtonBar.AlertDialog" parent="Base.Widget.AppCompat.ButtonBar.AlertDialog"/><style name="Widget.AppCompat.CompoundButton.CheckBox" parent="Base.Widget.AppCompat.CompoundButton.CheckBox"/><style name="Widget.AppCompat.CompoundButton.RadioButton" parent="Base.Widget.AppCompat.CompoundButton.RadioButton"/><style name="Widget.AppCompat.CompoundButton.Switch" parent="Base.Widget.AppCompat.CompoundButton.Switch"/><style name="Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?attr/colorControlNormal</item>
    </style><style name="Widget.AppCompat.DropDownItem.Spinner" parent="RtlOverlay.Widget.AppCompat.Search.DropDown.Text"/><style name="Widget.AppCompat.EditText" parent="Base.Widget.AppCompat.EditText"/><style name="Widget.AppCompat.ImageButton" parent="Base.Widget.AppCompat.ImageButton"/><style name="Widget.AppCompat.Light.ActionBar" parent="Base.Widget.AppCompat.Light.ActionBar">
    </style><style name="Widget.AppCompat.Light.ActionBar.Solid" parent="Base.Widget.AppCompat.Light.ActionBar.Solid">
    </style><style name="Widget.AppCompat.Light.ActionBar.Solid.Inverse"/><style name="Widget.AppCompat.Light.ActionBar.TabBar" parent="Base.Widget.AppCompat.Light.ActionBar.TabBar">
    </style><style name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse"/><style name="Widget.AppCompat.Light.ActionBar.TabText" parent="Base.Widget.AppCompat.Light.ActionBar.TabText">
    </style><style name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse">
    </style><style name="Widget.AppCompat.Light.ActionBar.TabView" parent="Base.Widget.AppCompat.Light.ActionBar.TabView">
    </style><style name="Widget.AppCompat.Light.ActionBar.TabView.Inverse"/><style name="Widget.AppCompat.Light.ActionButton" parent="Widget.AppCompat.ActionButton"/><style name="Widget.AppCompat.Light.ActionButton.CloseMode" parent="Widget.AppCompat.ActionButton.CloseMode"/><style name="Widget.AppCompat.Light.ActionButton.Overflow" parent="Widget.AppCompat.ActionButton.Overflow"/><style name="Widget.AppCompat.Light.ActionMode.Inverse" parent="Widget.AppCompat.ActionMode"/><style name="Widget.AppCompat.Light.ActivityChooserView" parent="Widget.AppCompat.ActivityChooserView"/><style name="Widget.AppCompat.Light.AutoCompleteTextView" parent="Widget.AppCompat.AutoCompleteTextView"/><style name="Widget.AppCompat.Light.DropDownItem.Spinner" parent="Widget.AppCompat.DropDownItem.Spinner"/><style name="Widget.AppCompat.Light.ListPopupWindow" parent="Widget.AppCompat.ListPopupWindow"/><style name="Widget.AppCompat.Light.ListView.DropDown" parent="Widget.AppCompat.ListView.DropDown"/><style name="Widget.AppCompat.Light.PopupMenu" parent="Base.Widget.AppCompat.Light.PopupMenu"/><style name="Widget.AppCompat.Light.PopupMenu.Overflow" parent="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
    </style><style name="Widget.AppCompat.Light.SearchView" parent="Widget.AppCompat.SearchView"/><style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" parent="Widget.AppCompat.Spinner.DropDown.ActionBar"/><style name="Widget.AppCompat.ListMenuView" parent="Base.Widget.AppCompat.ListMenuView"/><style name="Widget.AppCompat.ListPopupWindow" parent="Base.Widget.AppCompat.ListPopupWindow">
    </style><style name="Widget.AppCompat.ListView" parent="Base.Widget.AppCompat.ListView"/><style name="Widget.AppCompat.ListView.DropDown" parent="Base.Widget.AppCompat.ListView.DropDown"/><style name="Widget.AppCompat.ListView.Menu" parent="Base.Widget.AppCompat.ListView.Menu"/><style name="Widget.AppCompat.PopupMenu" parent="Base.Widget.AppCompat.PopupMenu"/><style name="Widget.AppCompat.PopupMenu.Overflow" parent="Base.Widget.AppCompat.PopupMenu.Overflow">
    </style><style name="Widget.AppCompat.PopupWindow" parent="Base.Widget.AppCompat.PopupWindow">
    </style><style name="Widget.AppCompat.ProgressBar" parent="Base.Widget.AppCompat.ProgressBar">
    </style><style name="Widget.AppCompat.ProgressBar.Horizontal" parent="Base.Widget.AppCompat.ProgressBar.Horizontal">
    </style><style name="Widget.AppCompat.RatingBar" parent="Base.Widget.AppCompat.RatingBar"/><style name="Widget.AppCompat.RatingBar.Indicator" parent="Base.Widget.AppCompat.RatingBar.Indicator"/><style name="Widget.AppCompat.RatingBar.Small" parent="Base.Widget.AppCompat.RatingBar.Small"/><style name="Widget.AppCompat.SearchView" parent="Base.Widget.AppCompat.SearchView"/><style name="Widget.AppCompat.SearchView.ActionBar" parent="Base.Widget.AppCompat.SearchView.ActionBar"/><style name="Widget.AppCompat.SeekBar" parent="Base.Widget.AppCompat.SeekBar"/><style name="Widget.AppCompat.SeekBar.Discrete" parent="Base.Widget.AppCompat.SeekBar.Discrete"/><style name="Widget.AppCompat.Spinner" parent="Base.Widget.AppCompat.Spinner"/><style name="Widget.AppCompat.Spinner.DropDown"/><style name="Widget.AppCompat.Spinner.DropDown.ActionBar"/><style name="Widget.AppCompat.Spinner.Underlined" parent="Base.Widget.AppCompat.Spinner.Underlined"/><style name="Widget.AppCompat.TextView.SpinnerItem" parent="Base.Widget.AppCompat.TextView.SpinnerItem"/><style name="Widget.AppCompat.Toolbar" parent="Base.Widget.AppCompat.Toolbar"/><style name="Widget.AppCompat.Toolbar.Button.Navigation" parent="Base.Widget.AppCompat.Toolbar.Button.Navigation"/></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-af\values-af.xml" qualifiers="af"><string msgid="*******************" name="abc_action_bar_home_description">"Navigeer tuis"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navigeer op"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Nog opsies"</string><string msgid="*******************" name="abc_action_mode_done">"Klaar"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Sien alles"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Kies \'n program"</string><string msgid="121134116657445385" name="abc_capital_off">"AF"</string><string msgid="3405795526292276155" name="abc_capital_on">"AAN"</string><string msgid="7723749260725869598" name="abc_search_hint">"Soek …"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Vee navraag uit"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Soeknavraag"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Soek"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Dien navraag in"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Stemsoektog"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Deel met"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Deel met <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Vou in"</string><string msgid="146198913615257606" name="search_menu_title">"Soek"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-am\values-am.xml" qualifiers="am"><string msgid="*******************" name="abc_action_bar_home_description">"ወደ መነሻ ይዳስሱ"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"ወደ ላይ ይዳስሱ"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"ተጨማሪ አማራጮች"</string><string msgid="*******************" name="abc_action_mode_done">"ተከናውኗል"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"ሁሉንም ይመልከቱ"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"መተግበሪያ ይምረጡ"</string><string msgid="121134116657445385" name="abc_capital_off">"ጠፍቷል"</string><string msgid="3405795526292276155" name="abc_capital_on">"በርቷል"</string><string msgid="7723749260725869598" name="abc_search_hint">"ፈልግ…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"መጠይቅ አጽዳ"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"የፍለጋ ጥያቄ"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"ፍለጋ"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"መጠይቅ ያስረክቡ"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"የድምፅ ፍለጋ"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"ከሚከተለው ጋር ያጋሩ"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"ከ<ns1:g id="APPLICATION_NAME">%s</ns1:g> ጋር አጋራ"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"ሰብስብ"</string><string msgid="146198913615257606" name="search_menu_title">"ፈልግ"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ar\values-ar.xml" qualifiers="ar"><string msgid="*******************" name="abc_action_bar_home_description">"التنقل إلى الشاشة الرئيسية"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"التنقل إلى أعلى"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"خيارات إضافية"</string><string msgid="*******************" name="abc_action_mode_done">"تم"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"عرض الكل"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"اختيار تطبيق"</string><string msgid="121134116657445385" name="abc_capital_off">"إيقاف"</string><string msgid="3405795526292276155" name="abc_capital_on">"تشغيل"</string><string msgid="7723749260725869598" name="abc_search_hint">"بحث…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"محو طلب البحث"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"طلب البحث"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"بحث"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"إرسال طلب البحث"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"البحث الصوتي"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"مشاركة مع"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"مشاركة مع <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"تصغير"</string><string msgid="146198913615257606" name="search_menu_title">"البحث"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-az\values-az.xml" qualifiers="az"><string msgid="*******************" name="abc_action_bar_home_description">"Evə naviqasiya et"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Yuxarı get"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Digər variantlar"</string><string msgid="*******************" name="abc_action_mode_done">"Hazırdır"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Hamısına baxın"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Tətbiq seçin"</string><string msgid="121134116657445385" name="abc_capital_off">"DEAKTİV"</string><string msgid="3405795526292276155" name="abc_capital_on">"AKTİV"</string><string msgid="7723749260725869598" name="abc_search_hint">"Axtarış..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Sorğunu təmizlə"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Axtarış sorğusu"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Axtarış"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Sorğunu göndərin"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Səsli axtarış"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Bununla paylaşın"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> ilə paylaşın"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Yığışdırın"</string><string msgid="146198913615257606" name="search_menu_title">"Axtarış"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-b+sr+Latn\values-b+sr+Latn.xml" qualifiers="b+sr+Latn"><string msgid="*******************" name="abc_action_bar_home_description">"Odlazak na Početnu"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Kretanje nagore"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Još opcija"</string><string msgid="*******************" name="abc_action_mode_done">"Gotovo"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Prikaži sve"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Izbor aplikacije"</string><string msgid="121134116657445385" name="abc_capital_off">"ISKLJUČI"</string><string msgid="3405795526292276155" name="abc_capital_on">"UKLJUČI"</string><string msgid="7723749260725869598" name="abc_search_hint">"Pretražite..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Brisanje upita"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Upit za pretragu"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Pretraga"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Slanje upita"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Glasovna pretraga"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Deli sa"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Deljenje sa aplikacijom <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Skupi"</string><string msgid="146198913615257606" name="search_menu_title">"Pretraži"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-be\values-be.xml" qualifiers="be"><string msgid="*******************" name="abc_action_bar_home_description">"Перайсці на галоўную старонку"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Перайсці ўверх"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Дадатковыя параметры"</string><string msgid="*******************" name="abc_action_mode_done">"Гатова"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Прагледзець усё"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Выбраць праграму"</string><string msgid="121134116657445385" name="abc_capital_off">"ВЫКЛ."</string><string msgid="3405795526292276155" name="abc_capital_on">"УКЛ."</string><string msgid="7723749260725869598" name="abc_search_hint">"Пошук..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Выдалiць запыт"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Запыт на пошук"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Пошук"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Адправіць запыт"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Галасавы пошук"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Абагуліць з"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Абагуліць праз праграму <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Згарнуць"</string><string msgid="146198913615257606" name="search_menu_title">"Пошук"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-bg\values-bg.xml" qualifiers="bg"><string msgid="*******************" name="abc_action_bar_home_description">"Придвижване към „Начало“"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Придвижване нагоре"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Още опции"</string><string msgid="*******************" name="abc_action_mode_done">"Готово"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Вижте всички"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Изберете приложение"</string><string msgid="121134116657445385" name="abc_capital_off">"ИЗКЛ."</string><string msgid="3405795526292276155" name="abc_capital_on">"ВКЛ."</string><string msgid="7723749260725869598" name="abc_search_hint">"Търсете…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Изчистване на заявката"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Заявка за търсене"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Търсене"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Изпращане на заявката"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Гласово търсене"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Споделяне със:"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Споделяне със: <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Свиване"</string><string msgid="146198913615257606" name="search_menu_title">"Търсене"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-bn\values-bn.xml" qualifiers="bn"><string msgid="*******************" name="abc_action_bar_home_description">"হোম এ নেভিগেট করুন"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"উপরের দিকে নেভিগেট করুন"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"আরও বিকল্প"</string><string msgid="*******************" name="abc_action_mode_done">"সম্পন্ন হয়েছে"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"সবগুলো দেখুন"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"একটি অ্যাপ্লিকেশান বেছে নিন"</string><string msgid="121134116657445385" name="abc_capital_off">"বন্ধ"</string><string msgid="3405795526292276155" name="abc_capital_on">"চালু"</string><string msgid="7723749260725869598" name="abc_search_hint">"অনুসন্ধান..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"ক্যোয়ারী সাফ করুন"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"ক্যোয়ারী খুঁজুন"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"খুঁজুন"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"ক্যোয়ারী জমা দিন"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"ভয়েস অনুসন্ধান"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"এর সাথে শেয়ার করুন"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> এর সাথে শেয়ার করুন"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"সঙ্কুচিত করুন"</string><string msgid="146198913615257606" name="search_menu_title">"খুঁজুন"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-bs\values-bs.xml" qualifiers="bs"><string msgid="*******************" name="abc_action_bar_home_description">"Vrati se na početnu stranicu"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navigiraj prema gore"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Više opcija"</string><string msgid="*******************" name="abc_action_mode_done">"Gotovo"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Prikaži sve"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Odaberite aplikaciju"</string><string msgid="121134116657445385" name="abc_capital_off">"ISKLJUČI"</string><string msgid="3405795526292276155" name="abc_capital_on">"UKLJUČI"</string><string msgid="7723749260725869598" name="abc_search_hint">"Pretraži..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Obriši upit"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Pretraži upit"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Traži"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Pošalji upit"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Glasovno pretraživanje"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Podijeli sa"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Podijeli koristeći aplikaciju <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Skupi"</string><string msgid="146198913615257606" name="search_menu_title">"Pretraži"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ca\values-ca.xml" qualifiers="ca"><string msgid="*******************" name="abc_action_bar_home_description">"Navega a la pàgina d\'inici"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navega cap a dalt"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Més opcions"</string><string msgid="*******************" name="abc_action_mode_done">"Fet"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Mostra\'ls tots"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Selecciona una aplicació"</string><string msgid="121134116657445385" name="abc_capital_off">"DESACTIVAT"</string><string msgid="3405795526292276155" name="abc_capital_on">"ACTIVAT"</string><string msgid="7723749260725869598" name="abc_search_hint">"Cerca..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Esborra la consulta"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Consulta de cerca"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Cerca"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Envia la consulta"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Cerca per veu"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Comparteix amb"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Comparteix amb <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Replega"</string><string msgid="146198913615257606" name="search_menu_title">"Cerca"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-cs\values-cs.xml" qualifiers="cs"><string msgid="*******************" name="abc_action_bar_home_description">"Přejít na plochu"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Přejít nahoru"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Více možností"</string><string msgid="*******************" name="abc_action_mode_done">"Hotovo"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Zobrazit vše"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Vybrat aplikaci"</string><string msgid="121134116657445385" name="abc_capital_off">"VYPNUTO"</string><string msgid="3405795526292276155" name="abc_capital_on">"ZAPNUTO"</string><string msgid="7723749260725869598" name="abc_search_hint">"Vyhledat…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Smazat dotaz"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Vyhledávací dotaz"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Hledat"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Odeslat dotaz"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Hlasové vyhledávání"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Sdílet pomocí"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Sdílet s aplikací <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Sbalit"</string><string msgid="146198913615257606" name="search_menu_title">"Hledat"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-da\values-da.xml" qualifiers="da"><string msgid="*******************" name="abc_action_bar_home_description">"Naviger hjem"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Naviger op"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Flere muligheder"</string><string msgid="*******************" name="abc_action_mode_done">"Luk"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Se alle"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Vælg en app"</string><string msgid="121134116657445385" name="abc_capital_off">"FRA"</string><string msgid="3405795526292276155" name="abc_capital_on">"TIL"</string><string msgid="7723749260725869598" name="abc_search_hint">"Søg…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Ryd forespørgslen"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Søgeforespørgsel"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Søg"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Indsend forespørgslen"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Talesøgning"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Del med"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Del med <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Skjul"</string><string msgid="146198913615257606" name="search_menu_title">"Søg"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-de\values-de.xml" qualifiers="de"><string msgid="*******************" name="abc_action_bar_home_description">"Zur Startseite"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Nach oben"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Weitere Optionen"</string><string msgid="*******************" name="abc_action_mode_done">"Fertig"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Alle ansehen"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"App auswählen"</string><string msgid="121134116657445385" name="abc_capital_off">"Aus"</string><string msgid="3405795526292276155" name="abc_capital_on">"An"</string><string msgid="7723749260725869598" name="abc_search_hint">"Suchen…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Suchanfrage löschen"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Suchanfrage"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Suchen"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Suchanfrage senden"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Sprachsuche"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Freigeben für"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Mit <ns1:g id="APPLICATION_NAME">%s</ns1:g> teilen"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Minimieren"</string><string msgid="146198913615257606" name="search_menu_title">"Suchen"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-el\values-el.xml" qualifiers="el"><string msgid="*******************" name="abc_action_bar_home_description">"Πλοήγηση στην αρχική σελίδα"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Πλοήγηση προς τα επάνω"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Περισσότερες επιλογές"</string><string msgid="*******************" name="abc_action_mode_done">"Τέλος"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Προβολή όλων"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Επιλέξτε κάποια εφαρμογή"</string><string msgid="121134116657445385" name="abc_capital_off">"ΑΠΕΝΕΡΓΟΠΟΙΗΣΗ"</string><string msgid="3405795526292276155" name="abc_capital_on">"ΕΝΕΡΓΟΠΟΙΗΣΗ"</string><string msgid="7723749260725869598" name="abc_search_hint">"Αναζήτηση…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Διαγραφή ερωτήματος"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Ερώτημα αναζήτησης"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Αναζήτηση"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Υποβολή ερωτήματος"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Φωνητική αναζήτηση"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Κοινή χρήση με"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Κοινή χρήση με <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Σύμπτυξη"</string><string msgid="146198913615257606" name="search_menu_title">"Αναζήτηση"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-en-rAU\values-en-rAU.xml" qualifiers="en-rAU"><string msgid="*******************" name="abc_action_bar_home_description">"Navigate home"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navigate up"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"More options"</string><string msgid="*******************" name="abc_action_mode_done">"Done"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"See all"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Choose an app"</string><string msgid="121134116657445385" name="abc_capital_off">"OFF"</string><string msgid="3405795526292276155" name="abc_capital_on">"ON"</string><string msgid="7723749260725869598" name="abc_search_hint">"Search…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Clear query"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Search query"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Search"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Submit query"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Voice search"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Share with"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Share with <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Collapse"</string><string msgid="146198913615257606" name="search_menu_title">"Search"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-en-rCA\values-en-rCA.xml" qualifiers="en-rCA"><string msgid="*******************" name="abc_action_bar_home_description">"Navigate home"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navigate up"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"More options"</string><string msgid="*******************" name="abc_action_mode_done">"Done"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"See all"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Choose an app"</string><string msgid="121134116657445385" name="abc_capital_off">"OFF"</string><string msgid="3405795526292276155" name="abc_capital_on">"ON"</string><string msgid="7723749260725869598" name="abc_search_hint">"Search…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Clear query"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Search query"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Search"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Submit query"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Voice search"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Share with"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Share with <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Collapse"</string><string msgid="146198913615257606" name="search_menu_title">"Search"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-en-rGB\values-en-rGB.xml" qualifiers="en-rGB"><string msgid="*******************" name="abc_action_bar_home_description">"Navigate home"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navigate up"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"More options"</string><string msgid="*******************" name="abc_action_mode_done">"Done"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"See all"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Choose an app"</string><string msgid="121134116657445385" name="abc_capital_off">"OFF"</string><string msgid="3405795526292276155" name="abc_capital_on">"ON"</string><string msgid="7723749260725869598" name="abc_search_hint">"Search…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Clear query"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Search query"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Search"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Submit query"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Voice search"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Share with"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Share with <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Collapse"</string><string msgid="146198913615257606" name="search_menu_title">"Search"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-en-rIN\values-en-rIN.xml" qualifiers="en-rIN"><string msgid="*******************" name="abc_action_bar_home_description">"Navigate home"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navigate up"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"More options"</string><string msgid="*******************" name="abc_action_mode_done">"Done"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"See all"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Choose an app"</string><string msgid="121134116657445385" name="abc_capital_off">"OFF"</string><string msgid="3405795526292276155" name="abc_capital_on">"ON"</string><string msgid="7723749260725869598" name="abc_search_hint">"Search…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Clear query"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Search query"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Search"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Submit query"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Voice search"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Share with"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Share with <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Collapse"</string><string msgid="146198913615257606" name="search_menu_title">"Search"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-en-rXC\values-en-rXC.xml" qualifiers="en-rXC"><string msgid="*******************" name="abc_action_bar_home_description">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‎‏‏‏‏‏‏‏‏‎‏‎‏‏‏‏‏‏‏‏‎‏‏‎‎‏‏‏‎‏‏‎‏‏‏‎‎‏‎‎‎‏‏‎‏‏‏‎‎‏‏‏‎‎‏‎‎‏‏‎‎‎‏‎Navigate home‎‏‎‎‏‎"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‎‏‏‎‏‏‎‎‎‎‏‏‏‏‏‏‏‎‏‏‏‏‏‎‏‎‎‏‏‎‏‎‎‎‎‎‏‏‏‎‏‎‎‎‎‎‏‏‎‏‏‎‎‏‎‏‎‏‏‏‏‏‎‎Navigate up‎‏‎‎‏‎"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‎‏‏‎‎‎‏‏‏‎‎‏‏‏‎‎‎‏‎‎‏‏‏‎‏‎‎‏‏‎‎‏‎‎‏‎‏‎‏‎‎‎‏‏‏‎‏‎‎‎‏‏‎‏‎‎‎‏‎‎‏‏‎‎More options‎‏‎‎‏‎"</string><string msgid="*******************" name="abc_action_mode_done">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‎‏‏‏‎‎‎‏‎‎‏‎‎‏‎‏‏‏‎‏‎‎‎‏‏‏‎‎‏‎‎‏‏‏‎‎‏‎‎‎‏‏‎‏‎‎‏‎‎‎‎‏‎‎‏‎‏‏‏‏‎‏‏‎Done‎‏‎‎‏‎"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‏‏‏‎‎‏‏‏‏‎‏‎‎‏‏‎‏‎‏‏‎‏‏‏‏‏‎‎‏‏‏‎‎‏‏‏‏‎‏‎‎‏‎‎‎‎‏‏‎‎‎‎‏‏‎‏‎‎‏‏‏‏‎‏‎See all‎‏‎‎‏‎"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‎‏‏‏‏‎‎‎‎‏‏‎‎‏‎‎‏‏‏‎‎‎‏‏‏‏‏‏‎‎‎‏‎‎‏‎‏‎‎‎‏‏‎‏‎‏‏‎‎‏‏‏‏‏‎‏‎‎‏‏‏‏‎‎Choose an app‎‏‎‎‏‎"</string><string msgid="121134116657445385" name="abc_capital_off">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‎‎‏‏‏‎‏‎‏‏‏‎‎‏‎‏‏‎‏‎‏‏‎‏‎‏‏‎‎‏‎‏‏‎‎‏‏‎‎‎‏‏‎‎‎‎‏‏‏‎‏‎‎‎‎‎‏‎‎‏‎OFF‎‏‎‎‏‎"</string><string msgid="3405795526292276155" name="abc_capital_on">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‎‏‎‏‏‏‏‎‏‎‎‎‎‏‏‏‏‎‏‎‎‎‎‏‏‏‏‏‏‎‎‏‎‎‎‏‎‏‎‎‏‎‎‎‏‎‎‎‎‎‎‎‎‏‏‏‎‏‏‏‎‏‏‎ON‎‏‎‎‏‎"</string><string msgid="7723749260725869598" name="abc_search_hint">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‏‏‏‎‏‎‏‏‎‎‏‏‎‎‎‎‎‏‎‎‎‏‎‏‎‎‎‎‎‏‏‏‏‏‎‎‏‎‏‏‏‏‎‎‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‏‏‏‎‎Search…‎‏‎‎‏‎"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‎‏‏‎‎‏‏‎‎‏‏‏‎‏‏‏‏‏‏‎‏‏‏‏‏‎‏‎‏‎‏‎‎‏‎‎‎‎‏‎‎‎‏‏‎‏‏‏‏‎‎‎‎‎‎‎‎‎‎‏‎‎‏‎Clear query‎‏‎‎‏‎"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‎‏‎‎‎‏‏‎‏‏‎‎‏‎‏‎‎‎‏‏‏‏‏‎‎‏‎‏‎‏‎‎‏‎‏‏‎‎‎‏‎‎‎‎‎‏‎‏‎‏‏‏‏‎‎‎‏‎‎‏‎‎‎‎Search query‎‏‎‎‏‎"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‏‏‏‏‎‎‏‎‏‎‏‏‎‎‏‎‏‏‏‎‏‎‎‏‎‏‎‎‏‎‎‎‏‏‎‏‎‏‎‏‎‏‎‎‎‏‎‏‎‎‎‎‎‎‏‎‏‏‎‎‎‏‎‏‎Search‎‏‎‎‏‎"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‏‏‏‏‏‎‏‏‏‏‏‎‎‏‏‏‎‏‏‎‎‏‎‎‏‎‏‎‎‏‎‏‎‏‎‏‎‏‎‎‎‎‎‏‏‎‎‎‎‎‏‏‎‎‎‏‏‎‎‎‎‎‎‎‎Submit query‎‏‎‎‏‎"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‎‎‏‏‎‎‎‏‏‎‎‏‏‎‎‎‎‏‎‎‎‎‎‎‏‎‏‏‏‏‏‎‏‏‏‎‎‏‏‏‎‎‎‏‎‏‎‎‏‏‎‏‏‎‎‏‎‎‎‏‏‎‎Voice search‎‏‎‎‏‎"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‎‏‎‏‏‏‏‎‏‏‏‏‎‎‏‏‏‏‏‏‎‏‏‏‏‎‏‎‎‎‏‎‏‎‏‏‏‎‎‏‏‎‎‎‎‎‏‎‏‎‏‎‏‏‎‏‎‏‏‎‏‎‎‎Share with‎‏‎‎‏‎"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‎‏‎‏‏‎‏‏‏‎‎‏‏‎‎‏‎‎‏‎‏‎‏‎‏‎‏‏‎‎‎‎‎‎‎‎‏‎‏‎‏‎‏‎‏‎‎‏‎‏‏‏‏‏‎‏‎‏‎‎‏‏‏‎Share with ‎‏‎‎‏‏‎<ns1:g id="APPLICATION_NAME">%s</ns1:g>‎‏‎‎‏‏‏‎‎‏‎‎‏‎"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‎‏‏‎‏‏‎‎‏‎‎‎‎‎‎‏‏‏‎‏‏‏‎‎‎‎‏‏‏‎‏‏‏‎‎‎‏‏‏‎‎‎‏‏‎‎‏‏‎‏‏‏‏‏‎‏‏‎‏‏‏‎‏‎Collapse‎‏‎‎‏‎"</string><string msgid="146198913615257606" name="search_menu_title">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‎‏‎‏‎‎‎‎‎‎‏‏‏‎‏‏‎‎‏‏‏‎‎‏‎‎‎‏‏‎‎‏‎‏‎‎‎‏‎‎‏‎‏‎‏‏‏‏‎‎‎‎‎‎‎‎‎‎‏‏‎‎Search‎‏‎‎‏‎"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-es\values-es.xml" qualifiers="es"><string msgid="*******************" name="abc_action_bar_home_description">"Ir a la pantalla de inicio"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Desplazarse hacia arriba"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Más opciones"</string><string msgid="*******************" name="abc_action_mode_done">"Listo"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Ver todo"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Seleccionar una aplicación"</string><string msgid="121134116657445385" name="abc_capital_off">"DESACTIVADO"</string><string msgid="3405795526292276155" name="abc_capital_on">"ACTIVADO"</string><string msgid="7723749260725869598" name="abc_search_hint">"Buscar…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Borrar consulta"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Consulta"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Buscar"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Enviar consulta"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Búsqueda por voz"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Compartir con"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Compartir con <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Contraer"</string><string msgid="146198913615257606" name="search_menu_title">"Buscar"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-es-rUS\values-es-rUS.xml" qualifiers="es-rUS"><string msgid="*******************" name="abc_action_bar_home_description">"Navegar a la página principal"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navegar hacia arriba"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Más opciones"</string><string msgid="*******************" name="abc_action_mode_done">"Listo"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Ver todo"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Elige una aplicación."</string><string msgid="121134116657445385" name="abc_capital_off">"DESACTIVADO"</string><string msgid="3405795526292276155" name="abc_capital_on">"ACTIVADO"</string><string msgid="7723749260725869598" name="abc_search_hint">"Buscar…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Eliminar la consulta"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Búsqueda"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Búsqueda"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Enviar consulta"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Búsqueda por voz"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Compartir con"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Compartir con <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Contraer"</string><string msgid="146198913615257606" name="search_menu_title">"Buscar"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-et\values-et.xml" qualifiers="et"><string msgid="*******************" name="abc_action_bar_home_description">"Navigeerimine avaekraanile"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navigeerimine üles"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Rohkem valikuid"</string><string msgid="*******************" name="abc_action_mode_done">"Valmis"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Kuva kõik"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Valige rakendus"</string><string msgid="121134116657445385" name="abc_capital_off">"VÄLJAS"</string><string msgid="3405795526292276155" name="abc_capital_on">"SEES"</string><string msgid="7723749260725869598" name="abc_search_hint">"Otsige …"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Päringu tühistamine"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Otsingupäring"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Otsing"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Päringu esitamine"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Häälotsing"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Jagamine:"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Jagamine rakendusega <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Ahendamine"</string><string msgid="146198913615257606" name="search_menu_title">"Otsing"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-eu\values-eu.xml" qualifiers="eu"><string msgid="*******************" name="abc_action_bar_home_description">"Joan orri nagusira"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Joan gora"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Aukera gehiago"</string><string msgid="*******************" name="abc_action_mode_done">"Eginda"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Ikusi guztiak"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Aukeratu aplikazio bat"</string><string msgid="121134116657445385" name="abc_capital_off">"DESAKTIBATUTA"</string><string msgid="3405795526292276155" name="abc_capital_on">"AKTIBATUTA"</string><string msgid="7723749260725869598" name="abc_search_hint">"Bilatu…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Garbitu kontsulta"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Bilaketa-kontsulta"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Bilatu"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Bidali kontsulta"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Ahozko bilaketa"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Partekatu hauekin"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Partekatu <ns1:g id="APPLICATION_NAME">%s</ns1:g> aplikazioarekin"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Tolestu"</string><string msgid="146198913615257606" name="search_menu_title">"Bilatu"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-fa\values-fa.xml" qualifiers="fa"><string msgid="*******************" name="abc_action_bar_home_description">"پیمایش به صفحه اصلی"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"پیمایش به بالا"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"گزینه‌های بیشتر"</string><string msgid="*******************" name="abc_action_mode_done">"تمام"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"مشاهده همه"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"انتخاب برنامه"</string><string msgid="121134116657445385" name="abc_capital_off">"خاموش"</string><string msgid="3405795526292276155" name="abc_capital_on">"روشن"</string><string msgid="7723749260725869598" name="abc_search_hint">"جستجو…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"پاک کردن عبارت جستجو"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"عبارت جستجو"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"جستجو"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"ارسال عبارت جستجو"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"جستجوی گفتاری"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"اشتراک‌گذاری با"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"اشتراک‌گذاری با <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"کوچک کردن"</string><string msgid="146198913615257606" name="search_menu_title">"جستجو"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-fi\values-fi.xml" qualifiers="fi"><string msgid="*******************" name="abc_action_bar_home_description">"Siirry etusivulle"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Siirry ylös"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Lisää"</string><string msgid="*******************" name="abc_action_mode_done">"Valmis"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Näytä kaikki"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Valitse sovellus"</string><string msgid="121134116657445385" name="abc_capital_off">"POIS KÄYTÖSTÄ"</string><string msgid="3405795526292276155" name="abc_capital_on">"KÄYTÖSSÄ"</string><string msgid="7723749260725869598" name="abc_search_hint">"Haku…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Tyhjennä kysely"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Hakulauseke"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Haku"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Lähetä kysely"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Puhehaku"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Jakaminen:"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Jaa sovelluksessa <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Kutista"</string><string msgid="146198913615257606" name="search_menu_title">"Haku"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-fr\values-fr.xml" qualifiers="fr"><string msgid="*******************" name="abc_action_bar_home_description">"Revenir à l\'accueil"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Revenir en haut de la page"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Plus d\'options"</string><string msgid="*******************" name="abc_action_mode_done">"OK"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Tout afficher"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Sélectionner une application"</string><string msgid="121134116657445385" name="abc_capital_off">"DÉSACTIVÉ"</string><string msgid="3405795526292276155" name="abc_capital_on">"ACTIVÉ"</string><string msgid="7723749260725869598" name="abc_search_hint">"Rechercher…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Effacer la requête"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Requête de recherche"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Rechercher"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Envoyer la requête"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Recherche vocale"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Partager avec"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Partager avec <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Réduire"</string><string msgid="146198913615257606" name="search_menu_title">"Rechercher"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-fr-rCA\values-fr-rCA.xml" qualifiers="fr-rCA"><string msgid="*******************" name="abc_action_bar_home_description">"Revenir à l\'accueil"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Revenir en haut de la page"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Plus d\'options"</string><string msgid="*******************" name="abc_action_mode_done">"Terminé"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Voir toutes les chaînes"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Sélectionnez une application"</string><string msgid="121134116657445385" name="abc_capital_off">"DÉSACTIVÉ"</string><string msgid="3405795526292276155" name="abc_capital_on">"ACTIVÉ"</string><string msgid="7723749260725869598" name="abc_search_hint">"Recherche en cours..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Effacer la requête"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Requête de recherche"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Rechercher"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Envoyer la requête"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Recherche vocale"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Partager"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Partager avec <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Réduire"</string><string msgid="146198913615257606" name="search_menu_title">"Rechercher"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-gl\values-gl.xml" qualifiers="gl"><string msgid="*******************" name="abc_action_bar_home_description">"Ir á páxina de inicio"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Desprazarse cara arriba"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Máis opcións"</string><string msgid="*******************" name="abc_action_mode_done">"Feito"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Ver todas"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Escoller unha aplicación"</string><string msgid="121134116657445385" name="abc_capital_off">"DESACTIVAR"</string><string msgid="3405795526292276155" name="abc_capital_on">"ACTIVAR"</string><string msgid="7723749260725869598" name="abc_search_hint">"Buscar…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Borrar consulta"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Consulta de busca"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Buscar"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Enviar consulta"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Busca por voz"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Compartir con"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Compartir con <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Contraer"</string><string msgid="146198913615257606" name="search_menu_title">"Buscar"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-gu\values-gu.xml" qualifiers="gu"><string msgid="*******************" name="abc_action_bar_home_description">"હોમ પર નેવિગેટ કરો"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"ઉપર નેવિગેટ કરો"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"વધુ વિકલ્પો"</string><string msgid="*******************" name="abc_action_mode_done">"થઈ ગયું"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"બધું જુઓ"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"એક ઍપ્લિકેશન પસંદ કરો"</string><string msgid="121134116657445385" name="abc_capital_off">"બંધ"</string><string msgid="3405795526292276155" name="abc_capital_on">"ચાલુ"</string><string msgid="7723749260725869598" name="abc_search_hint">"શોધો…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"ક્વેરી સાફ કરો"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"શોધ ક્વેરી"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"શોધો"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"ક્વેરી સબમિટ કરો"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"વૉઇસ શોધ"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"આની સાથે શેર કરો"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g>ની સાથે શેર કરો"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"સંકુચિત કરો"</string><string msgid="146198913615257606" name="search_menu_title">"શોધો"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-h720dp-v13\values-h720dp-v13.xml" qualifiers="h720dp-v13"><dimen name="abc_alert_dialog_button_bar_height">54dip</dimen></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-hdpi-v4\values-hdpi-v4.xml" qualifiers="hdpi-v4"><style name="Base.Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle.Common">
          <item name="barLength">18.66dp</item>
          <item name="gapBetweenBars">3.33dp</item>
          <item name="drawableSize">24dp</item>
     </style></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-hi\values-hi.xml" qualifiers="hi"><string msgid="*******************" name="abc_action_bar_home_description">"होम पेज पर जाएं"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"ऊपर जाएं"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"ज़्यादा विकल्प"</string><string msgid="*******************" name="abc_action_mode_done">"हो गया"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"सभी देखें"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"कोई एप्‍लिकेशन चुनें"</string><string msgid="121134116657445385" name="abc_capital_off">"बंद"</string><string msgid="3405795526292276155" name="abc_capital_on">"चालू"</string><string msgid="7723749260725869598" name="abc_search_hint">"खोजा जा रहा है…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"क्‍वेरी साफ़ करें"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"सर्च क्वेरी"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"सर्च करें"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"क्वेरी सबमिट करें"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"आवाज़ सर्च"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"इसके द्वारा साझा करें"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> के साथ साझा करें"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"छोटा करें"</string><string msgid="146198913615257606" name="search_menu_title">"सर्च"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-hr\values-hr.xml" qualifiers="hr"><string msgid="*******************" name="abc_action_bar_home_description">"Idi na početnu"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Idi gore"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Dodatne opcije"</string><string msgid="*******************" name="abc_action_mode_done">"Gotovo"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Prikaži sve"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Odabir aplikacije"</string><string msgid="121134116657445385" name="abc_capital_off">"ISKLJUČENO"</string><string msgid="3405795526292276155" name="abc_capital_on">"UKLJUČENO"</string><string msgid="7723749260725869598" name="abc_search_hint">"Pretražite…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Izbriši upit"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Upit za pretraživanje"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Pretraživanje"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Pošalji upit"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Glasovno pretraživanje"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Dijeljenje sa"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Dijeli putem aplikacije <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Sažmi"</string><string msgid="146198913615257606" name="search_menu_title">"Pretraživanje"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-hu\values-hu.xml" qualifiers="hu"><string msgid="*******************" name="abc_action_bar_home_description">"Ugrás a főoldalra"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Felfelé mozgatás"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"További lehetőségek"</string><string msgid="*******************" name="abc_action_mode_done">"Kész"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Összes megtekintése"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Válasszon ki egy alkalmazást"</string><string msgid="121134116657445385" name="abc_capital_off">"KI"</string><string msgid="3405795526292276155" name="abc_capital_on">"BE"</string><string msgid="7723749260725869598" name="abc_search_hint">"Keresés…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Lekérdezés törlése"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Keresési lekérdezés"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Keresés"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Lekérdezés küldése"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Hangalapú keresés"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Megosztás a következővel:"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Megosztás a következő alkalmazással: <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Összecsukás"</string><string msgid="146198913615257606" name="search_menu_title">"Keresés"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-hy\values-hy.xml" qualifiers="hy"><string msgid="*******************" name="abc_action_bar_home_description">"Ուղղվել տուն"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Ուղղվել վերև"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Այլ ընտրանքներ"</string><string msgid="*******************" name="abc_action_mode_done">"Պատրաստ է"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Տեսնել բոլորը"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Ընտրել ծրագիր"</string><string msgid="121134116657445385" name="abc_capital_off">"ԱՆՋԱՏՎԱԾ"</string><string msgid="3405795526292276155" name="abc_capital_on">"ՄԻԱՑՎԱԾ"</string><string msgid="7723749260725869598" name="abc_search_hint">"Որոնում..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Մաքրել հարցումը"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Որոնման հարցում"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Որոնել"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Ուղարկել հարցումը"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Ձայնային որոնում"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Կիսվել"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Ուղարկել <ns1:g id="APPLICATION_NAME">%s</ns1:g>-ին"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Թաքցնել"</string><string msgid="146198913615257606" name="search_menu_title">"Որոնել"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-in\values-in.xml" qualifiers="in"><string msgid="*******************" name="abc_action_bar_home_description">"Navigasi ke beranda"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navigasi naik"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Opsi lain"</string><string msgid="*******************" name="abc_action_mode_done">"Selesai"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Lihat semua"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Pilih aplikasi"</string><string msgid="121134116657445385" name="abc_capital_off">"NONAKTIF"</string><string msgid="3405795526292276155" name="abc_capital_on">"AKTIF"</string><string msgid="7723749260725869598" name="abc_search_hint">"Telusuri..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Hapus kueri"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Kueri penelusuran"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Telusuri"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Kirim kueri"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Penelusuran suara"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Bagikan dengan"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Bagikan ke <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Ciutkan"</string><string msgid="146198913615257606" name="search_menu_title">"Telusuri"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-is\values-is.xml" qualifiers="is"><string msgid="*******************" name="abc_action_bar_home_description">"Fara heim"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Fara upp"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Fleiri valkostir"</string><string msgid="*******************" name="abc_action_mode_done">"Lokið"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Sjá allt"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Veldu forrit"</string><string msgid="121134116657445385" name="abc_capital_off">"SLÖKKT"</string><string msgid="3405795526292276155" name="abc_capital_on">"KVEIKT"</string><string msgid="7723749260725869598" name="abc_search_hint">"Leita…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Hreinsa fyrirspurn"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Leitarfyrirspurn"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Leita"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Senda fyrirspurn"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Raddleit"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Deila með"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Deila með <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Minnka"</string><string msgid="146198913615257606" name="search_menu_title">"Leita"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-it\values-it.xml" qualifiers="it"><string msgid="*******************" name="abc_action_bar_home_description">"Vai alla home page"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Vai in alto"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Altre opzioni"</string><string msgid="*******************" name="abc_action_mode_done">"Fine"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Visualizza tutte"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Scegli un\'applicazione"</string><string msgid="121134116657445385" name="abc_capital_off">"OFF"</string><string msgid="3405795526292276155" name="abc_capital_on">"ON"</string><string msgid="7723749260725869598" name="abc_search_hint">"Cerca…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Cancella query"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Query di ricerca"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Cerca"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Invia query"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Ricerca vocale"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Condividi con"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Condividi tramite <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Comprimi"</string><string msgid="146198913615257606" name="search_menu_title">"Ricerca"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-iw\values-iw.xml" qualifiers="iw"><string msgid="*******************" name="abc_action_bar_home_description">"נווט לדף הבית"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"נווט למעלה"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"עוד אפשרויות"</string><string msgid="*******************" name="abc_action_mode_done">"בוצע"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"ראה הכל"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"בחר אפליקציה"</string><string msgid="121134116657445385" name="abc_capital_off">"כבוי"</string><string msgid="3405795526292276155" name="abc_capital_on">"פועל"</string><string msgid="7723749260725869598" name="abc_search_hint">"חיפוש…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"מחק שאילתה"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"שאילתת חיפוש"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"חיפוש"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"שלח שאילתה"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"חיפוש קולי"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"שתף עם"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"שתף עם <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"כווץ"</string><string msgid="146198913615257606" name="search_menu_title">"חיפוש"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ja\values-ja.xml" qualifiers="ja"><string msgid="*******************" name="abc_action_bar_home_description">"ホームへ移動"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"上へ移動"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"その他のオプション"</string><string msgid="*******************" name="abc_action_mode_done">"完了"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"すべて表示"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"アプリの選択"</string><string msgid="121134116657445385" name="abc_capital_off">"OFF"</string><string msgid="3405795526292276155" name="abc_capital_on">"ON"</string><string msgid="7723749260725869598" name="abc_search_hint">"検索…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"検索キーワードを削除"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"検索キーワード"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"検索"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"検索キーワードを送信"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"音声検索"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"共有"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g>と共有"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"折りたたむ"</string><string msgid="146198913615257606" name="search_menu_title">"検索"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ka\values-ka.xml" qualifiers="ka"><string msgid="*******************" name="abc_action_bar_home_description">"მთავარზე ნავიგაცია"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"ზემოთ ნავიგაცია"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"მეტი ვარიანტები"</string><string msgid="*******************" name="abc_action_mode_done">"დასრულდა"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"ყველას ნახვა"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"აპის არჩევა"</string><string msgid="121134116657445385" name="abc_capital_off">"გამორთულია"</string><string msgid="3405795526292276155" name="abc_capital_on">"ჩართულია"</string><string msgid="7723749260725869598" name="abc_search_hint">"ძიება..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"მოთხოვნის გასუფთავება"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"ძიების მოთხოვნა"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"ძიება"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"მოთხოვნის გადაგზავნა"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"ხმოვანი ძიება"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"გაზიარება:"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"გაუზიარეთ <ns1:g id="APPLICATION_NAME">%s</ns1:g>-ს"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"აკეცვა"</string><string msgid="146198913615257606" name="search_menu_title">"ძიება"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-kk\values-kk.xml" qualifiers="kk"><string msgid="*******************" name="abc_action_bar_home_description">"Негізгі бетте қозғалу"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Жоғары қозғалу"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Басқа опциялар"</string><string msgid="*******************" name="abc_action_mode_done">"Дайын"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Барлығын көру"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Қолданбаны таңдау"</string><string msgid="121134116657445385" name="abc_capital_off">"ӨШІРУЛІ"</string><string msgid="3405795526292276155" name="abc_capital_on">"ҚОСУЛЫ"</string><string msgid="7723749260725869598" name="abc_search_hint">"Іздеу…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Сұрақты жою"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Сұрақты іздеу"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Іздеу"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Сұрақты жіберу"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Дауыс арқылы іздеу"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Бөлісу"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> қолданбасымен бөлісу"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Тасалау"</string><string msgid="146198913615257606" name="search_menu_title">"Іздеу"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-km\values-km.xml" qualifiers="km"><string msgid="*******************" name="abc_action_bar_home_description">"រកមើល​ទៅ​ដើម"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"រកមើល​ឡើងលើ"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"ជម្រើស​ច្រើន​ទៀត"</string><string msgid="*******************" name="abc_action_mode_done">"រួចរាល់"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"មើល​ទាំងអស់"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"ជ្រើស​កម្មវិធី​​"</string><string msgid="121134116657445385" name="abc_capital_off">"បិទ"</string><string msgid="3405795526292276155" name="abc_capital_on">"បើក"</string><string msgid="7723749260725869598" name="abc_search_hint">"ស្វែងរក…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"សម្អាត​សំណួរ"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"ស្វែងរក​សំណួរ"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"ស្វែងរក"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"ដាក់​​​ស្នើ​សំណួរ"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"ការស្វែងរក​សំឡេង"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"ចែករំលែក​ជាមួយ"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"ចែក​រំលែក​ជា​មួយ <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"បង្រួម"</string><string msgid="146198913615257606" name="search_menu_title">"ស្វែងរក"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-kn\values-kn.xml" qualifiers="kn"><string msgid="*******************" name="abc_action_bar_home_description">"ಮುಖಪುಟವನ್ನು ನ್ಯಾವಿಗೇಟ್ ಮಾಡಿ"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"ಮೇಲಕ್ಕೆ ನ್ಯಾವಿಗೇಟ್ ಮಾಡಿ"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"ಇನ್ನಷ್ಟು ಆಯ್ಕೆಗಳು"</string><string msgid="*******************" name="abc_action_mode_done">"ಮುಗಿದಿದೆ"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"ಎಲ್ಲವನ್ನೂ ನೋಡಿ"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"ಒಂದು ಅಪ್ಲಿಕೇಶನ್ ಆಯ್ಕೆಮಾಡಿ"</string><string msgid="121134116657445385" name="abc_capital_off">"ಆಫ್"</string><string msgid="3405795526292276155" name="abc_capital_on">"ಆನ್"</string><string msgid="7723749260725869598" name="abc_search_hint">"ಹುಡುಕಿ…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"ಪ್ರಶ್ನೆಯನ್ನು ತೆರವುಗೊಳಿಸು"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"ಪ್ರಶ್ನೆಯನ್ನು ಹುಡುಕಿ"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"ಹುಡುಕಿ"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"ಪ್ರಶ್ನೆಯನ್ನು ಸಲ್ಲಿಸು"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"ಧ್ವನಿ ಹುಡುಕಾಟ"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"ಇವರೊಂದಿಗೆ ಹಂಚಿಕೊಳ್ಳಿ"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> ನೊಂದಿಗೆ ಹಂಚಿಕೊಳ್ಳಿ"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"ಸಂಕುಚಿಸು"</string><string msgid="146198913615257606" name="search_menu_title">"ಹುಡುಕಿ"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ko\values-ko.xml" qualifiers="ko"><string msgid="*******************" name="abc_action_bar_home_description">"홈 탐색"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"위로 탐색"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"옵션 더보기"</string><string msgid="*******************" name="abc_action_mode_done">"완료"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"전체 보기"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"앱 선택"</string><string msgid="121134116657445385" name="abc_capital_off">"사용 안함"</string><string msgid="3405795526292276155" name="abc_capital_on">"사용"</string><string msgid="7723749260725869598" name="abc_search_hint">"검색..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"검색어 삭제"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"검색어"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"검색"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"검색어 보내기"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"음성 검색"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"공유 대상"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g>와(과) 공유"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"접기"</string><string msgid="146198913615257606" name="search_menu_title">"검색"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ky\values-ky.xml" qualifiers="ky"><string msgid="*******************" name="abc_action_bar_home_description">"Үйгө багыттоо"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Жогору"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Көбүрөөк мүмкүнчүлүктөр"</string><string msgid="*******************" name="abc_action_mode_done">"Даяр"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Бардыгын көрүү"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Колдонмо тандоо"</string><string msgid="121134116657445385" name="abc_capital_off">"ӨЧҮК"</string><string msgid="3405795526292276155" name="abc_capital_on">"КҮЙҮК"</string><string msgid="7723749260725869598" name="abc_search_hint">"Издөө…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Талаптарды тазалоо"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Издөө талаптары"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Издөө"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Талап жөнөтүү"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Үн аркылуу издөө"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Бөлүшүү"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> аркылуу бөлүшүү"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Жыйнап коюу"</string><string msgid="146198913615257606" name="search_menu_title">"Издөө"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-land\values-land.xml" qualifiers="land"><dimen name="abc_action_bar_default_height_material">48dp</dimen><dimen name="abc_action_bar_progress_bar_size">32dp</dimen><dimen name="abc_text_size_subtitle_material_toolbar">12dp</dimen><dimen name="abc_text_size_title_material_toolbar">14dp</dimen></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-large-v4\values-large-v4.xml" qualifiers="large-v4"><dimen name="abc_config_prefDialogWidth">440dp</dimen><item name="abc_dialog_fixed_height_major" type="dimen">60%</item><item name="abc_dialog_fixed_height_minor" type="dimen">90%</item><item name="abc_dialog_fixed_width_major" type="dimen">60%</item><item name="abc_dialog_fixed_width_minor" type="dimen">90%</item><item name="abc_dialog_min_width_major" type="dimen">55%</item><item name="abc_dialog_min_width_minor" type="dimen">80%</item><style name="Base.Theme.AppCompat.DialogWhenLarge" parent="Base.Theme.AppCompat.Dialog.FixedSize"/><style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="Base.Theme.AppCompat.Light.Dialog.FixedSize"/></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ldltr-v21\values-ldltr-v21.xml" qualifiers="ldltr-v21"><style name="Base.Widget.AppCompat.Spinner.Underlined" parent="android:Widget.Material.Spinner.Underlined"/></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-lo\values-lo.xml" qualifiers="lo"><string msgid="*******************" name="abc_action_bar_home_description">"ກັບໄປໜ້າຫຼັກ"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"ຂຶ້ນເທິງ"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"ໂຕເລືອກອື່ນ"</string><string msgid="*******************" name="abc_action_mode_done">"ແລ້ວໆ"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"ເບິ່ງທັງຫມົດ"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"ເລືອກແອັບຯ"</string><string msgid="121134116657445385" name="abc_capital_off">"ປິດ"</string><string msgid="3405795526292276155" name="abc_capital_on">"ເປີດ"</string><string msgid="7723749260725869598" name="abc_search_hint">"ຊອກຫາ"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"ລຶບຂໍ້ຄວາມຊອກຫາ"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"ຊອກຫາ"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"ຊອກຫາ"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"ສົ່ງການຊອກຫາ"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"ຊອກຫາດ້ວຍສຽງ"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"ແບ່ງປັນກັບ"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"ແບ່ງປັນດ້ວຍ <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"ຫຍໍ້"</string><string msgid="146198913615257606" name="search_menu_title">"ຊອກຫາ"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-lt\values-lt.xml" qualifiers="lt"><string msgid="*******************" name="abc_action_bar_home_description">"Eiti į pagrindinį puslapį"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Eiti į viršų"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Daugiau parinkčių"</string><string msgid="*******************" name="abc_action_mode_done">"Atlikta"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Peržiūrėti viską"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Pasirinkti programą"</string><string msgid="121134116657445385" name="abc_capital_off">"IŠJUNGTA"</string><string msgid="3405795526292276155" name="abc_capital_on">"ĮJUNGTI"</string><string msgid="7723749260725869598" name="abc_search_hint">"Ieškoti..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Išvalyti užklausą"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Paieškos užklausa"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Paieška"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Pateikti užklausą"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Paieška balsu"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Bendrinti naudojant"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Bendrinti naudojant programą „<ns1:g id="APPLICATION_NAME">%s</ns1:g>“"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Sutraukti"</string><string msgid="146198913615257606" name="search_menu_title">"Paieška"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-lv\values-lv.xml" qualifiers="lv"><string msgid="*******************" name="abc_action_bar_home_description">"Pārvietoties uz sākuma ekrānu"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Pārvietoties augšup"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Vairāk opciju"</string><string msgid="*******************" name="abc_action_mode_done">"Gatavs"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Skatīt visu"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Izvēlieties lietotni"</string><string msgid="121134116657445385" name="abc_capital_off">"IZSLĒGTS"</string><string msgid="3405795526292276155" name="abc_capital_on">"IESLĒGTS"</string><string msgid="7723749260725869598" name="abc_search_hint">"Meklējiet…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Notīrīt vaicājumu"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Meklēšanas vaicājums"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Meklēt"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Iesniegt vaicājumu"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Meklēšana ar balsi"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Kopīgot ar:"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Kopīgot ar lietojumprogrammu <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Sakļaut"</string><string msgid="146198913615257606" name="search_menu_title">"Meklēt"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-mk\values-mk.xml" qualifiers="mk"><string msgid="*******************" name="abc_action_bar_home_description">"Движи се кон дома"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Движи се нагоре"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Повеќе опции"</string><string msgid="*******************" name="abc_action_mode_done">"Готово"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Види ги сите"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Избери апликација"</string><string msgid="121134116657445385" name="abc_capital_off">"ИСКЛУЧЕНО"</string><string msgid="3405795526292276155" name="abc_capital_on">"ВКЛУЧЕНО"</string><string msgid="7723749260725869598" name="abc_search_hint">"Пребарување…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Исчисти барање"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Пребарај барање"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Пребарај"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Поднеси барање"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Гласовно пребарување"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Сподели со"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Сподели со <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Собери"</string><string msgid="146198913615257606" name="search_menu_title">"Пребарај"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ml\values-ml.xml" qualifiers="ml"><string msgid="*******************" name="abc_action_bar_home_description">"ഹോമിലേക്ക് നാവിഗേറ്റുചെയ്യുക"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"മുകളിലേക്ക് നാവിഗേറ്റുചെയ്യുക"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"കൂടുതൽ‍ ഓപ്‌ഷനുകള്‍"</string><string msgid="*******************" name="abc_action_mode_done">"പൂർത്തിയാക്കി"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"എല്ലാം കാണുക"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"ഒരു അപ്ലിക്കേഷൻ തിരഞ്ഞെടുക്കുക"</string><string msgid="121134116657445385" name="abc_capital_off">"ഓഫ്"</string><string msgid="3405795526292276155" name="abc_capital_on">"ഓൺ"</string><string msgid="7723749260725869598" name="abc_search_hint">"തിരയുക…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"അന്വേഷണം മായ്‌ക്കുക"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"തിരയൽ അന്വേഷണം"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"തിരയൽ"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"അന്വേഷണം സമർപ്പിക്കുക"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"ശബ്ദതിരയൽ"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"ഇവരുമായി പങ്കിടുക"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g>-മായി പങ്കിടുക"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"ചുരുക്കുക"</string><string msgid="146198913615257606" name="search_menu_title">"തിരയുക"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-mn\values-mn.xml" qualifiers="mn"><string msgid="*******************" name="abc_action_bar_home_description">"Нүүр хуудас руу шилжих"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Дээш шилжих"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Нэмэлт сонголтууд"</string><string msgid="*******************" name="abc_action_mode_done">"Дууссан"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Бүгдийг харах"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Апп сонгох"</string><string msgid="121134116657445385" name="abc_capital_off">"ИДЭВХГҮЙ"</string><string msgid="3405795526292276155" name="abc_capital_on">"ИДЭВХТЭЙ"</string><string msgid="7723749260725869598" name="abc_search_hint">"Хайх..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Асуулгыг цэвэрлэх"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Хайх асуулга"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Хайх"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Асуулгыг илгээх"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Дуут хайлт"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Хуваалцах"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g>-тай хуваалцах"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Хумих"</string><string msgid="146198913615257606" name="search_menu_title">"Хайлт"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-mr\values-mr.xml" qualifiers="mr"><string msgid="*******************" name="abc_action_bar_home_description">"होमवर नेव्‍हिगेट करा"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"वर नेव्‍हिगेट करा"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"अधिक पर्याय"</string><string msgid="*******************" name="abc_action_mode_done">"पूर्ण झाले"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"सर्व पहा"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"एक अ‍ॅप निवडा"</string><string msgid="121134116657445385" name="abc_capital_off">"बंद"</string><string msgid="3405795526292276155" name="abc_capital_on">"चालू"</string><string msgid="7723749260725869598" name="abc_search_hint">"शोधा…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"क्‍वेरी स्‍पष्‍ट करा"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"शोध क्वेरी"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"शोध"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"क्वेरी सबमिट करा"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"व्हॉइस शोध"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"यांच्यासह सामायिक करा"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> सह शेअर करा"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"संक्षिप्त करा"</string><string msgid="146198913615257606" name="search_menu_title">"शोधा"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ms\values-ms.xml" qualifiers="ms"><string msgid="*******************" name="abc_action_bar_home_description">"Navigasi skrin utama"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navigasi ke atas"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Lagi pilihan"</string><string msgid="*******************" name="abc_action_mode_done">"Selesai"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Lihat semua"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Pilih apl"</string><string msgid="121134116657445385" name="abc_capital_off">"MATI"</string><string msgid="3405795526292276155" name="abc_capital_on">"HIDUP"</string><string msgid="7723749260725869598" name="abc_search_hint">"Cari…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Kosongkan pertanyaan"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Pertanyaan carian"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Cari"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Serah pertanyaan"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Carian suara"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Kongsi dengan"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Kongsi dengan <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Runtuhkan"</string><string msgid="146198913615257606" name="search_menu_title">"Cari"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-my\values-my.xml" qualifiers="my"><string msgid="*******************" name="abc_action_bar_home_description">"မူလနေရာကို သွားရန်"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"အပေါ်သို့သွားရန်"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"ပိုမိုရွေးချယ်စရာများ"</string><string msgid="*******************" name="abc_action_mode_done">"ပြီးဆုံးပါပြီ"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"အားလုံးကို ကြည့်ရန်"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"အက်ပ်တစ်ခုခုကို ရွေးချယ်ပါ"</string><string msgid="121134116657445385" name="abc_capital_off">"ပိတ်"</string><string msgid="3405795526292276155" name="abc_capital_on">"ဖွင့်"</string><string msgid="7723749260725869598" name="abc_search_hint">"ရှာဖွေပါ..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"ရှာစရာ အချက်အလက်များ ဖယ်ရှားရန်"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"ရှာစရာ အချက်အလက်နေရာ"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"ရှာဖွေရန်"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"ရှာဖွေစရာ အချက်အလက်ကို ပေးပို့ရန်"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"အသံဖြင့် ရှာဖွေခြင်း"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"မျှဝေဖို့ ရွေးပါ"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> ဖြင့် မျှဝေရန်"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"ခေါက်ရန်"</string><string msgid="146198913615257606" name="search_menu_title">"ရှာဖွေပါ"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-nb\values-nb.xml" qualifiers="nb"><string msgid="*******************" name="abc_action_bar_home_description">"Gå til startsiden"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Gå opp"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Flere alternativer"</string><string msgid="*******************" name="abc_action_mode_done">"Ferdig"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Se alle"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Velg en app"</string><string msgid="121134116657445385" name="abc_capital_off">"AV"</string><string msgid="3405795526292276155" name="abc_capital_on">"PÅ"</string><string msgid="7723749260725869598" name="abc_search_hint">"Søk …"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Slett søket"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Søkeord"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Søk"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Utfør søket"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Talesøk"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Del med"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Del med <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Skjul"</string><string msgid="146198913615257606" name="search_menu_title">"Søk"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ne\values-ne.xml" qualifiers="ne"><string msgid="*******************" name="abc_action_bar_home_description">"गृह खोज्नुहोस्"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"माथि खोज्नुहोस्"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"थप विकल्पहरू"</string><string msgid="*******************" name="abc_action_mode_done">"सम्पन्न भयो"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"सबै हेर्नुहोस्"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"एउटा अनुप्रयोग छान्नुहोस्"</string><string msgid="121134116657445385" name="abc_capital_off">"निष्क्रिय पार्नुहोस्"</string><string msgid="3405795526292276155" name="abc_capital_on">"सक्रिय गर्नुहोस्"</string><string msgid="7723749260725869598" name="abc_search_hint">"खोज्नुहोस्..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"प्रश्‍न हटाउनुहोस्"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"जिज्ञासाको खोज गर्नुहोस्"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"खोज्नुहोस्"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"जिज्ञासा पेस गर्नुहोस्"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"भ्वाइस खोजी"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"साझेदारी गर्नुहोस्..."</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> सँग आदान-प्रदान गर्नुहोस्"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"संक्षिप्त पार्नुहोस्"</string><string msgid="146198913615257606" name="search_menu_title">"खोज्नुहोस्"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-night-v8\values-night-v8.xml" qualifiers="night-v8"><style name="Theme.AppCompat.DayNight" parent="Theme.AppCompat"/><style name="Theme.AppCompat.DayNight.DarkActionBar" parent="Theme.AppCompat"/><style name="Theme.AppCompat.DayNight.Dialog" parent="Theme.AppCompat.Dialog"/><style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="Theme.AppCompat.Dialog.Alert"/><style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="Theme.AppCompat.Dialog.MinWidth"/><style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="Theme.AppCompat.DialogWhenLarge"/><style name="Theme.AppCompat.DayNight.NoActionBar" parent="Theme.AppCompat.NoActionBar"/></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-nl\values-nl.xml" qualifiers="nl"><string msgid="*******************" name="abc_action_bar_home_description">"Navigeren naar startpositie"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Omhoog navigeren"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Meer opties"</string><string msgid="*******************" name="abc_action_mode_done">"Gereed"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Alles weergeven"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Een app selecteren"</string><string msgid="121134116657445385" name="abc_capital_off">"UIT"</string><string msgid="3405795526292276155" name="abc_capital_on">"AAN"</string><string msgid="7723749260725869598" name="abc_search_hint">"Zoeken…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Zoekopdracht wissen"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Zoekopdracht"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Zoeken"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Zoekopdracht verzenden"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Gesproken zoekopdracht"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Delen met"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Delen met <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Samenvouwen"</string><string msgid="146198913615257606" name="search_menu_title">"Zoeken"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-pa\values-pa.xml" qualifiers="pa"><string msgid="*******************" name="abc_action_bar_home_description">"ਹੋਮ \'ਤੇ ਜਾਓ"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"ਉੱਪਰ ਜਾਓ"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"ਹੋਰ ਚੋਣਾਂ"</string><string msgid="*******************" name="abc_action_mode_done">"ਹੋ ਗਿਆ"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"ਸਭ ਦੇਖੋ"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"ਇੱਕ ਐਪ ਚੁਣੋ"</string><string msgid="121134116657445385" name="abc_capital_off">"ਬੰਦ"</string><string msgid="3405795526292276155" name="abc_capital_on">"ਤੇ"</string><string msgid="7723749260725869598" name="abc_search_hint">"ਖੋਜ…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"ਸਵਾਲ ਹਟਾਓ"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"ਖੋਜ ਪੁੱਛਗਿੱਛ"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"ਖੋਜੋ"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"ਸਵਾਲ ਪ੍ਰਸਤੁਤ ਕਰੋ"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"ਵੌਇਸ ਖੋਜ"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"ਇਸ ਨਾਲ ਸਾਂਝਾ ਕਰੋ"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> ਨਾਲ ਸਾਂਝਾ ਕਰੋ"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"ਨਸ਼ਟ ਕਰੋ"</string><string msgid="146198913615257606" name="search_menu_title">"ਖੋਜੋ"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-pl\values-pl.xml" qualifiers="pl"><string msgid="*******************" name="abc_action_bar_home_description">"Przejdź do strony głównej"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Przejdź wyżej"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Więcej opcji"</string><string msgid="*******************" name="abc_action_mode_done">"Gotowe"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Zobacz wszystkie"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Wybierz aplikację"</string><string msgid="121134116657445385" name="abc_capital_off">"WYŁ."</string><string msgid="3405795526292276155" name="abc_capital_on">"WŁ."</string><string msgid="7723749260725869598" name="abc_search_hint">"Szukaj…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Wyczyść zapytanie"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Wyszukiwane hasło"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Szukaj"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Wyślij zapytanie"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Wyszukiwanie głosowe"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Udostępnij dla"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Udostępnij przez: <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Zwiń"</string><string msgid="146198913615257606" name="search_menu_title">"Szukaj"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-port\values-port.xml" qualifiers="port"><bool name="abc_action_bar_embed_tabs">false</bool></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-pt\values-pt.xml" qualifiers="pt"><string msgid="*******************" name="abc_action_bar_home_description">"Navegar para a página inicial"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navegar para cima"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Mais opções"</string><string msgid="*******************" name="abc_action_mode_done">"Concluído"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Ver tudo"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Selecione um app"</string><string msgid="121134116657445385" name="abc_capital_off">"DESATIVAR"</string><string msgid="3405795526292276155" name="abc_capital_on">"ATIVAR"</string><string msgid="7723749260725869598" name="abc_search_hint">"Pesquisar..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Limpar consulta"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Consulta de pesquisa"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Pesquisar"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Enviar consulta"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Pesquisa por voz"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Compartilhar com"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Compartilhar com <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Recolher"</string><string msgid="146198913615257606" name="search_menu_title">"Pesquisar"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-pt-rBR\values-pt-rBR.xml" qualifiers="pt-rBR"><string msgid="*******************" name="abc_action_bar_home_description">"Navegar para a página inicial"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navegar para cima"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Mais opções"</string><string msgid="*******************" name="abc_action_mode_done">"Concluído"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Ver tudo"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Selecione um app"</string><string msgid="121134116657445385" name="abc_capital_off">"DESATIVAR"</string><string msgid="3405795526292276155" name="abc_capital_on">"ATIVAR"</string><string msgid="7723749260725869598" name="abc_search_hint">"Pesquisar..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Limpar consulta"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Consulta de pesquisa"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Pesquisar"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Enviar consulta"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Pesquisa por voz"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Compartilhar com"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Compartilhar com <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Recolher"</string><string msgid="146198913615257606" name="search_menu_title">"Pesquisar"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-pt-rPT\values-pt-rPT.xml" qualifiers="pt-rPT"><string msgid="*******************" name="abc_action_bar_home_description">"Navegar para a página inicial"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navegar para cima"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Mais opções"</string><string msgid="*******************" name="abc_action_mode_done">"Concluído"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Ver tudo"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Escolher uma aplicação"</string><string msgid="121134116657445385" name="abc_capital_off">"DESATIVADO"</string><string msgid="3405795526292276155" name="abc_capital_on">"ATIVADO"</string><string msgid="7723749260725869598" name="abc_search_hint">"Pesquisar..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Limpar consulta"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Consulta de pesquisa"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Pesquisar"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Enviar consulta"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Pesquisa por voz"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Partilhar com"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Partilhar com a aplicação <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Reduzir"</string><string msgid="146198913615257606" name="search_menu_title">"Pesquisar"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ro\values-ro.xml" qualifiers="ro"><string msgid="*******************" name="abc_action_bar_home_description">"Navigați la ecranul de pornire"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navigați în sus"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Mai multe opțiuni"</string><string msgid="*******************" name="abc_action_mode_done">"Terminat"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Afișați-le pe toate"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Alegeți o aplicație"</string><string msgid="121134116657445385" name="abc_capital_off">"DEZACTIVAȚI"</string><string msgid="3405795526292276155" name="abc_capital_on">"ACTIVAT"</string><string msgid="7723749260725869598" name="abc_search_hint">"Căutați…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Ștergeți interogarea"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Interogare de căutare"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Căutați"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Trimiteți interogarea"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Căutare vocală"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Trimiteți la"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Trimiteți folosind <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Restrângeți"</string><string msgid="146198913615257606" name="search_menu_title">"Căutați"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ru\values-ru.xml" qualifiers="ru"><string msgid="*******************" name="abc_action_bar_home_description">"Перейти на главный экран"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Перейти вверх"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Другие параметры"</string><string msgid="*******************" name="abc_action_mode_done">"Готово"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Показать все"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Выбрать приложение"</string><string msgid="121134116657445385" name="abc_capital_off">"ОТКЛ."</string><string msgid="3405795526292276155" name="abc_capital_on">"ВКЛ."</string><string msgid="7723749260725869598" name="abc_search_hint">"Поиск"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Удалить запрос"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Поисковый запрос"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Поиск"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Отправить запрос"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Голосовой поиск"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Открыть доступ"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Открыть доступ приложению \"<ns1:g id="APPLICATION_NAME">%s</ns1:g>\""</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Свернуть"</string><string msgid="146198913615257606" name="search_menu_title">"Поиск"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-si\values-si.xml" qualifiers="si"><string msgid="*******************" name="abc_action_bar_home_description">"ගෙදරට සංචාලනය කරන්න"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"ඉහලට සංචාලනය කරන්න"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"තවත් විකල්ප"</string><string msgid="*******************" name="abc_action_mode_done">"අවසාන වූ"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"සියල්ල බලන්න"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"යෙදුමක් තෝරන්න"</string><string msgid="121134116657445385" name="abc_capital_off">"ක්‍රියාවිරහිතයි"</string><string msgid="3405795526292276155" name="abc_capital_on">"ක්‍රියාත්මකයි"</string><string msgid="7723749260725869598" name="abc_search_hint">"සොයන්න..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"විමසුම හිස් කරන්න"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"සෙවුම් විමසුම"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"සෙවීම"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"විමසුම යොමු කරන්න"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"හඬ සෙවීම"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"සමඟ බෙදාගන්න"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> සමඟ බෙදා ගන්න"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"හකුළන්න"</string><string msgid="146198913615257606" name="search_menu_title">"සොයන්න"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-sk\values-sk.xml" qualifiers="sk"><string msgid="*******************" name="abc_action_bar_home_description">"Prejsť na plochu"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Prejsť hore"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Ďalšie možnosti"</string><string msgid="*******************" name="abc_action_mode_done">"Hotovo"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Zobraziť všetko"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Zvoľte aplikáciu"</string><string msgid="121134116657445385" name="abc_capital_off">"VYPNUTÉ"</string><string msgid="3405795526292276155" name="abc_capital_on">"ZAPNUTÉ"</string><string msgid="7723749260725869598" name="abc_search_hint">"Vyhľadať…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Vymazať dopyt"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Vyhľadávací dopyt"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Hľadať"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Odoslať dopyt"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Hlasové vyhľadávanie"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Zdieľať pomocou"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Zdieľať s aplikáciou <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Zbaliť"</string><string msgid="146198913615257606" name="search_menu_title">"Vyhľadávanie"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-sl\values-sl.xml" qualifiers="sl"><string msgid="*******************" name="abc_action_bar_home_description">"Krmarjenje domov"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Krmarjenje navzgor"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Več možnosti"</string><string msgid="*******************" name="abc_action_mode_done">"Končano"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Pokaži vse"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Izbira aplikacije"</string><string msgid="121134116657445385" name="abc_capital_off">"IZKLOPLJENO"</string><string msgid="3405795526292276155" name="abc_capital_on">"VKLOPLJENO"</string><string msgid="7723749260725869598" name="abc_search_hint">"Iskanje …"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Izbris poizvedbe"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Iskalna poizvedba"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Iskanje"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Pošiljanje poizvedbe"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Glasovno iskanje"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Deljenje z"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Deljenje z drugimi prek aplikacije <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Strni"</string><string msgid="146198913615257606" name="search_menu_title">"Iskanje"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-sq\values-sq.xml" qualifiers="sq"><string msgid="*******************" name="abc_action_bar_home_description">"Orientohu për në shtëpi"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Ngjitu lart"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Opsione të tjera"</string><string msgid="*******************" name="abc_action_mode_done">"U krye!"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Shikoji të gjitha"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Zgjidh një aplikacion"</string><string msgid="121134116657445385" name="abc_capital_off">"JOAKTIV"</string><string msgid="3405795526292276155" name="abc_capital_on">"AKTIV"</string><string msgid="7723749260725869598" name="abc_search_hint">"Kërko..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Pastro pyetjen"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Kërko pyetjen"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Kërko"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Dërgo pyetjen"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Kërkim me zë"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Shpërnda publikisht me"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Ndaje me <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Shpalos"</string><string msgid="146198913615257606" name="search_menu_title">"Kërko"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-sr\values-sr.xml" qualifiers="sr"><string msgid="*******************" name="abc_action_bar_home_description">"Одлазак на Почетну"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Кретање нагоре"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Још опција"</string><string msgid="*******************" name="abc_action_mode_done">"Готово"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Прикажи све"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Избор апликације"</string><string msgid="121134116657445385" name="abc_capital_off">"ИСКЉУЧИ"</string><string msgid="3405795526292276155" name="abc_capital_on">"УКЉУЧИ"</string><string msgid="7723749260725869598" name="abc_search_hint">"Претражите..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Брисање упита"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Упит за претрагу"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Претрага"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Слање упита"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Гласовна претрага"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Дели са"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Дељење са апликацијом <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Скупи"</string><string msgid="146198913615257606" name="search_menu_title">"Претражи"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-sv\values-sv.xml" qualifiers="sv"><string msgid="*******************" name="abc_action_bar_home_description">"Visa startsidan"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Navigera uppåt"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Fler alternativ"</string><string msgid="*******************" name="abc_action_mode_done">"Klart"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Visa alla"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Välj en app"</string><string msgid="121134116657445385" name="abc_capital_off">"AV"</string><string msgid="3405795526292276155" name="abc_capital_on">"PÅ"</string><string msgid="7723749260725869598" name="abc_search_hint">"Sök …"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Ta bort frågan"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Sökfråga"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Sök"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Skicka fråga"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Röstsökning"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Dela med"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Dela med <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Komprimera"</string><string msgid="146198913615257606" name="search_menu_title">"Sök"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-sw\values-sw.xml" qualifiers="sw"><string msgid="*******************" name="abc_action_bar_home_description">"Nenda mwanzo"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Nenda juu"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Chaguo zaidi"</string><string msgid="*******************" name="abc_action_mode_done">"Nimemaliza"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Angalia zote"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Chagua programu"</string><string msgid="121134116657445385" name="abc_capital_off">"IMEZIMWA"</string><string msgid="3405795526292276155" name="abc_capital_on">"IMEWASHWA"</string><string msgid="7723749260725869598" name="abc_search_hint">"Tafuta…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Futa hoja"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Hoja ya utafutaji"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Tafuta"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Wasilisha hoja"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Tafuta kwa kutamka"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Shiriki na:"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Shiriki ukitumia <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Kunja"</string><string msgid="146198913615257606" name="search_menu_title">"Tafuta"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-sw600dp-v13\values-sw600dp-v13.xml" qualifiers="sw600dp-v13"><dimen name="abc_action_bar_content_inset_material">24dp</dimen><dimen name="abc_action_bar_content_inset_with_nav">80dp</dimen><dimen name="abc_action_bar_default_height_material">64dp</dimen><dimen name="abc_action_bar_default_padding_end_material">8dp</dimen><dimen name="abc_action_bar_default_padding_start_material">8dp</dimen><dimen name="abc_config_prefDialogWidth">580dp</dimen><dimen name="abc_text_size_subtitle_material_toolbar">16dp</dimen><dimen name="abc_text_size_title_material_toolbar">20dp</dimen></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ta\values-ta.xml" qualifiers="ta"><string msgid="*******************" name="abc_action_bar_home_description">"முகப்பிற்கு வழிசெலுத்து"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"மேலே வழிசெலுத்து"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"மேலும் விருப்பங்கள்"</string><string msgid="*******************" name="abc_action_mode_done">"முடிந்தது"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"எல்லாம் காட்டு"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"பயன்பாட்டைத் தேர்வுசெய்க"</string><string msgid="121134116657445385" name="abc_capital_off">"ஆஃப்"</string><string msgid="3405795526292276155" name="abc_capital_on">"ஆன்"</string><string msgid="7723749260725869598" name="abc_search_hint">"தேடு..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"வினவலை அழி"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"தேடல் வினவல்"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"தேடு"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"வினவலைச் சமர்ப்பி"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"குரல் தேடல்"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"இதனுடன் பகிர்"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> மூலம் பகிர்"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"சுருக்கு"</string><string msgid="146198913615257606" name="search_menu_title">"தேடு"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-te\values-te.xml" qualifiers="te"><string msgid="*******************" name="abc_action_bar_home_description">"హోమ్‌కు నావిగేట్ చేయండి"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"పైకి నావిగేట్ చేయండి"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"మరిన్ని ఎంపికలు"</string><string msgid="*******************" name="abc_action_mode_done">"పూర్తయింది"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"అన్నీ చూడండి"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"అనువర్తనాన్ని ఎంచుకోండి"</string><string msgid="121134116657445385" name="abc_capital_off">"ఆఫ్ చేయి"</string><string msgid="3405795526292276155" name="abc_capital_on">"ఆన్ చేయి"</string><string msgid="7723749260725869598" name="abc_search_hint">"వెతుకు..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"ప్రశ్నను క్లియర్ చేయి"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"ప్రశ్న శోధించండి"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"వెతుకు"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"ప్రశ్నని సమర్పించు"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"వాయిస్ శోధన"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"వీరితో భాగస్వామ్యం చేయి"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g>తో భాగస్వామ్యం చేయండి"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"కుదించండి"</string><string msgid="146198913615257606" name="search_menu_title">"వెతుకు"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-th\values-th.xml" qualifiers="th"><string msgid="*******************" name="abc_action_bar_home_description">"นำทางไปหน้าแรก"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"นำทางขึ้น"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"ตัวเลือกอื่น"</string><string msgid="*******************" name="abc_action_mode_done">"เสร็จสิ้น"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"ดูทั้งหมด"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"เลือกแอป"</string><string msgid="121134116657445385" name="abc_capital_off">"ปิด"</string><string msgid="3405795526292276155" name="abc_capital_on">"เปิด"</string><string msgid="7723749260725869598" name="abc_search_hint">"ค้นหา…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"ล้างข้อความค้นหา"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"ข้อความค้นหา"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"ค้นหา"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"ส่งข้อความค้นหา"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"ค้นหาด้วยเสียง"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"แชร์กับ"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"แชร์ทาง <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"ยุบ"</string><string msgid="146198913615257606" name="search_menu_title">"ค้นหา"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-tl\values-tl.xml" qualifiers="tl"><string msgid="*******************" name="abc_action_bar_home_description">"Mag-navigate patungo sa home"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Mag-navigate pataas"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Higit pang mga opsyon"</string><string msgid="*******************" name="abc_action_mode_done">"Tapos na"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Tingnan lahat"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Pumili ng isang app"</string><string msgid="121134116657445385" name="abc_capital_off">"I-OFF"</string><string msgid="3405795526292276155" name="abc_capital_on">"I-ON"</string><string msgid="7723749260725869598" name="abc_search_hint">"Maghanap…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"I-clear ang query"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Query sa paghahanap"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Maghanap"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Isumite ang query"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Paghahanap gamit ang boses"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Ibahagi sa/kay"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Ibahagi gamit ang <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"I-collapse"</string><string msgid="146198913615257606" name="search_menu_title">"Maghanap"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-tr\values-tr.xml" qualifiers="tr"><string msgid="*******************" name="abc_action_bar_home_description">"Ana ekrana git"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Yukarı git"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Diğer seçenekler"</string><string msgid="*******************" name="abc_action_mode_done">"Bitti"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Tümünü göster"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Bir uygulama seçin"</string><string msgid="121134116657445385" name="abc_capital_off">"KAPAT"</string><string msgid="3405795526292276155" name="abc_capital_on">"AÇ"</string><string msgid="7723749260725869598" name="abc_search_hint">"Ara…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Sorguyu temizle"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Arama sorgusu"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Ara"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Sorguyu gönder"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Sesli arama"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Şununla paylaş"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> ile paylaş"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Daralt"</string><string msgid="146198913615257606" name="search_menu_title">"Ara"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-uk\values-uk.xml" qualifiers="uk"><string msgid="*******************" name="abc_action_bar_home_description">"Перейти на головний"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Перейти вгору"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Інші опції"</string><string msgid="*******************" name="abc_action_mode_done">"Готово"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Переглянути всі"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Вибрати програму"</string><string msgid="121134116657445385" name="abc_capital_off">"ВИМК."</string><string msgid="3405795526292276155" name="abc_capital_on">"УВІМК."</string><string msgid="7723749260725869598" name="abc_search_hint">"Пошук…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Очистити запит"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Пошуковий запит"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Пошук"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Надіслати запит"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Голосовий пошук"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Надіслати через"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Поділитися через додаток <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Згорнути"</string><string msgid="146198913615257606" name="search_menu_title">"Пошук"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-ur\values-ur.xml" qualifiers="ur"><string msgid="*******************" name="abc_action_bar_home_description">"ہوم پر نیویگیٹ کریں"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"اوپر نیویگیٹ کریں"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"مزید اختیارات"</string><string msgid="*******************" name="abc_action_mode_done">"ہو گیا"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"سبھی دیکھیں"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"ایک ایپ منتخب کریں"</string><string msgid="121134116657445385" name="abc_capital_off">"آف"</string><string msgid="3405795526292276155" name="abc_capital_on">"آن"</string><string msgid="7723749260725869598" name="abc_search_hint">"تلاش کریں…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"استفسار صاف کریں"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"استفسار تلاش کریں"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"تلاش کریں"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"استفسار جمع کرائیں"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"صوتی تلاش"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"اشتراک کریں مع"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> کے ساتھ اشتراک کریں"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"سکیڑیں"</string><string msgid="146198913615257606" name="search_menu_title">"تلاش"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-uz\values-uz.xml" qualifiers="uz"><string msgid="*******************" name="abc_action_bar_home_description">"Boshiga o‘tish"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Yuqoriga o‘tish"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Boshqa parametrlar"</string><string msgid="*******************" name="abc_action_mode_done">"Tayyor"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Barchasini ko‘rish"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Dastur tanlang"</string><string msgid="121134116657445385" name="abc_capital_off">"O‘CHIQ"</string><string msgid="3405795526292276155" name="abc_capital_on">"YONIQ"</string><string msgid="7723749260725869598" name="abc_search_hint">"Qidirish…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"So‘rovni tozalash"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"So‘rovni izlash"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Qidirish"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"So‘rov yaratish"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Ovozli qidiruv"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Ruxsat berish"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"<ns1:g id="APPLICATION_NAME">%s</ns1:g> orqali ulashish"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Yig‘ish"</string><string msgid="146198913615257606" name="search_menu_title">"Qidirish"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-v11\values-v11.xml" qualifiers="v11"><style name="Base.TextAppearance.AppCompat.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Large.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Medium.Inverse">
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Small.Inverse">
        <item name="android:textColor">?android:attr/textColorTertiaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style><style name="Base.Theme.AppCompat.Dialog" parent="Base.V11.Theme.AppCompat.Dialog"/><style name="Base.Theme.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.Theme.AppCompat.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.Theme.AppCompat.Light.Dialog" parent="Base.V11.Theme.AppCompat.Light.Dialog"/><style name="Base.Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.Theme.AppCompat.Light.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.ThemeOverlay.AppCompat.Dialog" parent="Base.V11.ThemeOverlay.AppCompat.Dialog"/><style name="Base.ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.V11.Theme.AppCompat.Dialog" parent="Base.V7.Theme.AppCompat.Dialog">
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">@bool/abc_config_closeDialogWhenTouchOutside</item>
    </style><style name="Base.V11.Theme.AppCompat.Light.Dialog" parent="Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">@bool/abc_config_closeDialogWhenTouchOutside</item>
    </style><style name="Base.V11.ThemeOverlay.AppCompat.Dialog" parent="Base.V7.ThemeOverlay.AppCompat.Dialog">
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">@bool/abc_config_closeDialogWhenTouchOutside</item>
    </style><style name="Base.Widget.AppCompat.ProgressBar" parent="android:Widget.Holo.ProgressBar">
    </style><style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="android:Widget.Holo.ProgressBar.Horizontal">
    </style><style name="Platform.AppCompat" parent="Platform.V11.AppCompat"/><style name="Platform.AppCompat.Light" parent="Platform.V11.AppCompat.Light"/><style name="Platform.V11.AppCompat" parent="android:Theme.Holo">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>

        
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_dark</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_dark</item>

        
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_light</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>

        
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>

        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_dark</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_dark</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_dark</item>
    </style><style name="Platform.V11.AppCompat.Light" parent="android:Theme.Holo.Light">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>

        
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_light</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_light</item>

        
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_dark</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>

        
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>

        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_light</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_light</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_light</item>
    </style><style name="Platform.Widget.AppCompat.Spinner" parent="android:Widget.Holo.Spinner"/></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-v12\values-v12.xml" qualifiers="v12"><style name="Base.V12.Widget.AppCompat.AutoCompleteTextView" parent="Base.V7.Widget.AppCompat.AutoCompleteTextView">
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style><style name="Base.V12.Widget.AppCompat.EditText" parent="Base.V7.Widget.AppCompat.EditText">
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style><style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="Base.V12.Widget.AppCompat.AutoCompleteTextView"/><style name="Base.Widget.AppCompat.EditText" parent="Base.V12.Widget.AppCompat.EditText"/></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-v13\values-v13.xml" qualifiers="v13"><bool name="abc_allow_stacked_button_bar">false</bool></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-v14\values-v14.xml" qualifiers="v14"><style name="Base.TextAppearance.AppCompat.Button">
        <item name="android:textSize">@dimen/abc_text_size_button_material</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Platform.AppCompat" parent="Platform.V14.AppCompat"/><style name="Platform.AppCompat.Light" parent="Platform.V14.AppCompat.Light"/><style name="Platform.V14.AppCompat" parent="Platform.V11.AppCompat">
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style><style name="Platform.V14.AppCompat.Light" parent="Platform.V11.AppCompat.Light">
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-v16\values-v16.xml" qualifiers="v16"><style name="TextAppearance.AppCompat.Tooltip">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">14sp</item>
    </style></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-v17\values-v17.xml" qualifiers="v17"><style name="RtlOverlay.DialogWindowTitle.AppCompat" parent="Base.DialogWindowTitle.AppCompat">
        <item name="android:textAlignment">viewStart</item>
    </style><style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="android:Widget">
        <item name="android:layout_gravity">center_vertical|start</item>
        <item name="android:paddingEnd">8dp</item>
    </style><style name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" parent="android:Widget">
        <item name="android:layout_marginEnd">8dp</item>
    </style><style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="android:Widget">
        <item name="android:paddingEnd">16dp</item>
    </style><style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="android:Widget">
        <item name="android:layout_marginStart">16dp</item>
    </style><style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="android:Widget">
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:textAlignment">viewStart</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="android:Widget">
        <item name="android:paddingStart">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingEnd">4dp</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="android:Widget">
        <item name="android:layout_alignParentStart">true</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="android:Widget">
        <item name="android:layout_toStartOf">@id/edit_query</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="android:Widget">
        <item name="android:layout_alignParentEnd">true</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toStartOf">@android:id/icon2</item>
        <item name="android:layout_toEndOf">@android:id/icon1</item>
    </style><style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="android:Widget">
        <item name="android:layout_marginStart">@dimen/abc_dropdownitem_text_padding_left</item>
    </style><style name="RtlUnderlay.Widget.AppCompat.ActionButton" parent="android:Widget">
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
    </style><style name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingStart">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingEnd">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-v18\values-v18.xml" qualifiers="v18"><dimen name="abc_switch_padding">0px</dimen></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-v21\values-v21.xml" qualifiers="v21"><style name="Base.TextAppearance.AppCompat" parent="android:TextAppearance.Material"/><style name="Base.TextAppearance.AppCompat.Body1" parent="android:TextAppearance.Material.Body1"/><style name="Base.TextAppearance.AppCompat.Body2" parent="android:TextAppearance.Material.Body2"/><style name="Base.TextAppearance.AppCompat.Button" parent="android:TextAppearance.Material.Button"/><style name="Base.TextAppearance.AppCompat.Caption" parent="android:TextAppearance.Material.Caption"/><style name="Base.TextAppearance.AppCompat.Display1" parent="android:TextAppearance.Material.Display1"/><style name="Base.TextAppearance.AppCompat.Display2" parent="android:TextAppearance.Material.Display2"/><style name="Base.TextAppearance.AppCompat.Display3" parent="android:TextAppearance.Material.Display3"/><style name="Base.TextAppearance.AppCompat.Display4" parent="android:TextAppearance.Material.Display4"/><style name="Base.TextAppearance.AppCompat.Headline" parent="android:TextAppearance.Material.Headline"/><style name="Base.TextAppearance.AppCompat.Inverse" parent="android:TextAppearance.Material.Inverse"/><style name="Base.TextAppearance.AppCompat.Large" parent="android:TextAppearance.Material.Large"/><style name="Base.TextAppearance.AppCompat.Large.Inverse" parent="android:TextAppearance.Material.Large.Inverse"/><style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="android:TextAppearance.Material.Widget.PopupMenu.Large">
    </style><style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="android:TextAppearance.Material.Widget.PopupMenu.Small">
    </style><style name="Base.TextAppearance.AppCompat.Medium" parent="android:TextAppearance.Material.Medium"/><style name="Base.TextAppearance.AppCompat.Medium.Inverse" parent="android:TextAppearance.Material.Medium.Inverse"/><style name="Base.TextAppearance.AppCompat.Menu" parent="android:TextAppearance.Material.Menu"/><style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" parent="android:TextAppearance.Material.SearchResult.Subtitle">
    </style><style name="Base.TextAppearance.AppCompat.SearchResult.Title" parent="android:TextAppearance.Material.SearchResult.Title">
    </style><style name="Base.TextAppearance.AppCompat.Small" parent="android:TextAppearance.Material.Small"/><style name="Base.TextAppearance.AppCompat.Small.Inverse" parent="android:TextAppearance.Material.Small.Inverse"/><style name="Base.TextAppearance.AppCompat.Subhead" parent="android:TextAppearance.Material.Subhead"/><style name="Base.TextAppearance.AppCompat.Title" parent="android:TextAppearance.Material.Title"/><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="android:TextAppearance.Material.Widget.ActionBar.Subtitle">
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="android:TextAppearance.Material.Widget.ActionBar.Subtitle.Inverse">
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="android:TextAppearance.Material.Widget.ActionBar.Title">
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="android:TextAppearance.Material.Widget.ActionBar.Title.Inverse">
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="android:TextAppearance.Material.Widget.ActionMode.Subtitle">
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="android:TextAppearance.Material.Widget.ActionMode.Title">
    </style><style name="Base.TextAppearance.AppCompat.Widget.Button" parent="android:TextAppearance.Material.Widget.Button"/><style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="TextAppearance.AppCompat">
        <item name="android:fontFamily">@string/abc_font_family_title_material</item>
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="android:TextAppearance.Material.Widget.PopupMenu.Large">
    </style><style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="android:TextAppearance.Material.Widget.PopupMenu.Small">
    </style><style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="android:TextAppearance.Material.Button"/><style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="android:TextAppearance.Material.Widget.TextView.SpinnerItem"/><style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="android:TextAppearance.Material.Widget.ActionBar.Subtitle">
    </style><style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="android:TextAppearance.Material.Widget.ActionBar.Title">
    </style><style name="Base.Theme.AppCompat" parent="Base.V21.Theme.AppCompat"/><style name="Base.Theme.AppCompat.Dialog" parent="Base.V21.Theme.AppCompat.Dialog"/><style name="Base.Theme.AppCompat.Light" parent="Base.V21.Theme.AppCompat.Light"/><style name="Base.Theme.AppCompat.Light.Dialog" parent="Base.V21.Theme.AppCompat.Light.Dialog"/><style name="Base.ThemeOverlay.AppCompat.Dialog" parent="Base.V21.ThemeOverlay.AppCompat.Dialog"/><style name="Base.V21.Theme.AppCompat" parent="Base.V7.Theme.AppCompat">
        
        <item name="actionBarSize">?android:attr/actionBarSize</item>
        <item name="actionBarDivider">?android:attr/actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionButtonStyle">?android:attr/actionButtonStyle</item>
        <item name="actionModeBackground">?android:attr/actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:attr/actionModeCloseDrawable</item>
        <item name="actionOverflowButtonStyle">?android:attr/actionOverflowButtonStyle</item>
        <item name="homeAsUpIndicator">?android:attr/homeAsUpIndicator</item>

        
        <item name="listPreferredItemHeightSmall">?android:attr/listPreferredItemHeightSmall</item>
        <item name="textAppearanceLargePopupMenu">?android:attr/textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:attr/textAppearanceSmallPopupMenu</item>

        
        <item name="selectableItemBackground">?android:attr/selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="borderlessButtonStyle">?android:borderlessButtonStyle</item>
        <item name="dividerHorizontal">?android:attr/dividerHorizontal</item>
        <item name="dividerVertical">?android:attr/dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/editTextColor</item>
        <item name="listChoiceBackgroundIndicator">?android:attr/listChoiceBackgroundIndicator</item>

        
        <item name="buttonStyle">?android:attr/buttonStyle</item>
        <item name="buttonStyleSmall">?android:attr/buttonStyleSmall</item>
        <item name="checkboxStyle">?android:attr/checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="radioButtonStyle">?android:attr/radioButtonStyle</item>
        <item name="ratingBarStyle">?android:attr/ratingBarStyle</item>
        <item name="spinnerStyle">?android:attr/spinnerStyle</item>

        
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
    </style><style name="Base.V21.Theme.AppCompat.Dialog" parent="Base.V11.Theme.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style><style name="Base.V21.Theme.AppCompat.Light" parent="Base.V7.Theme.AppCompat.Light">
        
        <item name="actionBarSize">?android:attr/actionBarSize</item>
        <item name="actionBarDivider">?android:attr/actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionButtonStyle">?android:attr/actionButtonStyle</item>
        <item name="actionModeBackground">?android:attr/actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:attr/actionModeCloseDrawable</item>
        <item name="actionOverflowButtonStyle">?android:attr/actionOverflowButtonStyle</item>
        <item name="homeAsUpIndicator">?android:attr/homeAsUpIndicator</item>

        
        <item name="listPreferredItemHeightSmall">?android:attr/listPreferredItemHeightSmall</item>
        <item name="textAppearanceLargePopupMenu">?android:attr/textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:attr/textAppearanceSmallPopupMenu</item>

        
        <item name="selectableItemBackground">?android:attr/selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="borderlessButtonStyle">?android:borderlessButtonStyle</item>
        <item name="dividerHorizontal">?android:attr/dividerHorizontal</item>
        <item name="dividerVertical">?android:attr/dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/editTextColor</item>
        <item name="listChoiceBackgroundIndicator">?android:attr/listChoiceBackgroundIndicator</item>

        
        <item name="buttonStyle">?android:attr/buttonStyle</item>
        <item name="buttonStyleSmall">?android:attr/buttonStyleSmall</item>
        <item name="checkboxStyle">?android:attr/checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="radioButtonStyle">?android:attr/radioButtonStyle</item>
        <item name="ratingBarStyle">?android:attr/ratingBarStyle</item>
        <item name="spinnerStyle">?android:attr/spinnerStyle</item>

        
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
    </style><style name="Base.V21.Theme.AppCompat.Light.Dialog" parent="Base.V11.Theme.AppCompat.Light.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style><style name="Base.V21.ThemeOverlay.AppCompat.Dialog" parent="Base.V11.ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style><style name="Base.Widget.AppCompat.ActionBar.TabText" parent="android:Widget.Material.ActionBar.TabText">
    </style><style name="Base.Widget.AppCompat.ActionBar.TabView" parent="android:Widget.Material.ActionBar.TabView">
    </style><style name="Base.Widget.AppCompat.ActionButton" parent="android:Widget.Material.ActionButton">
    </style><style name="Base.Widget.AppCompat.ActionButton.CloseMode" parent="android:Widget.Material.ActionButton.CloseMode">
        <item name="android:minWidth">56dp</item>
    </style><style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="android:Widget.Material.ActionButton.Overflow">
    </style><style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="android:Widget.Material.AutoCompleteTextView">
        <item name="android:background">?attr/editTextBackground</item>
    </style><style name="Base.Widget.AppCompat.Button" parent="android:Widget.Material.Button"/><style name="Base.Widget.AppCompat.Button.Borderless" parent="android:Widget.Material.Button.Borderless"/><style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="android:Widget.Material.Button.Borderless.Colored">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style><style name="Base.Widget.AppCompat.Button.Small" parent="android:Widget.Material.Button.Small"/><style name="Base.Widget.AppCompat.ButtonBar" parent="android:Widget.Material.ButtonBar"/><style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="android:Widget.Material.CompoundButton.CheckBox"/><style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="android:Widget.Material.CompoundButton.RadioButton"/><style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="android:Widget.Material.DropDownItem.Spinner"/><style name="Base.Widget.AppCompat.EditText" parent="android:Widget.Material.EditText">
        <item name="android:background">?attr/editTextBackground</item>
    </style><style name="Base.Widget.AppCompat.ImageButton" parent="android:Widget.Material.ImageButton"/><style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="android:Widget.Material.Light.ActionBar.TabText">
    </style><style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="android:Widget.Material.Light.ActionBar.TabText">
    </style><style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="android:Widget.Material.Light.ActionBar.TabView">
    </style><style name="Base.Widget.AppCompat.Light.PopupMenu" parent="android:Widget.Material.Light.PopupMenu">
    </style><style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
        <item name="android:dropDownHorizontalOffset">-4dip</item>
        <item name="android:overlapAnchor">true</item>
    </style><style name="Base.Widget.AppCompat.ListPopupWindow" parent="android:Widget.Material.ListPopupWindow">
    </style><style name="Base.Widget.AppCompat.ListView" parent="android:Widget.Material.ListView"/><style name="Base.Widget.AppCompat.ListView.DropDown" parent="android:Widget.Material.ListView.DropDown"/><style name="Base.Widget.AppCompat.ListView.Menu"/><style name="Base.Widget.AppCompat.PopupMenu" parent="android:Widget.Material.PopupMenu">
    </style><style name="Base.Widget.AppCompat.PopupMenu.Overflow">
        <item name="android:dropDownHorizontalOffset">-4dip</item>
        <item name="android:overlapAnchor">true</item>
    </style><style name="Base.Widget.AppCompat.ProgressBar" parent="android:Widget.Material.ProgressBar">
    </style><style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="android:Widget.Material.ProgressBar.Horizontal">
    </style><style name="Base.Widget.AppCompat.RatingBar" parent="android:Widget.Material.RatingBar"/><style name="Base.Widget.AppCompat.SeekBar" parent="android:Widget.Material.SeekBar"/><style name="Base.Widget.AppCompat.Spinner" parent="android:Widget.Material.Spinner"/><style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="android:Widget.Material.TextView.SpinnerItem"/><style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="android:Widget.Material.Toolbar.Button.Navigation">
    </style><style name="Platform.AppCompat" parent="Platform.V21.AppCompat"/><style name="Platform.AppCompat.Light" parent="Platform.V21.AppCompat.Light"/><style name="Platform.ThemeOverlay.AppCompat" parent="">
        
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
    </style><style name="Platform.ThemeOverlay.AppCompat.Dark"/><style name="Platform.ThemeOverlay.AppCompat.Light"/><style name="Platform.V21.AppCompat" parent="android:Theme.Material.NoActionBar">
        
        <item name="android:textColorLink">?android:attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?android:attr/colorAccent</item>

        
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
    </style><style name="Platform.V21.AppCompat.Light" parent="android:Theme.Material.Light.NoActionBar">
        
        <item name="android:textColorLink">?android:attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?android:attr/colorAccent</item>

        
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
    </style></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-v22\values-v22.xml" qualifiers="v22"><style name="Base.Theme.AppCompat" parent="Base.V22.Theme.AppCompat"/><style name="Base.Theme.AppCompat.Light" parent="Base.V22.Theme.AppCompat.Light"/><style name="Base.V22.Theme.AppCompat" parent="Base.V21.Theme.AppCompat">
        <item name="actionModeShareDrawable">?android:attr/actionModeShareDrawable</item>
        
        <item name="editTextBackground">?android:attr/editTextBackground</item>
    </style><style name="Base.V22.Theme.AppCompat.Light" parent="Base.V21.Theme.AppCompat.Light">
        <item name="actionModeShareDrawable">?android:attr/actionModeShareDrawable</item>
        
        <item name="editTextBackground">?android:attr/editTextBackground</item>
    </style></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-v23\values-v23.xml" qualifiers="v23"><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="android:TextAppearance.Material.Widget.ActionBar.Menu"/><style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="android:TextAppearance.Material.Widget.Button.Inverse"/><style name="Base.Theme.AppCompat" parent="Base.V23.Theme.AppCompat"/><style name="Base.Theme.AppCompat.Light" parent="Base.V23.Theme.AppCompat.Light"/><style name="Base.V23.Theme.AppCompat" parent="Base.V22.Theme.AppCompat">
        
        <item name="ratingBarStyleIndicator">?android:attr/ratingBarStyleIndicator</item>
        <item name="ratingBarStyleSmall">?android:attr/ratingBarStyleSmall</item>

        
        <item name="actionBarItemBackground">?android:attr/actionBarItemBackground</item>
        
        <item name="actionMenuTextColor">?android:attr/actionMenuTextColor</item>
        <item name="actionMenuTextAppearance">?android:attr/actionMenuTextAppearance</item>

        <item name="controlBackground">@drawable/abc_control_background_material</item>
    </style><style name="Base.V23.Theme.AppCompat.Light" parent="Base.V22.Theme.AppCompat.Light">
        
        <item name="ratingBarStyleIndicator">?android:attr/ratingBarStyleIndicator</item>
        <item name="ratingBarStyleSmall">?android:attr/ratingBarStyleSmall</item>

        
        <item name="actionBarItemBackground">?android:attr/actionBarItemBackground</item>
        
        <item name="actionMenuTextColor">?android:attr/actionMenuTextColor</item>
        <item name="actionMenuTextAppearance">?android:attr/actionMenuTextAppearance</item>

        <item name="controlBackground">@drawable/abc_control_background_material</item>
    </style><style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="android:Widget.Material.Button.Borderless.Colored"/><style name="Base.Widget.AppCompat.Button.Colored" parent="android:Widget.Material.Button.Colored"/><style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="android:Widget.Material.RatingBar.Indicator"/><style name="Base.Widget.AppCompat.RatingBar.Small" parent="android:Widget.Material.RatingBar.Small"/><style name="Base.Widget.AppCompat.Spinner.Underlined" parent="android:Widget.Material.Spinner.Underlined"/></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-v24\values-v24.xml" qualifiers="v24"><style name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="android:TextAppearance.Material.Widget.Button.Borderless.Colored"/><style name="Base.TextAppearance.AppCompat.Widget.Button.Colored" parent="android:TextAppearance.Material.Widget.Button.Colored"/></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-v25\values-v25.xml" qualifiers="v25"><style name="Platform.AppCompat" parent="Platform.V25.AppCompat"/><style name="Platform.AppCompat.Light" parent="Platform.V25.AppCompat.Light"/><style name="Platform.V25.AppCompat" parent="android:Theme.Material.NoActionBar">
    </style><style name="Platform.V25.AppCompat.Light" parent="android:Theme.Material.Light.NoActionBar">
    </style></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-v26\values-v26.xml" qualifiers="v26"><style name="Base.Theme.AppCompat" parent="Base.V26.Theme.AppCompat"/><style name="Base.Theme.AppCompat.Light" parent="Base.V26.Theme.AppCompat.Light"/><style name="Base.V26.Theme.AppCompat" parent="Base.V23.Theme.AppCompat">
        
        <item name="colorError">?android:attr/colorError</item>
    </style><style name="Base.V26.Theme.AppCompat.Light" parent="Base.V23.Theme.AppCompat.Light">
        
        <item name="colorError">?android:attr/colorError</item>
    </style><style name="Base.V26.Widget.AppCompat.Toolbar" parent="Base.V7.Widget.AppCompat.Toolbar">
        <item name="android:touchscreenBlocksFocus">true</item>
        <item name="android:keyboardNavigationCluster">true</item>
    </style><style name="Base.Widget.AppCompat.Toolbar" parent="Base.V26.Widget.AppCompat.Toolbar"/></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-vi\values-vi.xml" qualifiers="vi"><string msgid="*******************" name="abc_action_bar_home_description">"Điều hướng về trang chủ"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Điều hướng lên trên"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Thêm tùy chọn"</string><string msgid="*******************" name="abc_action_mode_done">"Xong"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Xem tất cả"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Chọn một ứng dụng"</string><string msgid="121134116657445385" name="abc_capital_off">"TẮT"</string><string msgid="3405795526292276155" name="abc_capital_on">"BẬT"</string><string msgid="7723749260725869598" name="abc_search_hint">"Tìm kiếm…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Xóa truy vấn"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Tìm kiếm truy vấn"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Tìm kiếm"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Gửi truy vấn"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Tìm kiếm bằng giọng nói"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Chia sẻ với"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Chia sẻ với <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Thu gọn"</string><string msgid="146198913615257606" name="search_menu_title">"Tìm kiếm"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-xlarge-v4\values-xlarge-v4.xml" qualifiers="xlarge-v4"><item name="abc_dialog_fixed_height_major" type="dimen">60%</item><item name="abc_dialog_fixed_height_minor" type="dimen">90%</item><item name="abc_dialog_fixed_width_major" type="dimen">50%</item><item name="abc_dialog_fixed_width_minor" type="dimen">70%</item><item name="abc_dialog_min_width_major" type="dimen">45%</item><item name="abc_dialog_min_width_minor" type="dimen">72%</item></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-zh-rCN\values-zh-rCN.xml" qualifiers="zh-rCN"><string msgid="*******************" name="abc_action_bar_home_description">"转到主屏幕"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"转到上一层级"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"更多选项"</string><string msgid="*******************" name="abc_action_mode_done">"完成"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"查看全部"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"选择应用"</string><string msgid="121134116657445385" name="abc_capital_off">"关闭"</string><string msgid="3405795526292276155" name="abc_capital_on">"开启"</string><string msgid="7723749260725869598" name="abc_search_hint">"搜索…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"清除查询"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"搜索查询"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"搜索"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"提交查询"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"语音搜索"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"分享方式"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"使用<ns1:g id="APPLICATION_NAME">%s</ns1:g>分享"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"收起"</string><string msgid="146198913615257606" name="search_menu_title">"搜索"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-zh-rHK\values-zh-rHK.xml" qualifiers="zh-rHK"><string msgid="*******************" name="abc_action_bar_home_description">"瀏覽主頁"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"向上瀏覽"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"更多選項"</string><string msgid="*******************" name="abc_action_mode_done">"完成"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"顯示全部"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"選擇應用程式"</string><string msgid="121134116657445385" name="abc_capital_off">"關閉"</string><string msgid="3405795526292276155" name="abc_capital_on">"開啟"</string><string msgid="7723749260725869598" name="abc_search_hint">"搜尋…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"清除查詢"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"搜尋查詢"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"搜尋"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"提交查詢"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"語音搜尋"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"分享對象"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"使用「<ns1:g id="APPLICATION_NAME">%s</ns1:g>」分享"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"收合"</string><string msgid="146198913615257606" name="search_menu_title">"搜尋"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-zh-rTW\values-zh-rTW.xml" qualifiers="zh-rTW"><string msgid="*******************" name="abc_action_bar_home_description">"瀏覽首頁"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"向上瀏覽"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"更多選項"</string><string msgid="*******************" name="abc_action_mode_done">"完成"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"查看全部"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"選擇應用程式"</string><string msgid="121134116657445385" name="abc_capital_off">"關閉"</string><string msgid="3405795526292276155" name="abc_capital_on">"開啟"</string><string msgid="7723749260725869598" name="abc_search_hint">"搜尋…"</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"清除查詢"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"搜尋查詢"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"搜尋"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"提交查詢"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"語音搜尋"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"選擇分享對象"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"與「<ns1:g id="APPLICATION_NAME">%s</ns1:g>」分享"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"收合"</string><string msgid="146198913615257606" name="search_menu_title">"搜尋"</string></file><file path="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4d39853c039fc72c0029bb8eeb9366e7\transformed\appcompat-v7-27.0.2\res\values-zu\values-zu.xml" qualifiers="zu"><string msgid="*******************" name="abc_action_bar_home_description">"Zulazulela ekhaya"</string><string msgid="1594238315039666878" name="abc_action_bar_up_description">"Zulazulela phezulu"</string><string msgid="3588849162933574182" name="abc_action_menu_overflow_description">"Izinketho eziningi"</string><string msgid="*******************" name="abc_action_mode_done">"Kwenziwe"</string><string msgid="7468859129482906941" name="abc_activity_chooser_view_see_all">"Buka konke"</string><string msgid="2031811694353399454" name="abc_activitychooserview_choose_application">"Khetha uhlelo lokusebenza"</string><string msgid="121134116657445385" name="abc_capital_off">"VALIWE"</string><string msgid="3405795526292276155" name="abc_capital_on">"VULIWE"</string><string msgid="7723749260725869598" name="abc_search_hint">"Iyasesha..."</string><string msgid="3691816814315814921" name="abc_searchview_description_clear">"Sula inkinga"</string><string msgid="2550479030709304392" name="abc_searchview_description_query">"Umbuzo wosesho"</string><string msgid="8264924765203268293" name="abc_searchview_description_search">"Sesha"</string><string msgid="8928215447528550784" name="abc_searchview_description_submit">"Hambisa umbuzo"</string><string msgid="893419373245838918" name="abc_searchview_description_voice">"Ukusesha ngezwi"</string><string msgid="3421042268587513524" name="abc_shareactionprovider_share_with">"Yabelana no-"</string><string msgid="3300176832234831527" name="abc_shareactionprovider_share_with_application">"Yabelana ne-<ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string><string msgid="1603543279005712093" name="abc_toolbar_collapse_description">"Goqa"</string><string msgid="146198913615257606" name="search_menu_title">"Sesha"</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res"><file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\drawable\ic_launcher_background.xml" preprocessing="true" qualifiers=""><generated-file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\generated\res\pngs\debug\drawable-xxxhdpi\ic_launcher_background.png" qualifiers="xxxhdpi-v4" type="drawable"/><generated-file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\generated\res\pngs\debug\drawable-mdpi\ic_launcher_background.png" qualifiers="mdpi-v4" type="drawable"/><generated-file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\generated\res\pngs\debug\drawable-ldpi\ic_launcher_background.png" qualifiers="ldpi-v4" type="drawable"/><generated-file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\generated\res\pngs\debug\drawable-xxhdpi\ic_launcher_background.png" qualifiers="xxhdpi-v4" type="drawable"/><generated-file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\generated\res\pngs\debug\drawable-hdpi\ic_launcher_background.png" qualifiers="hdpi-v4" type="drawable"/><generated-file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\generated\res\pngs\debug\drawable-xhdpi\ic_launcher_background.png" qualifiers="xhdpi-v4" type="drawable"/><generated-file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\generated\res\pngs\debug\drawable-anydpi-v21\ic_launcher_background.xml" qualifiers="anydpi-v21" type="drawable"/></file><file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" preprocessing="true" qualifiers="v24"><generated-file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\generated\res\pngs\debug\drawable-anydpi-v24\ic_launcher_foreground.xml" qualifiers="anydpi-v24" type="drawable"/></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res"><file name="activity_main" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="error" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\layout\error.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\colors.xml" qualifiers=""><color name="colorPrimary">#3F51B5</color><color name="colorPrimaryDark">#303F9F</color><color name="colorAccent">#FF4081</color></file><file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#D0D5D0</color></file><file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">My Flarum</string><string name="error_message">Error occured, please check your internet connecion.</string><string name="notification_error_ssl_cert_invalid">SSL Error! Give Permission</string><string name="exit_app">Do you want to  exit this application?</string></file><file path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style><style name="AppTheme.NoTitleBar" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build\generated\res\resValues\debug"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="AlertDialog"><attr name="android:layout"/><attr format="reference" name="buttonPanelSideLayout"/><attr format="reference" name="listLayout"/><attr format="reference" name="multiChoiceItemLayout"/><attr format="reference" name="singleChoiceItemLayout"/><attr format="reference" name="listItemLayout"/><attr format="boolean" name="showTitle"/></declare-styleable><declare-styleable name="AppCompatSeekBar"><attr name="android:thumb"/><attr format="reference" name="tickMark"/><attr format="color" name="tickMarkTint"/><attr name="tickMarkTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr></declare-styleable><declare-styleable name="Spinner"><attr name="android:prompt"/><attr name="popupTheme"/><attr name="android:popupBackground"/><attr name="android:dropDownWidth"/><attr name="android:entries"/></declare-styleable><declare-styleable name="ColorStateListItem"><attr name="android:color"/><attr format="float" name="alpha"/><attr name="android:alpha"/></declare-styleable><declare-styleable name="PopupWindow"><attr format="boolean" name="overlapAnchor"/><attr name="android:popupBackground"/><attr name="android:popupAnimationStyle"/></declare-styleable><declare-styleable name="DrawerArrowToggle"><attr format="color" name="color"/><attr format="boolean" name="spinBars"/><attr format="dimension" name="drawableSize"/><attr format="dimension" name="gapBetweenBars"/><attr format="dimension" name="arrowHeadLength"/><attr format="dimension" name="arrowShaftLength"/><attr format="dimension" name="barLength"/><attr format="dimension" name="thickness"/></declare-styleable><declare-styleable name="ActionBarLayout"><attr name="android:layout_gravity"/></declare-styleable><declare-styleable name="ActivityChooserView"><attr format="string" name="initialActivityCount"/><attr format="reference" name="expandActivityOverflowButtonDrawable"/></declare-styleable><declare-styleable name="ViewStubCompat"><attr name="android:layout"/><attr name="android:inflatedId"/><attr name="android:id"/></declare-styleable><declare-styleable name="AppCompatTheme"><attr format="boolean" name="windowActionBar"/><attr format="boolean" name="windowNoTitle"/><attr format="boolean" name="windowActionBarOverlay"/><attr format="boolean" name="windowActionModeOverlay"/><attr format="dimension|fraction" name="windowFixedWidthMajor"/><attr format="dimension|fraction" name="windowFixedHeightMinor"/><attr format="dimension|fraction" name="windowFixedWidthMinor"/><attr format="dimension|fraction" name="windowFixedHeightMajor"/><attr format="dimension|fraction" name="windowMinWidthMajor"/><attr format="dimension|fraction" name="windowMinWidthMinor"/><attr name="android:windowIsFloating"/><attr name="android:windowAnimationStyle"/><attr format="reference" name="actionBarTabStyle"/><attr format="reference" name="actionBarTabBarStyle"/><attr format="reference" name="actionBarTabTextStyle"/><attr format="reference" name="actionOverflowButtonStyle"/><attr format="reference" name="actionOverflowMenuStyle"/><attr format="reference" name="actionBarPopupTheme"/><attr format="reference" name="actionBarStyle"/><attr format="reference" name="actionBarSplitStyle"/><attr format="reference" name="actionBarTheme"/><attr format="reference" name="actionBarWidgetTheme"/><attr format="dimension" name="actionBarSize">
            <enum name="wrap_content" value="0"/>
        </attr><attr format="reference" name="actionBarDivider"/><attr format="reference" name="actionBarItemBackground"/><attr format="reference" name="actionMenuTextAppearance"/><attr format="color|reference" name="actionMenuTextColor"/><attr format="reference" name="actionModeStyle"/><attr format="reference" name="actionModeCloseButtonStyle"/><attr format="reference" name="actionModeBackground"/><attr format="reference" name="actionModeSplitBackground"/><attr format="reference" name="actionModeCloseDrawable"/><attr format="reference" name="actionModeCutDrawable"/><attr format="reference" name="actionModeCopyDrawable"/><attr format="reference" name="actionModePasteDrawable"/><attr format="reference" name="actionModeSelectAllDrawable"/><attr format="reference" name="actionModeShareDrawable"/><attr format="reference" name="actionModeFindDrawable"/><attr format="reference" name="actionModeWebSearchDrawable"/><attr format="reference" name="actionModePopupWindowStyle"/><attr format="reference" name="textAppearanceLargePopupMenu"/><attr format="reference" name="textAppearanceSmallPopupMenu"/><attr format="reference" name="textAppearancePopupMenuHeader"/><attr format="reference" name="dialogTheme"/><attr format="dimension" name="dialogPreferredPadding"/><attr format="reference" name="listDividerAlertDialog"/><attr format="reference" name="actionDropDownStyle"/><attr format="dimension" name="dropdownListPreferredItemHeight"/><attr format="reference" name="spinnerDropDownItemStyle"/><attr format="reference" name="homeAsUpIndicator"/><attr format="reference" name="actionButtonStyle"/><attr format="reference" name="buttonBarStyle"/><attr format="reference" name="buttonBarButtonStyle"/><attr format="reference" name="selectableItemBackground"/><attr format="reference" name="selectableItemBackgroundBorderless"/><attr format="reference" name="borderlessButtonStyle"/><attr format="reference" name="dividerVertical"/><attr format="reference" name="dividerHorizontal"/><attr format="reference" name="activityChooserViewStyle"/><attr format="reference" name="toolbarStyle"/><attr format="reference" name="toolbarNavigationButtonStyle"/><attr format="reference" name="popupMenuStyle"/><attr format="reference" name="popupWindowStyle"/><attr format="reference|color" name="editTextColor"/><attr format="reference" name="editTextBackground"/><attr format="reference" name="imageButtonStyle"/><attr format="reference" name="textAppearanceSearchResultTitle"/><attr format="reference" name="textAppearanceSearchResultSubtitle"/><attr format="reference|color" name="textColorSearchUrl"/><attr format="reference" name="searchViewStyle"/><attr format="dimension" name="listPreferredItemHeight"/><attr format="dimension" name="listPreferredItemHeightSmall"/><attr format="dimension" name="listPreferredItemHeightLarge"/><attr format="dimension" name="listPreferredItemPaddingLeft"/><attr format="dimension" name="listPreferredItemPaddingRight"/><attr format="reference" name="dropDownListViewStyle"/><attr format="reference" name="listPopupWindowStyle"/><attr format="reference" name="textAppearanceListItem"/><attr format="reference" name="textAppearanceListItemSecondary"/><attr format="reference" name="textAppearanceListItemSmall"/><attr format="reference" name="panelBackground"/><attr format="dimension" name="panelMenuListWidth"/><attr format="reference" name="panelMenuListTheme"/><attr format="reference" name="listChoiceBackgroundIndicator"/><attr format="color" name="colorPrimary"/><attr format="color" name="colorPrimaryDark"/><attr format="color" name="colorAccent"/><attr format="color" name="colorControlNormal"/><attr format="color" name="colorControlActivated"/><attr format="color" name="colorControlHighlight"/><attr format="color" name="colorButtonNormal"/><attr format="color" name="colorSwitchThumbNormal"/><attr format="reference" name="controlBackground"/><attr format="color" name="colorBackgroundFloating"/><attr format="reference" name="alertDialogStyle"/><attr format="reference" name="alertDialogButtonGroupStyle"/><attr format="boolean" name="alertDialogCenterButtons"/><attr format="reference" name="alertDialogTheme"/><attr format="reference|color" name="textColorAlertDialogListItem"/><attr format="reference" name="buttonBarPositiveButtonStyle"/><attr format="reference" name="buttonBarNegativeButtonStyle"/><attr format="reference" name="buttonBarNeutralButtonStyle"/><attr format="reference" name="autoCompleteTextViewStyle"/><attr format="reference" name="buttonStyle"/><attr format="reference" name="buttonStyleSmall"/><attr format="reference" name="checkboxStyle"/><attr format="reference" name="checkedTextViewStyle"/><attr format="reference" name="editTextStyle"/><attr format="reference" name="radioButtonStyle"/><attr format="reference" name="ratingBarStyle"/><attr format="reference" name="ratingBarStyleIndicator"/><attr format="reference" name="ratingBarStyleSmall"/><attr format="reference" name="seekBarStyle"/><attr format="reference" name="spinnerStyle"/><attr format="reference" name="switchStyle"/><attr format="reference" name="listMenuViewStyle"/><attr format="reference" name="tooltipFrameBackground"/><attr format="reference|color" name="tooltipForegroundColor"/><attr format="reference|color" name="colorError"/></declare-styleable><declare-styleable name="ListPopupWindow"><attr name="android:dropDownVerticalOffset"/><attr name="android:dropDownHorizontalOffset"/></declare-styleable><declare-styleable name="TextAppearance"><attr name="android:textSize"/><attr name="android:textColor"/><attr name="android:textColorHint"/><attr name="android:textColorLink"/><attr name="android:textStyle"/><attr name="android:typeface"/><attr name="android:fontFamily"/><attr name="fontFamily"/><attr name="textAllCaps"/><attr name="android:shadowColor"/><attr name="android:shadowDy"/><attr name="android:shadowDx"/><attr name="android:shadowRadius"/></declare-styleable><declare-styleable name="MenuView"><attr name="android:itemTextAppearance"/><attr name="android:horizontalDivider"/><attr name="android:verticalDivider"/><attr name="android:headerBackground"/><attr name="android:itemBackground"/><attr name="android:windowAnimationStyle"/><attr name="android:itemIconDisabledAlpha"/><attr format="boolean" name="preserveIconSpacing"/><attr format="reference" name="subMenuArrow"/></declare-styleable><declare-styleable name="MenuGroup"><attr name="android:id"/><attr name="android:menuCategory"/><attr name="android:orderInCategory"/><attr name="android:checkableBehavior"/><attr name="android:visible"/><attr name="android:enabled"/></declare-styleable><declare-styleable name="RecycleListView"><attr format="dimension" name="paddingBottomNoButtons"/><attr format="dimension" name="paddingTopNoTitle"/></declare-styleable><declare-styleable name="ActionMenuItemView"><attr name="android:minWidth"/></declare-styleable><declare-styleable name="ActionBar"><attr name="navigationMode">
            
            <enum name="normal" value="0"/>
            
            <enum name="listMode" value="1"/>
            
            <enum name="tabMode" value="2"/>
        </attr><attr name="displayOptions">
            <flag name="none" value="0"/>
            <flag name="useLogo" value="0x1"/>
            <flag name="showHome" value="0x2"/>
            <flag name="homeAsUp" value="0x4"/>
            <flag name="showTitle" value="0x8"/>
            <flag name="showCustom" value="0x10"/>
            <flag name="disableHome" value="0x20"/>
        </attr><attr name="title"/><attr format="string" name="subtitle"/><attr format="reference" name="titleTextStyle"/><attr format="reference" name="subtitleTextStyle"/><attr format="reference" name="icon"/><attr format="reference" name="logo"/><attr format="reference" name="divider"/><attr format="reference" name="background"/><attr format="reference|color" name="backgroundStacked"/><attr format="reference|color" name="backgroundSplit"/><attr format="reference" name="customNavigationLayout"/><attr name="height"/><attr format="reference" name="homeLayout"/><attr format="reference" name="progressBarStyle"/><attr format="reference" name="indeterminateProgressStyle"/><attr format="dimension" name="progressBarPadding"/><attr name="homeAsUpIndicator"/><attr format="dimension" name="itemPadding"/><attr format="boolean" name="hideOnContentScroll"/><attr format="dimension" name="contentInsetStart"/><attr format="dimension" name="contentInsetEnd"/><attr format="dimension" name="contentInsetLeft"/><attr format="dimension" name="contentInsetRight"/><attr format="dimension" name="contentInsetStartWithNavigation"/><attr format="dimension" name="contentInsetEndWithActions"/><attr format="dimension" name="elevation"/><attr format="reference" name="popupTheme"/></declare-styleable><declare-styleable name="Toolbar"><attr format="reference" name="titleTextAppearance"/><attr format="reference" name="subtitleTextAppearance"/><attr name="title"/><attr name="subtitle"/><attr name="android:gravity"/><attr format="dimension" name="titleMargin"/><attr format="dimension" name="titleMarginStart"/><attr format="dimension" name="titleMarginEnd"/><attr format="dimension" name="titleMarginTop"/><attr format="dimension" name="titleMarginBottom"/><attr format="dimension" name="titleMargins"/><attr name="contentInsetStart"/><attr name="contentInsetEnd"/><attr name="contentInsetLeft"/><attr name="contentInsetRight"/><attr name="contentInsetStartWithNavigation"/><attr name="contentInsetEndWithActions"/><attr format="dimension" name="maxButtonHeight"/><attr name="buttonGravity">
            
            <flag name="top" value="0x30"/>
            
            <flag name="bottom" value="0x50"/>
        </attr><attr format="reference" name="collapseIcon"/><attr format="string" name="collapseContentDescription"/><attr name="popupTheme"/><attr format="reference" name="navigationIcon"/><attr format="string" name="navigationContentDescription"/><attr name="logo"/><attr format="string" name="logoDescription"/><attr format="color" name="titleTextColor"/><attr format="color" name="subtitleTextColor"/><attr name="android:minHeight"/></declare-styleable><declare-styleable name="LinearLayoutCompat_Layout"><attr name="android:layout_width"/><attr name="android:layout_height"/><attr name="android:layout_weight"/><attr name="android:layout_gravity"/></declare-styleable><declare-styleable name="FontFamily"><attr format="string" name="fontProviderAuthority"/><attr format="string" name="fontProviderPackage"/><attr format="string" name="fontProviderQuery"/><attr format="reference" name="fontProviderCerts"/><attr name="fontProviderFetchStrategy">
            
            <enum name="blocking" value="0"/>
            
            <enum name="async" value="1"/>
        </attr><attr format="integer" name="fontProviderFetchTimeout">
          
            <enum name="forever" value="-1"/>
        </attr></declare-styleable><declare-styleable name="AppCompatTextView"><attr format="reference|boolean" name="textAllCaps"/><attr name="android:textAppearance"/><attr format="enum" name="autoSizeTextType">
            
            <enum name="none" value="0"/>
            
            <enum name="uniform" value="1"/>
        </attr><attr format="dimension" name="autoSizeStepGranularity"/><attr format="reference" name="autoSizePresetSizes"/><attr format="dimension" name="autoSizeMinTextSize"/><attr format="dimension" name="autoSizeMaxTextSize"/><attr format="string" name="fontFamily"/></declare-styleable><declare-styleable name="MenuItem"><attr name="android:id"/><attr name="android:menuCategory"/><attr name="android:orderInCategory"/><attr name="android:title"/><attr name="android:titleCondensed"/><attr name="android:icon"/><attr name="android:alphabeticShortcut"/><attr name="alphabeticModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr><attr name="android:numericShortcut"/><attr name="numericModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr><attr name="android:checkable"/><attr name="android:checked"/><attr name="android:visible"/><attr name="android:enabled"/><attr name="android:onClick"/><attr name="showAsAction">
            
            <flag name="never" value="0"/>
            
            <flag name="ifRoom" value="1"/>
            
            <flag name="always" value="2"/>
            
            <flag name="withText" value="4"/>
            
            <flag name="collapseActionView" value="8"/>
        </attr><attr format="reference" name="actionLayout"/><attr format="string" name="actionViewClass"/><attr format="string" name="actionProviderClass"/><attr format="string" name="contentDescription"/><attr format="string" name="tooltipText"/><attr format="color" name="iconTint"/><attr name="iconTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr></declare-styleable><declare-styleable name="ButtonBarLayout"><attr format="boolean" name="allowStacking"/></declare-styleable><declare-styleable name="LinearConstraintLayout"><attr name="android:orientation"/></declare-styleable><declare-styleable name="ViewBackgroundHelper"><attr name="android:background"/><attr format="color" name="backgroundTint"/><attr name="backgroundTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr></declare-styleable><declare-styleable name="ConstraintSet"><attr name="android:orientation"/><attr name="android:id"/><attr name="android:visibility"/><attr name="android:alpha"/><attr name="android:elevation"/><attr name="android:rotationX"/><attr name="android:rotationY"/><attr name="android:scaleX"/><attr name="android:scaleY"/><attr name="android:transformPivotX"/><attr name="android:transformPivotY"/><attr name="android:translationX"/><attr name="android:translationY"/><attr name="android:translationZ"/><attr name="android:layout_width"/><attr name="android:layout_height"/><attr name="android:layout_marginStart"/><attr name="android:layout_marginBottom"/><attr name="android:layout_marginTop"/><attr name="android:layout_marginEnd"/><attr name="android:layout_marginLeft"/><attr name="android:layout_marginRight"/><attr name="layout_constraintGuide_begin"/><attr name="layout_constraintGuide_end"/><attr name="layout_constraintGuide_percent"/><attr name="layout_constraintLeft_toLeftOf"/><attr name="layout_constraintLeft_toRightOf"/><attr name="layout_constraintRight_toLeftOf"/><attr name="layout_constraintRight_toRightOf"/><attr name="layout_constraintTop_toTopOf"/><attr name="layout_constraintTop_toBottomOf"/><attr name="layout_constraintBottom_toTopOf"/><attr name="layout_constraintBottom_toBottomOf"/><attr name="layout_constraintBaseline_toBaselineOf"/><attr name="layout_constraintStart_toEndOf"/><attr name="layout_constraintStart_toStartOf"/><attr name="layout_constraintEnd_toStartOf"/><attr name="layout_constraintEnd_toEndOf"/><attr name="layout_goneMarginLeft"/><attr name="layout_goneMarginTop"/><attr name="layout_goneMarginRight"/><attr name="layout_goneMarginBottom"/><attr name="layout_goneMarginStart"/><attr name="layout_goneMarginEnd"/><attr name="layout_constraintHorizontal_bias"/><attr name="layout_constraintVertical_bias"/><attr name="layout_constraintWidth_default"/><attr name="layout_constraintHeight_default"/><attr name="layout_constraintWidth_min"/><attr name="layout_constraintWidth_max"/><attr name="layout_constraintHeight_min"/><attr name="layout_constraintHeight_max"/><attr name="layout_constraintLeft_creator"/><attr name="layout_constraintTop_creator"/><attr name="layout_constraintRight_creator"/><attr name="layout_constraintBottom_creator"/><attr name="layout_constraintBaseline_creator"/><attr name="layout_constraintDimensionRatio"/><attr name="layout_constraintHorizontal_weight"/><attr name="layout_constraintVertical_weight"/><attr name="layout_constraintHorizontal_chainStyle"/><attr name="layout_constraintVertical_chainStyle"/><attr name="layout_editor_absoluteX"/><attr name="layout_editor_absoluteY"/></declare-styleable><declare-styleable name="AppCompatImageView"><attr name="android:src"/><attr format="reference" name="srcCompat"/><attr format="color" name="tint"/><attr name="tintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr></declare-styleable><declare-styleable name="View"><attr format="dimension" name="paddingStart"/><attr format="dimension" name="paddingEnd"/><attr name="android:focusable"/><attr format="reference" name="theme"/><attr name="android:theme"/></declare-styleable><declare-styleable name="ConstraintLayout_Layout"><attr name="android:orientation"/><attr name="android:minWidth"/><attr name="android:minHeight"/><attr name="android:maxWidth"/><attr name="android:maxHeight"/><attr name="layout_optimizationLevel"/><attr name="constraintSet"/><attr name="layout_constraintGuide_begin"/><attr name="layout_constraintGuide_end"/><attr name="layout_constraintGuide_percent"/><attr name="layout_constraintLeft_toLeftOf"/><attr name="layout_constraintLeft_toRightOf"/><attr name="layout_constraintRight_toLeftOf"/><attr name="layout_constraintRight_toRightOf"/><attr name="layout_constraintTop_toTopOf"/><attr name="layout_constraintTop_toBottomOf"/><attr name="layout_constraintBottom_toTopOf"/><attr name="layout_constraintBottom_toBottomOf"/><attr name="layout_constraintBaseline_toBaselineOf"/><attr name="layout_constraintStart_toEndOf"/><attr name="layout_constraintStart_toStartOf"/><attr name="layout_constraintEnd_toStartOf"/><attr name="layout_constraintEnd_toEndOf"/><attr name="layout_goneMarginLeft"/><attr name="layout_goneMarginTop"/><attr name="layout_goneMarginRight"/><attr name="layout_goneMarginBottom"/><attr name="layout_goneMarginStart"/><attr name="layout_goneMarginEnd"/><attr name="layout_constraintHorizontal_bias"/><attr name="layout_constraintVertical_bias"/><attr name="layout_constraintWidth_default"/><attr name="layout_constraintHeight_default"/><attr name="layout_constraintWidth_min"/><attr name="layout_constraintWidth_max"/><attr name="layout_constraintHeight_min"/><attr name="layout_constraintHeight_max"/><attr name="layout_constraintLeft_creator"/><attr name="layout_constraintTop_creator"/><attr name="layout_constraintRight_creator"/><attr name="layout_constraintBottom_creator"/><attr name="layout_constraintBaseline_creator"/><attr name="layout_constraintDimensionRatio"/><attr name="layout_constraintHorizontal_weight"/><attr name="layout_constraintVertical_weight"/><attr name="layout_constraintHorizontal_chainStyle"/><attr name="layout_constraintVertical_chainStyle"/><attr name="layout_editor_absoluteX"/><attr name="layout_editor_absoluteY"/></declare-styleable><declare-styleable name="PopupWindowBackgroundState"><attr format="boolean" name="state_above_anchor"/></declare-styleable><declare-styleable name="FontFamilyFont"><attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr><attr format="reference" name="font"/><attr format="integer" name="fontWeight"/><attr name="android:fontStyle"/><attr name="android:font"/><attr name="android:fontWeight"/></declare-styleable><declare-styleable name="LinearLayoutCompat"><attr name="android:orientation"/><attr name="android:gravity"/><attr name="android:baselineAligned"/><attr name="android:baselineAlignedChildIndex"/><attr name="android:weightSum"/><attr format="boolean" name="measureWithLargestChild"/><attr name="divider"/><attr name="showDividers">
            <flag name="none" value="0"/>
            <flag name="beginning" value="1"/>
            <flag name="middle" value="2"/>
            <flag name="end" value="4"/>
        </attr><attr format="dimension" name="dividerPadding"/></declare-styleable><declare-styleable name="SearchView"><attr format="reference" name="layout"/><attr format="boolean" name="iconifiedByDefault"/><attr name="android:maxWidth"/><attr format="string" name="queryHint"/><attr format="string" name="defaultQueryHint"/><attr name="android:imeOptions"/><attr name="android:inputType"/><attr format="reference" name="closeIcon"/><attr format="reference" name="goIcon"/><attr format="reference" name="searchIcon"/><attr format="reference" name="searchHintIcon"/><attr format="reference" name="voiceIcon"/><attr format="reference" name="commitIcon"/><attr format="reference" name="suggestionRowLayout"/><attr format="reference" name="queryBackground"/><attr format="reference" name="submitBackground"/><attr name="android:focusable"/></declare-styleable><declare-styleable name="ActionMode"><attr name="titleTextStyle"/><attr name="subtitleTextStyle"/><attr name="background"/><attr name="backgroundSplit"/><attr name="height"/><attr format="reference" name="closeItemLayout"/></declare-styleable><declare-styleable name="ActionMenuView"/><declare-styleable name="CompoundButton"><attr name="android:button"/><attr format="color" name="buttonTint"/><attr name="buttonTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr></declare-styleable><declare-styleable name="SwitchCompat"><attr name="android:thumb"/><attr format="color" name="thumbTint"/><attr name="thumbTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr><attr format="reference" name="track"/><attr format="color" name="trackTint"/><attr name="trackTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr><attr name="android:textOn"/><attr name="android:textOff"/><attr format="dimension" name="thumbTextPadding"/><attr format="reference" name="switchTextAppearance"/><attr format="dimension" name="switchMinWidth"/><attr format="dimension" name="switchPadding"/><attr format="boolean" name="splitTrack"/><attr format="boolean" name="showText"/></declare-styleable><declare-styleable name="AppCompatTextHelper"><attr name="android:drawableLeft"/><attr name="android:drawableTop"/><attr name="android:drawableRight"/><attr name="android:drawableBottom"/><attr name="android:drawableStart"/><attr name="android:drawableEnd"/><attr name="android:textAppearance"/></declare-styleable></configuration></mergedItems></merger>