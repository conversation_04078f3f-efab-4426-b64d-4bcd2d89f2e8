<lint-module
    format="1"
    dir="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app"
    name=":app"
    type="APP"
    maven="flarum-app:app:unspecified"
    agpVersion="8.12.0"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-27\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-27"
    neverShrinking="true">
  <lintOptions
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
