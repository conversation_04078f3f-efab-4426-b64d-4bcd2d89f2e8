[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "android/arch/lifecycle/ClassesInfoCache$CallbackInfo.class", "name": "android/arch/lifecycle/ClassesInfoCache$CallbackInfo.class", "size": 3372, "crc": -2043012218}, {"key": "android/arch/lifecycle/OnLifecycleEvent.class", "name": "android/arch/lifecycle/OnLifecycleEvent.class", "size": 582, "crc": 184075165}, {"key": "android/arch/lifecycle/ClassesInfoCache$MethodReference.class", "name": "android/arch/lifecycle/ClassesInfoCache$MethodReference.class", "size": 2297, "crc": 1264307593}, {"key": "android/arch/lifecycle/FullLifecycleObserverAdapter.class", "name": "android/arch/lifecycle/FullLifecycleObserverAdapter.class", "size": 1699, "crc": 1842685387}, {"key": "android/arch/lifecycle/Lifecycle.class", "name": "android/arch/lifecycle/Lifecycle.class", "size": 823, "crc": -2099105567}, {"key": "android/arch/lifecycle/GenericLifecycleObserver.class", "name": "android/arch/lifecycle/GenericLifecycleObserver.class", "size": 431, "crc": -1734393656}, {"key": "android/arch/lifecycle/Lifecycle$Event.class", "name": "android/arch/lifecycle/Lifecycle$Event.class", "size": 1407, "crc": 172054491}, {"key": "android/arch/lifecycle/CompositeGeneratedAdaptersObserver.class", "name": "android/arch/lifecycle/CompositeGeneratedAdaptersObserver.class", "size": 1911, "crc": -656586979}, {"key": "android/arch/lifecycle/ReflectiveGenericLifecycleObserver.class", "name": "android/arch/lifecycle/ReflectiveGenericLifecycleObserver.class", "size": 1520, "crc": -1613483520}, {"key": "android/arch/lifecycle/ClassesInfoCache.class", "name": "android/arch/lifecycle/ClassesInfoCache.class", "size": 7071, "crc": -257306796}, {"key": "android/arch/lifecycle/Lifecycling.class", "name": "android/arch/lifecycle/Lifecycling.class", "size": 7693, "crc": 1608332976}, {"key": "android/arch/lifecycle/FullLifecycleObserver.class", "name": "android/arch/lifecycle/FullLifecycleObserver.class", "size": 350, "crc": -1928249521}, {"key": "android/arch/lifecycle/FullLifecycleObserverAdapter$1.class", "name": "android/arch/lifecycle/FullLifecycleObserverAdapter$1.class", "size": 1109, "crc": 323123665}, {"key": "android/arch/lifecycle/LifecycleObserver.class", "name": "android/arch/lifecycle/LifecycleObserver.class", "size": 138, "crc": 339784206}, {"key": "android/arch/lifecycle/GeneratedAdapter.class", "name": "android/arch/lifecycle/GeneratedAdapter.class", "size": 681, "crc": -2126557598}, {"key": "android/arch/lifecycle/MethodCallsLogger.class", "name": "android/arch/lifecycle/MethodCallsLogger.class", "size": 1438, "crc": -2112564213}, {"key": "android/arch/lifecycle/Lifecycle$State.class", "name": "android/arch/lifecycle/Lifecycle$State.class", "size": 1602, "crc": 100625704}, {"key": "android/arch/lifecycle/SingleGeneratedAdapterObserver.class", "name": "android/arch/lifecycle/SingleGeneratedAdapterObserver.class", "size": 1494, "crc": 2142766151}, {"key": "android/arch/lifecycle/LifecycleOwner.class", "name": "android/arch/lifecycle/LifecycleOwner.class", "size": 275, "crc": 357253030}]