1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.hhilan.flarum"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="15"
9        android:targetSdkVersion="27" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:3:22-64
12
13    <application
13-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:4:5-20:19
14        android:allowBackup="true"
14-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:5:9-35
15        android:extractNativeLibs="true"
16        android:icon="@mipmap/ic_launcher"
16-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:6:9-43
17        android:label="@string/app_name"
17-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:7:9-41
18        android:roundIcon="@mipmap/ic_launcher_round"
18-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:8:9-54
19        android:supportsRtl="true"
19-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:9:9-35
20        android:theme="@style/AppTheme.NoTitleBar" >
20-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:10:9-51
21        <activity
21-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:11:9-19:20
22            android:name="com.hhilan.flarum.MainActivity"
22-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:11:19-64
23            android:configChanges="keyboardHidden|orientation|screenSize" >
23-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:12:13-74
24            <intent-filter>
24-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:14:13-18:29
25                <action android:name="android.intent.action.MAIN" />
25-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:15:17-69
25-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:15:25-66
26
27                <category android:name="android.intent.category.LAUNCHER" />
27-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:17:17-77
27-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:17:27-74
28            </intent-filter>
29        </activity>
30    </application>
31
32</manifest>
