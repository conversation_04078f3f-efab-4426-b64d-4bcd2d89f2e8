1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.hhilan.flarum"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="15"
9        android:targetSdkVersion="27" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:3:22-64
12
13    <application
13-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:4:5-20:19
14        android:allowBackup="true"
14-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:5:9-35
15        android:debuggable="true"
16        android:extractNativeLibs="true"
17        android:icon="@mipmap/ic_launcher"
17-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:6:9-43
18        android:label="@string/app_name"
18-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:7:9-41
19        android:roundIcon="@mipmap/ic_launcher_round"
19-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:8:9-54
20        android:supportsRtl="true"
20-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:9:9-35
21        android:theme="@style/AppTheme.NoTitleBar" >
21-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:10:9-51
22        <activity
22-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:11:9-19:20
23            android:name="com.hhilan.flarum.MainActivity"
23-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:11:19-64
24            android:configChanges="keyboardHidden|orientation|screenSize" >
24-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:12:13-74
25            <intent-filter>
25-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:14:13-18:29
26                <action android:name="android.intent.action.MAIN" />
26-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:15:17-69
26-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:15:25-66
27
28                <category android:name="android.intent.category.LAUNCHER" />
28-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:17:17-77
28-->C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:17:27-74
29            </intent-filter>
30        </activity>
31    </application>
32
33</manifest>
