<variant
    name="debug"
    package="com.hhilan.flarum"
    minSdkVersion="15"
    targetSdkVersion="27"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-debug-report.txt"
    partialResultsDir="build\intermediates\android_test_lint_partial_results\debug\lintAnalyzeDebugAndroidTest\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
  </sourceProviders>
  <testSourceProviders>
    <sourceProvider
        manifests="src\androidTest\AndroidManifest.xml"
        javaDirectories="src\androidTest\java;src\androidTestDebug\java;src\androidTest\kotlin;src\androidTestDebug\kotlin"
        resDirectories="src\androidTest\res;src\androidTestDebug\res"
        assetsDirectories="src\androidTest\assets;src\androidTestDebug\assets"
        androidTest="true"/>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      type="INSTRUMENTATION_TEST"
      applicationId="com.hhilan.flarum.test"
      generatedResourceFolders="build\generated\res\resValues\androidTest\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\cdbf31a5279ec6a02c7355b2a3305033\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
