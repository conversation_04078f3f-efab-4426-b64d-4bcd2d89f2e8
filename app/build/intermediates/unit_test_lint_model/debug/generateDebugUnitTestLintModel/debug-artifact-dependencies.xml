<dependencies>
  <compile
      roots=":@@:app::debug,com.android.support:appcompat-v7:27.0.2@aar,com.android.support.constraint:constraint-layout:1.0.2@aar,junit:junit:4.12@jar,org.hamcrest:hamcrest-core:1.3@jar,com.android.support:support-fragment:27.0.2@aar,com.android.support:support-core-utils:27.0.2@aar,com.android.support:animated-vector-drawable:27.0.2@aar,com.android.support:support-vector-drawable:27.0.2@aar,com.android.support:support-core-ui:27.0.2@aar,com.android.support:support-compat:27.0.2@aar,com.android.support:support-annotations:27.0.2@jar,android.arch.lifecycle:runtime:1.0.3@aar,android.arch.lifecycle:common:1.0.3@jar,android.arch.core:common:1.0.0@jar,com.android.support.constraint:constraint-layout-solver:1.0.2@jar">
    <dependency
        name=":@@:app::debug"
        simpleName="artifacts::app"/>
    <dependency
        name="com.android.support:appcompat-v7:27.0.2@aar"
        simpleName="com.android.support:appcompat-v7"/>
    <dependency
        name="com.android.support.constraint:constraint-layout:1.0.2@aar"
        simpleName="com.android.support.constraint:constraint-layout"/>
    <dependency
        name="junit:junit:4.12@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="com.android.support:support-fragment:27.0.2@aar"
        simpleName="com.android.support:support-fragment"/>
    <dependency
        name="com.android.support:support-core-utils:27.0.2@aar"
        simpleName="com.android.support:support-core-utils"/>
    <dependency
        name="com.android.support:animated-vector-drawable:27.0.2@aar"
        simpleName="com.android.support:animated-vector-drawable"/>
    <dependency
        name="com.android.support:support-vector-drawable:27.0.2@aar"
        simpleName="com.android.support:support-vector-drawable"/>
    <dependency
        name="com.android.support:support-core-ui:27.0.2@aar"
        simpleName="com.android.support:support-core-ui"/>
    <dependency
        name="com.android.support:support-compat:27.0.2@aar"
        simpleName="com.android.support:support-compat"/>
    <dependency
        name="com.android.support:support-annotations:27.0.2@jar"
        simpleName="com.android.support:support-annotations"/>
    <dependency
        name="android.arch.lifecycle:runtime:1.0.3@aar"
        simpleName="android.arch.lifecycle:runtime"/>
    <dependency
        name="android.arch.lifecycle:common:1.0.3@jar"
        simpleName="android.arch.lifecycle:common"/>
    <dependency
        name="android.arch.core:common:1.0.0@jar"
        simpleName="android.arch.core:common"/>
    <dependency
        name="com.android.support.constraint:constraint-layout-solver:1.0.2@jar"
        simpleName="com.android.support.constraint:constraint-layout-solver"/>
  </compile>
  <package
      roots="junit:junit:4.12@jar,com.android.support:appcompat-v7:27.0.2@aar,com.android.support.constraint:constraint-layout:1.0.2@aar,org.hamcrest:hamcrest-core:1.3@jar,com.android.support:support-fragment:27.0.2@aar,com.android.support:support-core-utils:27.0.2@aar,com.android.support:animated-vector-drawable:27.0.2@aar,com.android.support:support-vector-drawable:27.0.2@aar,com.android.support:support-core-ui:27.0.2@aar,com.android.support:support-compat:27.0.2@aar,com.android.support:support-annotations:27.0.2@jar,com.android.support.constraint:constraint-layout-solver:1.0.2@jar,android.arch.lifecycle:runtime:1.0.3@aar,android.arch.lifecycle:common:1.0.3@jar,android.arch.core:common:1.0.0@jar">
    <dependency
        name="junit:junit:4.12@jar"
        simpleName="junit:junit"/>
    <dependency
        name="com.android.support:appcompat-v7:27.0.2@aar"
        simpleName="com.android.support:appcompat-v7"/>
    <dependency
        name="com.android.support.constraint:constraint-layout:1.0.2@aar"
        simpleName="com.android.support.constraint:constraint-layout"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="com.android.support:support-fragment:27.0.2@aar"
        simpleName="com.android.support:support-fragment"/>
    <dependency
        name="com.android.support:support-core-utils:27.0.2@aar"
        simpleName="com.android.support:support-core-utils"/>
    <dependency
        name="com.android.support:animated-vector-drawable:27.0.2@aar"
        simpleName="com.android.support:animated-vector-drawable"/>
    <dependency
        name="com.android.support:support-vector-drawable:27.0.2@aar"
        simpleName="com.android.support:support-vector-drawable"/>
    <dependency
        name="com.android.support:support-core-ui:27.0.2@aar"
        simpleName="com.android.support:support-core-ui"/>
    <dependency
        name="com.android.support:support-compat:27.0.2@aar"
        simpleName="com.android.support:support-compat"/>
    <dependency
        name="com.android.support:support-annotations:27.0.2@jar"
        simpleName="com.android.support:support-annotations"/>
    <dependency
        name="com.android.support.constraint:constraint-layout-solver:1.0.2@jar"
        simpleName="com.android.support.constraint:constraint-layout-solver"/>
    <dependency
        name="android.arch.lifecycle:runtime:1.0.3@aar"
        simpleName="android.arch.lifecycle:runtime"/>
    <dependency
        name="android.arch.lifecycle:common:1.0.3@jar"
        simpleName="android.arch.lifecycle:common"/>
    <dependency
        name="android.arch.core:common:1.0.0@jar"
        simpleName="android.arch.core:common"/>
  </package>
</dependencies>
