<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<inset xmlns:android="http://schemas.android.com/apk/res/android"
       android:insetLeft="@dimen/abc_control_inset_material"
       android:insetTop="@dimen/abc_control_inset_material"
       android:insetBottom="@dimen/abc_control_inset_material"
       android:insetRight="@dimen/abc_control_inset_material">
    <selector>
        <item android:state_checked="false" android:state_pressed="false">
            <layer-list>
                <item android:drawable="@drawable/abc_textfield_default_mtrl_alpha" />
                <item android:drawable="@drawable/abc_spinner_mtrl_am_alpha" />
            </layer-list>
        </item>
        <item>
            <layer-list>
                <item android:drawable="@drawable/abc_textfield_activated_mtrl_alpha" />
                <item android:drawable="@drawable/abc_spinner_mtrl_am_alpha" />
            </layer-list>
        </item>
    </selector>
</inset>