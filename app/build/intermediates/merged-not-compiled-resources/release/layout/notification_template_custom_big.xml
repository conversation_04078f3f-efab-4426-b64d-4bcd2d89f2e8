<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<LinearLayout android:id="@+id/notification_main_column_container"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/notification_large_icon_height"
    android:orientation="horizontal"
    android:paddingRight="12dp"
    android:paddingEnd="12dp">
    <ImageView android:id="@+id/icon"
        android:layout_width="@dimen/notification_large_icon_width"
        android:layout_height="@dimen/notification_large_icon_height"
        android:background="@drawable/notification_tile_bg"
        android:scaleType="center"
    />
    <FrameLayout
        android:id="@+id/notification_main_column"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingStart="12dp"
        android:paddingTop="@dimen/notification_main_column_padding_top"
        android:layout_weight="1"/>
    <FrameLayout
        android:id="@+id/right_side"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="12dp"
        android:paddingLeft="12dp">
        <include
            layout="@layout/notification_template_part_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|top"
            android:visibility="gone"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:layout_marginTop="18dp"
            android:orientation="horizontal">
            <TextView android:id="@+id/info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:singleLine="true"
                android:textAppearance="@style/TextAppearance.Compat.Notification.Info"
            />
            <ImageView android:id="@+id/right_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:layout_marginLeft="8dp"
                android:alpha="0.7"
                android:scaleType="center"
                android:visibility="gone"
            />
        </LinearLayout>
    </FrameLayout>
</LinearLayout>