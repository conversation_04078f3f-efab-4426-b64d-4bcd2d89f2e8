<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.12.0" type="conditional_incidents">

    <incident
        id="GestureBackNavigation"
        severity="warning"
        message="If intercepting back events, this should be handled through the registration of callbacks; see https://developer.android.com/guide/navigation/custom-back/predictive-back-gesture">
        <show-url
            description="Show https://developer.android.com/guide/navigation/custom-back/predictive-back-gesture"
            url="https://developer.android.com/guide/navigation/custom-back/predictive-back-gesture"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/hhilan/flarum/MainActivity.java"
            line="119"
            column="22"
            startOffset="3666"
            endLine="119"
            endColumn="43"
            endOffset="3687"/>
    </incident>

    <incident
        id="IntentFilterExportedReceiver"
        severity="warning"
        message="As of Android 12, `android:exported` must be set; use `true` to make the activity available to other apps, and `false` otherwise. For launcher activities, this should be set to `true`.">
        <fix-attribute
            description="Set exported=&quot;true&quot;"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="exported"
            value="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="11"
            column="10"
            startOffset="473"
            endLine="11"
            endColumn="18"
            endOffset="481"/>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_alignParentLeft`; already defining `layout_alignParentStart` with `targetSdkVersion` 27">
        <fix-attribute
            description="Delete layout_alignParentLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_alignParentLeft"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="14"
            column="9"
            startOffset="525"
            endLine="14"
            endColumn="39"
            endOffset="555"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_alignParentRight`; already defining `layout_alignParentEnd` with `targetSdkVersion` 27">
        <fix-attribute
            description="Delete layout_alignParentRight"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_alignParentRight"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="17"
            column="9"
            startOffset="669"
            endLine="17"
            endColumn="40"
            endOffset="700"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlEnabled"
        severity="warning"
        message="You must set `android:targetSdkVersion` to at least 17 when enabling RTL support">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="9"
            column="30"
            startOffset="403"
            endLine="9"
            endColumn="34"
            endOffset="407"/>
        <map>
            <entry
                name="applies"
                int="1"/>
        </map>
    </incident>

</incidents>
