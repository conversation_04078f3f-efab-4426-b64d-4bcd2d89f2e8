<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.12.0" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.hhilan.flarum.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.colorAccent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="164"
            endLine="5"
            endColumn="30"
            endOffset="182"/>
        <location id="R.color.colorPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="64"
            endLine="3"
            endColumn="31"
            endOffset="83"/>
        <location id="R.color.colorPrimaryDark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="112"
            endLine="4"
            endColumn="35"
            endOffset="135"/>
        <location id="R.drawable.ic_launcher_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="74"
            endColumn="10"
            endOffset="4939"/>
        <location id="R.drawable.ic_launcher_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-v24/ic_launcher_foreground.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="34"
            endColumn="10"
            endOffset="1912"/>
        <location id="R.style.AppTheme"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="4"
            column="12"
            startOffset="64"
            endLine="4"
            endColumn="27"
            endOffset="79"/>
        <entry
            name="model"
            string="color[ic_launcher_background(U),colorPrimary(D),colorPrimaryDark(D),colorAccent(D)],drawable[ic_launcher_background(D),ic_launcher_foreground(D),ic_launcher_foreground_1(E)],id[webView(U),progressBar1(U),button(D),button1(D)],layout[activity_main(U),error(U)],mipmap[ic_launcher(U),ic_launcher_round(U),ic_launcher_foreground(U)],string[app_name(U),error_message(U),notification_error_ssl_cert_invalid(U),exit_app(U)],style[AppTheme_NoTitleBar(U),AppTheme(D),Theme_AppCompat_Light_DarkActionBar(R)];5^6,c^11,d^0^f,e^0^f,14^16,15^16^1^2^3;;;"/>
    </map>
    <map id="VectorDrawableCompat">
        <entry
            name="ic_launcher_background"
            boolean="false"/>
        <entry
            name="ic_launcher_foreground"
            boolean="false"/>
    </map>

</incidents>
