<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.12.0" type="incidents">

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of `compileSdkVersion` than 27 is available: 36">
        <fix-replace
            description="Set compileSdkVersion to 36"
            oldString="27"
            replacement="36"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="5"
            column="5"
            startOffset="93"
            endLine="5"
            endColumn="25"
            endOffset="113"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.android.support:appcompat-v7 than 27.0.2 is available: 28.0.0">
        <fix-replace
            description="Change to 28.0.0"
            family="Update versions"
            oldString="27.0.2"
            replacement="28.0.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="32"
            column="20"
            startOffset="924"
            endLine="32"
            endColumn="61"
            endOffset="965"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.android.support.constraint:constraint-layout than 1.0.2 is available: 2.0.4">
        <fix-replace
            description="Change to 2.0.4"
            family="Update versions"
            oldString="1.0.2"
            replacement="2.0.4"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="33"
            column="20"
            startOffset="986"
            endLine="33"
            endColumn="76"
            endOffset="1042"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.android.support.test:runner than 1.0.1 is available: 1.0.2">
        <fix-replace
            description="Change to 1.0.2"
            family="Update versions"
            oldString="1.0.1"
            replacement="1.0.2"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="35"
            column="31"
            startOffset="1117"
            endLine="35"
            endColumn="70"
            endOffset="1156"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.android.support.test.espresso:espresso-core than 3.0.1 is available: 3.0.2">
        <fix-replace
            description="Change to 3.0.2"
            family="Update versions"
            oldString="3.0.1"
            replacement="3.0.2"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="36"
            column="31"
            startOffset="1188"
            endLine="36"
            endColumn="86"
            endOffset="1243"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of junit:junit than 4.12 is available: 4.13.2">
        <fix-replace
            description="Change to 4.13.2"
            family="Update versions"
            oldString="4.12"
            replacement="4.13.2"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="34"
            column="24"
            startOffset="1067"
            endLine="34"
            endColumn="42"
            endOffset="1085"/>
    </incident>

    <incident
        id="Typos"
        severity="warning"
        message="&quot;occured&quot; is a common misspelling; did you mean &quot;occurred&quot;?">
        <fix-replace
            description="Replace with &quot;occurred&quot;"
            oldString="occured"
            replacement="occurred"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="3"
            column="40"
            startOffset="100"
            endLine="3"
            endColumn="47"
            endOffset="107"/>
    </incident>

    <incident
        id="WebViewClientOnReceivedSslError"
        severity="warning"
        message="Permitting connections with SSL-related errors could allow eavesdroppers to intercept data sent by your app, which impacts the privacy of your users. Consider canceling the connections by invoking `SslErrorHandler#cancel()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/hhilan/flarum/MainActivity.java"
            line="65"
            column="21"
            startOffset="2104"
            endLine="65"
            endColumn="38"
            endOffset="2121"/>
    </incident>

    <incident
        id="ExpiredTargetSdkVersion"
        severity="fatal"
        message="Google Play requires that apps target API level 33 or higher.">
        <location
            file="${:app*projectDir}/build.gradle"
            line="9"
            column="9"
            startOffset="213"
            endLine="9"
            endColumn="28"
            endOffset="232"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive icon is missing a monochrome tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="17"
            endOffset="269"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive roundIcon is missing a monochrome tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="17"
            endOffset="269"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Try Again&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/error.xml"
            line="24"
            column="9"
            startOffset="726"
            endLine="24"
            endColumn="33"
            endOffset="750"/>
    </incident>

</incidents>
