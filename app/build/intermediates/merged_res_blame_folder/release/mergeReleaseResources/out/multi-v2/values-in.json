{"logs": [{"outputFile": "com.hhilan.flarum.app-mergeReleaseResources-5:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1948", "endColumns": "100", "endOffsets": "2044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,317,422,509,613,729,811,890,976,1079,1188,1289,1393,1501,1609,1765,1864", "endColumns": "109,101,104,86,103,115,81,78,85,102,108,100,103,107,107,155,98,83", "endOffsets": "210,312,417,504,608,724,806,885,971,1074,1183,1284,1388,1496,1604,1760,1859,1943"}}]}]}