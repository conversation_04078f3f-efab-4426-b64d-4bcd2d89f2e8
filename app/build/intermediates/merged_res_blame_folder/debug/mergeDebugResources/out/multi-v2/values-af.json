{"logs": [{"outputFile": "com.hhilan.flarum.app-mergeDebugResources-5:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,415,500,603,721,797,874,955,1062,1165,1262,1370,1472,1574,1728,1826", "endColumns": "103,99,105,84,102,117,75,76,80,106,102,96,107,101,101,153,97,79", "endOffsets": "204,304,410,495,598,716,792,869,950,1057,1160,1257,1365,1467,1569,1723,1821,1901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1906", "endColumns": "100", "endOffsets": "2002"}}]}]}