{"logs": [{"outputFile": "com.hhilan.flarum.app-mergeDebugResources-5:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,419,502,602,715,792,869,948,1057,1165,1261,1375,1477,1578,1731,1828", "endColumns": "102,98,111,82,99,112,76,76,78,108,107,95,113,101,100,152,96,78", "endOffsets": "203,302,414,497,597,710,787,864,943,1052,1160,1256,1370,1472,1573,1726,1823,1902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1907", "endColumns": "100", "endOffsets": "2003"}}]}]}