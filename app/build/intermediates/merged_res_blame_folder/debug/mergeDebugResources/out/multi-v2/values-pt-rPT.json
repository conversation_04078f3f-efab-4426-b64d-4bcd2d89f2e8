{"logs": [{"outputFile": "com.hhilan.flarum.app-mergeDebugResources-5:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,752,836,917,1004,1111,1223,1325,1433,1540,1647,1818,1917", "endColumns": "119,105,106,88,100,123,83,80,86,106,111,101,107,106,106,170,98,84", "endOffsets": "220,326,433,522,623,747,831,912,999,1106,1218,1320,1428,1535,1642,1813,1912,1997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2002", "endColumns": "100", "endOffsets": "2098"}}]}]}