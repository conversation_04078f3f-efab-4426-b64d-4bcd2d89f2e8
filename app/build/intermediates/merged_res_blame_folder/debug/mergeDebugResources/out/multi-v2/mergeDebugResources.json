{"logs": [{"outputFile": "com.hhilan.flarum.app-debug-7:/values-sq_values-sq.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,518,628,751,832,911,994,1100,1205,1303,1409,1512,1628,1782,1881", "endColumns": "113,99,111,86,109,122,80,78,82,105,104,97,105,102,115,153,98,80", "endOffsets": "214,314,426,513,623,746,827,906,989,1095,1200,1298,1404,1507,1623,1777,1876,1957"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1962", "endColumns": "100", "endOffsets": "2058"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-zh-rCN_values-zh-rCN.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,296,396,478,575,681,757,833,911,1007,1103,1198,1295,1390,1488,1637,1731", "endColumns": "95,94,99,81,96,105,75,75,77,95,95,94,96,94,97,148,93,77", "endOffsets": "196,291,391,473,570,676,752,828,906,1002,1098,1193,1290,1385,1483,1632,1726,1804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1809", "endColumns": "100", "endOffsets": "1905"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-am_values-am.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-am\\values-am.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1882", "endColumns": "100", "endOffsets": "1978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,412,498,601,714,792,870,949,1049,1149,1245,1348,1447,1554,1707,1803", "endColumns": "101,98,105,85,102,112,77,77,78,99,99,95,102,98,106,152,95,78", "endOffsets": "202,301,407,493,596,709,787,865,944,1044,1144,1240,1343,1442,1549,1702,1798,1877"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-vi_values-vi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,327,436,520,623,742,819,896,980,1084,1193,1294,1399,1513,1618,1775,1874", "endColumns": "113,107,108,83,102,118,76,76,83,103,108,100,104,113,104,156,98,83", "endOffsets": "214,322,431,515,618,737,814,891,975,1079,1188,1289,1394,1508,1613,1770,1869,1953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1958", "endColumns": "100", "endOffsets": "2054"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-pl_values-pl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,323,431,517,626,745,823,900,982,1091,1200,1299,1408,1519,1627,1790,1886", "endColumns": "115,101,107,85,108,118,77,76,81,108,108,98,108,110,107,162,95,81", "endOffsets": "216,318,426,512,621,740,818,895,977,1086,1195,1294,1403,1514,1622,1785,1881,1963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1968", "endColumns": "100", "endOffsets": "2064"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ldltr-v21_values-ldltr-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ldltr-v21\\values-ldltr-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "112", "endOffsets": "163"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-v12_values-v12.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-v12\\values-v12.xml", "from": {"startLines": "2,5,8,9", "startColumns": "4,4,4,4", "startOffsets": "55,279,479,598", "endLines": "4,7,8,9", "endColumns": "12,12,118,94", "endOffsets": "274,474,593,688"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-mk_values-mk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,616,735,818,900,987,1093,1200,1301,1408,1519,1623,1779,1877", "endColumns": "107,103,107,85,104,118,82,81,86,105,106,100,106,110,103,155,97,83", "endOffsets": "208,312,420,506,611,730,813,895,982,1088,1195,1296,1403,1514,1618,1774,1872,1956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1961", "endColumns": "100", "endOffsets": "2057"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-az_values-az.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,424,512,619,733,814,893,978,1085,1192,1292,1401,1505,1615,1773,1875", "endColumns": "107,98,111,87,106,113,80,78,84,106,106,99,108,103,109,157,101,82", "endOffsets": "208,307,419,507,614,728,809,888,973,1080,1187,1287,1396,1500,1610,1768,1870,1953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-az\\values-az.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1958", "endColumns": "100", "endOffsets": "2054"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-port_values-port.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-port\\values-port.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "55", "endOffsets": "106"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-kk_values-kk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1950", "endColumns": "100", "endOffsets": "2046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,320,430,515,621,740,821,901,982,1085,1190,1288,1395,1504,1604,1770,1869", "endColumns": "111,102,109,84,105,118,80,79,80,102,104,97,106,108,99,165,98,80", "endOffsets": "212,315,425,510,616,735,816,896,977,1080,1185,1283,1390,1499,1599,1765,1864,1945"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-v16_values-v16.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "223"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "121", "endLines": "6", "endColumns": "12", "endOffsets": "289"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-de_values-de.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,611,726,803,879,961,1072,1175,1274,1385,1487,1594,1750,1852", "endColumns": "104,97,111,85,104,114,76,75,81,110,102,98,110,101,106,155,101,81", "endOffsets": "205,303,415,501,606,721,798,874,956,1067,1170,1269,1380,1482,1589,1745,1847,1929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1934", "endColumns": "100", "endOffsets": "2030"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-en-rAU_values-en-rAU.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1907", "endColumns": "100", "endOffsets": "2003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,793,869,951,1054,1158,1257,1362,1465,1569,1725,1825", "endColumns": "103,99,107,83,99,114,76,75,81,102,103,98,104,102,103,155,99,81", "endOffsets": "204,304,412,496,596,711,788,864,946,1049,1153,1252,1357,1460,1564,1720,1820,1902"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-sv_values-sv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1903", "endColumns": "100", "endOffsets": "1999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,314,425,510,612,725,801,877,957,1063,1163,1259,1364,1466,1568,1722,1824", "endColumns": "105,102,110,84,101,112,75,75,79,105,99,95,104,101,101,153,101,78", "endOffsets": "206,309,420,505,607,720,796,872,952,1058,1158,1254,1359,1461,1563,1717,1819,1898"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-is_values-is.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,794,874,955,1065,1173,1271,1380,1479,1582,1737,1835", "endColumns": "99,96,111,84,100,113,79,79,80,109,107,97,108,98,102,154,97,80", "endOffsets": "200,297,409,494,595,709,789,869,950,1060,1168,1266,1375,1474,1577,1732,1830,1911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-is\\values-is.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1916", "endColumns": "100", "endOffsets": "2012"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-nl_values-nl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1970", "endColumns": "100", "endOffsets": "2066"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,521,629,749,826,903,985,1096,1200,1299,1414,1527,1630,1785,1888", "endColumns": "117,104,106,85,107,119,76,76,81,110,103,98,114,112,102,154,102,81", "endOffsets": "218,323,430,516,624,744,821,898,980,1091,1195,1294,1409,1522,1625,1780,1883,1965"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-fr_values-fr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2003", "endColumns": "100", "endOffsets": "2099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,442,524,630,760,843,923,1009,1119,1231,1334,1445,1552,1659,1818,1917", "endColumns": "110,114,110,81,105,129,82,79,85,109,111,102,110,106,106,158,98,85", "endOffsets": "211,326,437,519,625,755,838,918,1004,1114,1226,1329,1440,1547,1654,1813,1912,1998"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-iw_values-iw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,308,416,500,600,714,792,870,951,1053,1157,1255,1358,1459,1559,1711,1807", "endColumns": "103,98,107,83,99,113,77,77,80,101,103,97,102,100,99,151,95,80", "endOffsets": "204,303,411,495,595,709,787,865,946,1048,1152,1250,1353,1454,1554,1706,1802,1883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "102", "endOffsets": "153"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1888", "endColumns": "102", "endOffsets": "1986"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-nb_values-nb.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,308,422,508,608,721,797,873,953,1056,1155,1251,1355,1453,1554,1707,1804", "endColumns": "107,94,113,85,99,112,75,75,79,102,98,95,103,97,100,152,96,78", "endOffsets": "208,303,417,503,603,716,792,868,948,1051,1150,1246,1350,1448,1549,1702,1799,1878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1883", "endColumns": "100", "endOffsets": "1979"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-th_values-th.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1906", "endColumns": "100", "endOffsets": "2002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,416,505,607,717,794,872,953,1061,1165,1263,1371,1476,1577,1730,1825", "endColumns": "104,97,107,88,101,109,76,77,80,107,103,97,107,104,100,152,94,80", "endOffsets": "205,303,411,500,602,712,789,867,948,1056,1160,1258,1366,1471,1572,1725,1820,1901"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-da_values-da.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,419,502,602,715,792,869,948,1057,1165,1261,1375,1477,1578,1731,1828", "endColumns": "102,98,111,82,99,112,76,76,78,108,107,95,113,101,100,152,96,78", "endOffsets": "203,302,414,497,597,710,787,864,943,1052,1160,1256,1370,1472,1573,1726,1823,1902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1907", "endColumns": "100", "endOffsets": "2003"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ml_values-ml.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2004", "endColumns": "100", "endOffsets": "2100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,342,457,550,655,787,864,940,1022,1133,1239,1337,1451,1551,1662,1821,1922", "endColumns": "118,117,114,92,104,131,76,75,81,110,105,97,113,99,110,158,100,81", "endOffsets": "219,337,452,545,650,782,859,935,1017,1128,1234,1332,1446,1546,1657,1816,1917,1999"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-be_values-be.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,638,756,835,913,996,1102,1208,1306,1414,1519,1624,1793,1893", "endColumns": "119,102,115,85,107,117,78,77,82,105,105,97,107,104,104,168,99,80", "endOffsets": "220,323,439,525,633,751,830,908,991,1097,1203,1301,1409,1514,1619,1788,1888,1969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-be\\values-be.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "108", "endOffsets": "159"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1974", "endColumns": "108", "endOffsets": "2078"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-tr_values-tr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,309,421,506,612,732,811,887,966,1073,1178,1274,1381,1483,1591,1747,1845", "endColumns": "104,98,111,84,105,119,78,75,78,106,104,95,106,101,107,155,97,78", "endOffsets": "205,304,416,501,607,727,806,882,961,1068,1173,1269,1376,1478,1586,1742,1840,1919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1924", "endColumns": "100", "endOffsets": "2020"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ur_values-ur.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,321,430,516,620,740,816,892,977,1085,1194,1296,1407,1507,1615,1780,1878", "endColumns": "109,105,108,85,103,119,75,75,84,107,108,101,110,99,107,164,97,79", "endOffsets": "210,316,425,511,615,735,811,887,972,1080,1189,1291,1402,1502,1610,1775,1873,1953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "102", "endOffsets": "153"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1958", "endColumns": "102", "endOffsets": "2056"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-fa_values-fa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1948", "endColumns": "100", "endOffsets": "2044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,318,429,513,616,731,810,888,969,1081,1184,1282,1392,1496,1605,1766,1867", "endColumns": "109,102,110,83,102,114,78,77,80,111,102,97,109,103,108,160,100,80", "endOffsets": "210,313,424,508,611,726,805,883,964,1076,1179,1277,1387,1491,1600,1761,1862,1943"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-b+sr+Latn_values-b+sr+Latn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1962", "endColumns": "100", "endOffsets": "2058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,318,424,510,614,732,814,895,983,1089,1197,1298,1403,1511,1612,1781,1878", "endColumns": "108,103,105,85,103,117,81,80,87,105,107,100,104,107,100,168,96,83", "endOffsets": "209,313,419,505,609,727,809,890,978,1084,1192,1293,1398,1506,1607,1776,1873,1957"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ms_values-ms.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1945", "endColumns": "100", "endOffsets": "2041"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,809,888,968,1080,1189,1286,1395,1498,1605,1764,1865", "endColumns": "110,104,107,86,103,110,77,78,79,111,108,96,108,102,106,158,100,79", "endOffsets": "211,316,424,511,615,726,804,883,963,1075,1184,1281,1390,1493,1600,1759,1860,1940"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-v11_values-v11.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-v11\\values-v11.xml", "from": {"startLines": "2,8,14,20,26,32,38,39,43,47,48,52,56,57,61,66,71,76,78,80,81,82,136,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,474,899,1327,1753,2180,2605,2694,2940,3189,3290,3542,3797,3900,4153,4561,4981,5403,5510,5639,5710,5793,9818,13986", "endLines": "7,13,19,25,31,37,38,42,46,47,51,55,56,60,65,70,75,77,79,80,81,135,190,191", "endColumns": "12,12,12,12,12,12,88,12,12,100,12,12,102,12,12,12,12,12,12,70,82,12,12,90", "endOffsets": "469,894,1322,1748,2175,2600,2689,2935,3184,3285,3537,3792,3895,4148,4556,4976,5398,5505,5634,5705,5788,9813,13981,14072"}, "to": {"startLines": "2,8,14,20,26,32,38,39,43,47,48,52,56,57,61,66,71,76,78,80,81,82,136,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,474,899,1327,1753,2180,2605,2694,2940,3189,3290,3542,3797,3900,4153,4561,4981,5403,5510,5639,5710,5793,9756,13862", "endLines": "7,13,19,25,31,37,38,42,46,47,51,55,56,60,65,70,75,77,79,80,81,135,190,191", "endColumns": "12,12,12,12,12,12,88,12,12,100,12,12,102,12,12,12,12,12,12,70,82,12,12,90", "endOffsets": "469,894,1322,1748,2175,2600,2689,2935,3184,3285,3537,3792,3895,4148,4556,4976,5398,5505,5634,5705,5788,9751,13857,13948"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ca_values-ca.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,329,436,519,627,753,837,918,1001,1112,1221,1319,1429,1533,1641,1801,1900", "endColumns": "117,105,106,82,107,125,83,80,82,110,108,97,109,103,107,159,98,80", "endOffsets": "218,324,431,514,622,748,832,913,996,1107,1216,1314,1424,1528,1636,1796,1895,1976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1981", "endColumns": "100", "endOffsets": "2077"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-hi_values-hi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,308,418,504,606,728,805,883,973,1082,1185,1287,1397,1498,1613,1775,1876", "endColumns": "105,96,109,85,101,121,76,77,89,108,102,101,109,100,114,161,100,79", "endOffsets": "206,303,413,499,601,723,800,878,968,1077,1180,1282,1392,1493,1608,1770,1871,1951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1956", "endColumns": "100", "endOffsets": "2052"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-sr_values-sr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1955", "endColumns": "100", "endOffsets": "2051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,317,423,509,613,731,812,892,980,1085,1193,1294,1398,1506,1607,1774,1871", "endColumns": "108,102,105,85,103,117,80,79,87,104,107,100,103,107,100,166,96,83", "endOffsets": "209,312,418,504,608,726,807,887,975,1080,1188,1289,1393,1501,1602,1769,1866,1950"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-v13_values-v13.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-v13\\values-v13.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "58", "endOffsets": "109"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-pt_values-pt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1989", "endColumns": "100", "endOffsets": "2085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,746,829,909,996,1103,1215,1317,1425,1532,1642,1804,1904", "endColumns": "119,105,106,88,100,117,82,79,86,106,111,101,107,106,109,161,99,84", "endOffsets": "220,326,433,522,623,741,824,904,991,1098,1210,1312,1420,1527,1637,1799,1899,1984"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-si_values-si.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-si\\values-si.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1965", "endColumns": "100", "endOffsets": "2061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,322,429,517,622,738,827,914,998,1107,1212,1310,1420,1519,1625,1784,1883", "endColumns": "109,106,106,87,104,115,88,86,83,108,104,97,109,98,105,158,98,81", "endOffsets": "210,317,424,512,617,733,822,909,993,1102,1207,1305,1415,1514,1620,1779,1878,1960"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-bs_values-bs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,333,440,526,630,752,834,915,1001,1104,1209,1307,1412,1525,1630,1805,1902", "endColumns": "118,108,106,85,103,121,81,80,85,102,104,97,104,112,104,174,96,83", "endOffsets": "219,328,435,521,625,747,829,910,996,1099,1204,1302,1407,1520,1625,1800,1897,1981"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1986", "endColumns": "100", "endOffsets": "2082"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-bn_values-bn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,326,432,526,631,760,838,916,1003,1113,1221,1320,1430,1536,1649,1814,1919", "endColumns": "108,111,105,93,104,128,77,77,86,109,107,98,109,105,112,164,104,81", "endOffsets": "209,321,427,521,626,755,833,911,998,1108,1216,1315,1425,1531,1644,1809,1914,1996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2001", "endColumns": "100", "endOffsets": "2097"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-sw_values-sw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1944", "endColumns": "100", "endOffsets": "2040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,306,414,504,609,726,808,891,973,1074,1183,1282,1389,1498,1603,1765,1862", "endColumns": "102,97,107,89,104,116,81,82,81,100,108,98,106,108,104,161,96,81", "endOffsets": "203,301,409,499,604,721,803,886,968,1069,1178,1277,1384,1493,1598,1760,1857,1939"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-v17_values-v17.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-v17\\values-v17.xml", "from": {"startLines": "2,5,9,12,15,18,22,26,29,32,35,39,42,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,456,614,764,936,1164,1406,1577,1751,1920,2193,2393,2597", "endLines": "4,8,11,14,17,21,25,28,31,34,38,41,45,49", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "223,451,609,759,931,1159,1401,1572,1746,1915,2188,2388,2592,2921"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-large-v4_values-large-v4.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-large-v4\\values-large-v4.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,185,256,326,396,464,532,636", "endColumns": "58,70,70,69,69,67,67,103,115", "endOffsets": "109,180,251,321,391,459,527,631,747"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-zh-rTW_values-zh-rTW.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,910,1006,1102,1197,1294,1389,1489,1639,1733", "endColumns": "94,92,99,81,96,107,75,75,77,95,95,94,96,94,99,149,93,77", "endOffsets": "195,288,388,470,567,675,751,827,905,1001,1097,1192,1289,1384,1484,1634,1728,1806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1811", "endColumns": "100", "endOffsets": "1907"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-sw600dp-v13_values-sw600dp-v13.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,124,193,263,337,413,472,543", "endColumns": "68,68,69,73,75,58,70,67", "endOffsets": "119,188,258,332,408,467,538,606"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ja_values-ja.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,765,841,919,1021,1120,1215,1318,1413,1509,1657,1754", "endColumns": "96,92,104,81,97,107,76,75,77,101,98,94,102,94,95,147,96,77", "endOffsets": "197,290,395,477,575,683,760,836,914,1016,1115,1210,1313,1408,1504,1652,1749,1827"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1832", "endColumns": "100", "endOffsets": "1928"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-fi_values-fi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1930", "endColumns": "100", "endOffsets": "2026"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,414,500,605,723,810,892,972,1079,1182,1279,1385,1484,1588,1751,1850", "endColumns": "107,99,100,85,104,117,86,81,79,106,102,96,105,98,103,162,98,79", "endOffsets": "208,308,409,495,600,718,805,887,967,1074,1177,1274,1380,1479,1583,1746,1845,1925"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-v21_values-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,223,290,354,470,596,722,850,1022", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "103,63,66,63,115,125,125,127,12,12", "endOffsets": "154,218,285,349,465,591,717,845,1017,1355"}, "to": {"startLines": "2,3,4,5,263,264,265,266,267,270", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,223,290,18636,18752,18878,19004,19132,19304", "endLines": "2,3,4,5,263,264,265,266,269,274", "endColumns": "103,63,66,63,115,125,125,127,12,12", "endOffsets": "154,218,285,349,18747,18873,18999,19127,19299,19637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,107,110,154,157,160,162,164,166,169,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,221,222,223,233,234,235,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4234,4383,4532,4644,4791,4944,5091,5166,5255,5342,5443,5546,8614,8800,11880,12078,12278,12401,12524,12637,12820,12951,13152,13241,13352,13585,13686,13781,13904,14033,14150,14327,14426,14561,14704,14839,14958,15159,15278,15371,15482,15538,15645,15840,15951,16084,16179,16270,16361,16478,16617,16688,16771,17451,17508,17566,18260", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,106,109,153,156,159,161,163,165,168,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,220,221,222,232,233,234,246,258", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4229,4378,4527,4639,4786,4939,5086,5161,5250,5337,5438,5541,8609,8795,11875,12073,12273,12396,12519,12632,12815,12946,13147,13236,13347,13580,13681,13776,13899,14028,14145,14322,14421,14556,14699,14834,14953,15154,15273,15366,15477,15533,15640,15835,15946,16079,16174,16265,16356,16473,16612,16683,16766,17446,17503,17561,18255,18961"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,24,25,26,28,30,31,32,33,34,36,38,40,42,44,46,47,52,54,56,57,58,60,62,63,64,65,66,67,111,114,158,161,164,166,168,170,173,175,178,179,180,183,184,185,186,187,188,191,192,194,196,198,200,204,206,207,208,209,211,215,217,219,220,221,222,223,225,226,227,237,238,239,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "354,445,548,651,756,863,972,1081,1190,1299,1408,1515,1618,1737,1892,2047,2152,2273,2374,2521,2662,2765,2884,2991,3094,3249,3420,3569,3734,3891,4042,4161,4533,4682,4831,4943,5090,5243,5390,5465,5554,5641,5742,5845,8697,8883,11747,11945,12145,12268,12391,12504,12687,12818,13019,13108,13219,13452,13553,13648,13771,13900,14017,14194,14293,14428,14571,14706,14825,15026,15145,15238,15349,15405,15512,15707,15818,15951,16046,16137,16228,16345,16484,16555,16638,17261,17318,17376,18000", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,23,24,25,27,29,30,31,32,33,35,37,39,41,43,45,46,51,53,55,56,57,59,61,62,63,64,65,66,110,113,157,160,163,165,167,169,172,174,177,178,179,182,183,184,185,186,187,190,191,193,195,197,199,203,205,206,207,208,210,214,216,218,219,220,221,222,224,225,226,236,237,238,250,262", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,116,12,70,82,12,56,57,12,12", "endOffsets": "440,543,646,751,858,967,1076,1185,1294,1403,1510,1613,1732,1887,2042,2147,2268,2369,2516,2657,2760,2879,2986,3089,3244,3415,3564,3729,3886,4037,4156,4528,4677,4826,4938,5085,5238,5385,5460,5549,5636,5737,5840,8692,8878,11742,11940,12140,12263,12386,12499,12682,12813,13014,13103,13214,13447,13548,13643,13766,13895,14012,14189,14288,14423,14566,14701,14820,15021,15140,15233,15344,15400,15507,15702,15813,15946,16041,16132,16223,16340,16479,16550,16633,17256,17313,17371,17995,18631"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-el_values-el.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-el\\values-el.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2022", "endColumns": "100", "endOffsets": "2118"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,641,767,855,941,1026,1137,1247,1349,1460,1569,1677,1837,1937", "endColumns": "117,110,116,84,104,125,87,85,84,110,109,101,110,108,107,159,99,84", "endOffsets": "218,329,446,531,636,762,850,936,1021,1132,1242,1344,1455,1564,1672,1832,1932,2017"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-eu_values-eu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1987", "endColumns": "100", "endOffsets": "2083"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,825,909,991,1100,1210,1309,1418,1524,1635,1806,1905", "endColumns": "108,97,109,85,105,123,86,83,81,108,109,98,108,105,110,170,98,81", "endOffsets": "209,307,417,503,609,733,820,904,986,1095,1205,1304,1413,1519,1630,1801,1900,1982"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-lo_values-lo.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1897", "endColumns": "100", "endOffsets": "1993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,412,497,602,714,791,869,949,1056,1153,1251,1356,1459,1563,1720,1816", "endColumns": "102,96,106,84,104,111,76,77,79,106,96,97,104,102,103,156,95,80", "endOffsets": "203,300,407,492,597,709,786,864,944,1051,1148,1246,1351,1454,1558,1715,1811,1892"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-tl_values-tl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2010", "endColumns": "100", "endOffsets": "2106"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,332,449,537,643,764,843,921,1005,1114,1225,1326,1436,1553,1661,1824,1926", "endColumns": "118,107,116,87,105,120,78,77,83,108,110,100,109,116,107,162,101,83", "endOffsets": "219,327,444,532,638,759,838,916,1000,1109,1220,1321,1431,1548,1656,1819,1921,2005"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-v23_values-v23.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,19,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1277,2079,2206,2311,2426,2533", "endLines": "2,3,4,5,18,31,32,33,34,35,36", "endColumns": "134,134,74,86,12,12,126,104,114,106,112", "endOffsets": "185,320,395,482,1272,2074,2201,2306,2421,2528,2641"}, "to": {"startLines": "2,3,4,5,6,19,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1131,1787,1914,2019,2134,2241", "endLines": "2,3,4,5,18,31,32,33,34,35,36", "endColumns": "134,134,74,86,12,12,126,104,114,106,112", "endOffsets": "185,320,395,482,1126,1782,1909,2014,2129,2236,2349"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-pt-rPT_values-pt-rPT.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,752,836,917,1004,1111,1223,1325,1433,1540,1647,1818,1917", "endColumns": "119,105,106,88,100,123,83,80,86,106,111,101,107,106,106,170,98,84", "endOffsets": "220,326,433,522,623,747,831,912,999,1106,1218,1320,1428,1535,1642,1813,1912,1997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2002", "endColumns": "100", "endOffsets": "2098"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-v25_values-v25.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-v25\\values-v25.xml", "from": {"startLines": "2,3,4,6", "startColumns": "4,4,4,4", "startOffsets": "55,126,209,308", "endLines": "2,3,5,7", "endColumns": "70,82,12,12", "endOffsets": "121,204,303,414"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-af_values-af.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,415,500,603,721,797,874,955,1062,1165,1262,1370,1472,1574,1728,1826", "endColumns": "103,99,105,84,102,117,75,76,80,106,102,96,107,101,101,153,97,79", "endOffsets": "204,304,410,495,598,716,792,869,950,1057,1160,1257,1365,1467,1569,1723,1821,1901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1906", "endColumns": "100", "endOffsets": "2002"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ru_values-ru.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,434,520,625,745,824,902,982,1088,1196,1294,1403,1509,1617,1792,1892", "endColumns": "114,101,111,85,104,119,78,77,79,105,107,97,108,105,107,174,99,80", "endOffsets": "215,317,429,515,620,740,819,897,977,1083,1191,1289,1398,1504,1612,1787,1887,1968"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1973", "endColumns": "100", "endOffsets": "2069"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-lt_values-lt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,322,435,522,631,752,834,915,1000,1109,1218,1318,1428,1532,1645,1821,1922", "endColumns": "115,100,112,86,108,120,81,80,84,108,108,99,109,103,112,175,100,82", "endOffsets": "216,317,430,517,626,747,829,910,995,1104,1213,1313,1423,1527,1640,1816,1917,2000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2005", "endColumns": "100", "endOffsets": "2101"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-en-rGB_values-en-rGB.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,793,869,951,1054,1158,1257,1362,1465,1569,1725,1825", "endColumns": "103,99,107,83,99,114,76,75,81,102,103,98,104,102,103,155,99,81", "endOffsets": "204,304,412,496,596,711,788,864,946,1049,1153,1252,1357,1460,1564,1720,1820,1902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1907", "endColumns": "100", "endOffsets": "2003"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-gl_values-gl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1972", "endColumns": "100", "endOffsets": "2068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,329,437,522,624,750,834,915,997,1104,1213,1312,1420,1524,1631,1790,1890", "endColumns": "111,111,107,84,101,125,83,80,81,106,108,98,107,103,106,158,99,81", "endOffsets": "212,324,432,517,619,745,829,910,992,1099,1208,1307,1415,1519,1626,1785,1885,1967"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-mr_values-mr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,735,812,890,970,1082,1184,1280,1389,1490,1605,1762,1867", "endColumns": "110,105,106,89,100,114,76,77,79,111,101,95,108,100,114,156,104,79", "endOffsets": "211,317,424,514,615,730,807,885,965,1077,1179,1275,1384,1485,1600,1757,1862,1942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1947", "endColumns": "100", "endOffsets": "2043"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-hdpi-v4_values-hdpi-v4.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-hdpi-v4\\values-hdpi-v4.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "6", "endColumns": "13", "endOffsets": "327"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-pt-rBR_values-pt-rBR.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,746,829,909,996,1103,1215,1317,1425,1532,1642,1804,1904", "endColumns": "119,105,106,88,100,117,82,79,86,106,111,101,107,106,109,161,99,84", "endOffsets": "220,326,433,522,623,741,824,904,991,1098,1210,1312,1420,1527,1637,1799,1899,1984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1989", "endColumns": "100", "endOffsets": "2085"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-zu_values-zu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,434,522,625,752,832,912,998,1102,1208,1306,1413,1519,1625,1782,1878", "endColumns": "107,106,113,87,102,126,79,79,85,103,105,97,106,105,105,156,95,80", "endOffsets": "208,315,429,517,620,747,827,907,993,1097,1203,1301,1408,1514,1620,1777,1873,1954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1959", "endColumns": "100", "endOffsets": "2055"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-v14_values-v14.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-v14\\values-v14.xml", "from": {"startLines": "2,7,8,9,15", "startColumns": "4,4,4,4,4", "startOffsets": "55,331,402,485,895", "endLines": "6,7,8,14,20", "endColumns": "12,70,82,12,12", "endOffsets": "326,397,480,890,1312"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-km_values-km.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-km\\values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1943", "endColumns": "100", "endOffsets": "2039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,308,420,507,611,729,806,883,966,1070,1175,1275,1385,1492,1600,1762,1860", "endColumns": "102,99,111,86,103,117,76,76,82,103,104,99,109,106,107,161,97,82", "endOffsets": "203,303,415,502,606,724,801,878,961,1065,1170,1270,1380,1487,1595,1757,1855,1938"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-hu_values-hu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2021", "endColumns": "100", "endOffsets": "2117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,433,517,629,759,835,911,994,1104,1215,1315,1426,1534,1653,1835,1938", "endColumns": "107,104,114,83,111,129,75,75,82,109,110,99,110,107,118,181,102,82", "endOffsets": "208,313,428,512,624,754,830,906,989,1099,1210,1310,1421,1529,1648,1830,1933,2016"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-lv_values-lv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,333,442,528,632,754,836,918,1003,1112,1224,1323,1434,1543,1648,1822,1921", "endColumns": "119,107,108,85,103,121,81,81,84,108,111,98,110,108,104,173,98,81", "endOffsets": "220,328,437,523,627,749,831,913,998,1107,1219,1318,1429,1538,1643,1817,1916,1998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2003", "endColumns": "100", "endOffsets": "2099"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-fr-rCA_values-fr-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2023", "endColumns": "100", "endOffsets": "2119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,442,529,645,775,858,938,1034,1144,1256,1359,1470,1577,1679,1838,1937", "endColumns": "110,114,110,86,115,129,82,79,95,109,111,102,110,106,101,158,98,85", "endOffsets": "211,326,437,524,640,770,853,933,1029,1139,1251,1354,1465,1572,1674,1833,1932,2018"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-h720dp-v13_values-h720dp-v13.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-h720dp-v13\\values-h720dp-v13.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "66", "endOffsets": "117"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-v18_values-v18.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-v18\\values-v18.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "48", "endOffsets": "99"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-gu_values-gu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,318,425,512,613,736,813,891,971,1077,1179,1276,1385,1484,1594,1754,1857", "endColumns": "108,103,106,86,100,122,76,77,79,105,101,96,108,98,109,159,102,79", "endOffsets": "209,313,420,507,608,731,808,886,966,1072,1174,1271,1380,1479,1589,1749,1852,1932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1937", "endColumns": "100", "endOffsets": "2033"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-pa_values-pa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1900", "endColumns": "100", "endOffsets": "1996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,786,862,941,1042,1146,1243,1352,1451,1561,1720,1820", "endColumns": "102,96,104,85,99,112,76,75,78,100,103,96,108,98,109,158,99,79", "endOffsets": "203,300,405,491,591,704,781,857,936,1037,1141,1238,1347,1446,1556,1715,1815,1895"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-mn_values-mn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,318,431,518,624,736,818,900,982,1091,1195,1292,1400,1501,1604,1763,1860", "endColumns": "112,99,112,86,105,111,81,81,81,108,103,96,107,100,102,158,96,80", "endOffsets": "213,313,426,513,619,731,813,895,977,1086,1190,1287,1395,1496,1599,1758,1855,1936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1941", "endColumns": "100", "endOffsets": "2037"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-bg_values-bg.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1977", "endColumns": "100", "endOffsets": "2073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,327,432,518,623,744,823,901,984,1098,1207,1307,1421,1527,1635,1795,1894", "endColumns": "114,106,104,85,104,120,78,77,82,113,108,99,113,105,107,159,98,82", "endOffsets": "215,322,427,513,618,739,818,896,979,1093,1202,1302,1416,1522,1630,1790,1889,1972"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-sk_values-sk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1972", "endColumns": "100", "endOffsets": "2068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,312,423,509,617,735,816,897,981,1086,1195,1294,1400,1511,1620,1786,1884", "endColumns": "106,99,110,85,107,117,80,80,83,104,108,98,105,110,108,165,97,87", "endOffsets": "207,307,418,504,612,730,811,892,976,1081,1190,1289,1395,1506,1615,1781,1879,1967"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values_values.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\Documents\\HBuilderProjects\\flarum-app\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,10", "startColumns": "4,4", "startOffsets": "57,384", "endLines": "8,14", "endColumns": "12,12", "endOffsets": "376,635"}, "to": {"startLines": "338,344", "startColumns": "4,4", "startOffsets": "20969,21253", "endLines": "343,348", "endColumns": "12,12", "endOffsets": "21248,21500"}}, {"source": "C:\\Users\\<USER>\\Documents\\HBuilderProjects\\flarum-app\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "4,2,3", "startColumns": "4,4,4", "startOffsets": "157,57,105", "endColumns": "45,46,50", "endOffsets": "198,99,151"}, "to": {"startLines": "118,119,120", "startColumns": "4,4,4", "startOffsets": "6239,6285,6332", "endColumns": "45,46,50", "endOffsets": "6280,6327,6378"}}, {"source": "C:\\Users\\<USER>\\Documents\\HBuilderProjects\\flarum-app\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,2,4,3", "startColumns": "4,4,4,4", "startOffsets": "17,65,253,161", "endColumns": "46,94,75,90", "endOffsets": "59,155,324,247"}, "to": {"startLines": "327,328,329,330", "startColumns": "4,4,4,4", "startOffsets": "20094,20141,20236,20312", "endColumns": "46,94,75,90", "endOffsets": "20136,20231,20307,20398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\00e405b0cf73f315c97110fd1c7d2138\\transformed\\constraint-layout-1.0.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,7,8,11,14,15,18,21,22,23,24,28,29,30,31,36,37,38,41,44,45,48,51,54,57,58,61,64,65,70,71,75,76,77,78,79,80,81,82,83,84,85,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,177,310,378,507,633,700,823,948,1015,1080,1145,1285,1351,1417,1484,1694,1763,1829,1954,2080,2147,2273,2400,2525,2652,2717,2843,2966,3031,3239,3306,3445,3510,3575,3637,3699,3761,3820,3880,3941,4002,4061,4270,6417,9095", "endLines": "2,3,6,7,10,13,14,17,20,21,22,23,27,28,29,30,35,36,37,40,43,44,47,50,53,56,57,60,63,64,69,70,74,75,76,77,78,79,80,81,82,83,84,90,91,92,93", "endColumns": "51,69,11,67,11,11,66,11,11,66,64,64,11,65,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,64,64,61,61,61,58,59,60,60,58,11,2146,2677,107", "endOffsets": "102,172,305,373,502,628,695,818,943,1010,1075,1140,1280,1346,1412,1479,1689,1758,1824,1949,2075,2142,2268,2395,2520,2647,2712,2838,2961,3026,3234,3301,3440,3505,3570,3632,3694,3756,3815,3875,3936,3997,4056,4265,6412,9090,9198"}, "to": {"startLines": "2,6,7,10,11,14,17,18,21,24,25,26,27,31,32,33,34,39,40,41,44,47,48,51,54,57,60,61,64,67,68,73,74,78,79,80,81,82,83,84,85,86,87,88,1812,1813,1828", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,306,376,509,577,706,832,899,1022,1147,1214,1279,1344,1484,1550,1616,1683,1893,1962,2028,2153,2279,2346,2472,2599,2724,2851,2916,3042,3165,3230,3438,3505,3644,3709,3774,3836,3898,3960,4019,4079,4140,4201,4260,126268,128415,132442", "endLines": "2,6,9,10,13,16,17,20,23,24,25,26,30,31,32,33,38,39,40,43,46,47,50,53,56,59,60,63,66,67,72,73,77,78,79,80,81,82,83,84,85,86,87,93,1812,1813,1828", "endColumns": "51,69,11,67,11,11,66,11,11,66,64,64,11,65,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,64,64,61,61,61,58,59,60,60,58,11,2146,2677,107", "endOffsets": "152,371,504,572,701,827,894,1017,1142,1209,1274,1339,1479,1545,1611,1678,1888,1957,2023,2148,2274,2341,2467,2594,2719,2846,2911,3037,3160,3225,3433,3500,3639,3704,3769,3831,3893,3955,4014,4074,4135,4196,4255,4464,128410,131088,132545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,397,398,402,406,410,415,421,428,432,436,441,445,449,453,457,461,465,469,473,477,481,485,489,494,498,501,505,509,513,517,521,525,528,532,536,540,544,548,549,550,551,554,557,560,563,567,568,569,570,571,574,576,578,580,585,586,590,596,600,601,603,614,615,619,625,629,630,631,635,662,666,667,671,699,865,887,1053,1075,1102,1109,1114,1128,1150,1155,1160,1170,1179,1188,1192,1199,1207,1214,1215,1224,1227,1230,1234,1238,1242,1245,1246,1250,1254,1264,1269,1276,1282,1283,1286,1290,1295,1297,1299,1302,1305,1307,1311,1314,1321,1324,1327,1331,1333,1337,1339,1345,1347,1351,1359,1367,1379,1385,1394,1397,1408,1411,1416,1417,1422,1464,1507,1508,1518,1527,1528,1530,1534,1537,1540,1543,1546,1550,1553,1556,1559,1563,1566,1570,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1596,1598,1599,1600,1601,1602,1603,1604,1605,1607,1608,1610,1611,1613,1615,1616,1618,1619,1620,1621,1622,1623,1625,1626,1627,1628,1629,1630,1632,1634,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1650,1651,1652,1653,1654,1655,1657,1661,1665,1666,1667,1668,1669,1670,1671,1672,1674,1676,1678,1680,1682,1683,1684,1685,1687,1689,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1705,1706,1707,1708,1710,1712,1713,1715,1716,1718,1720,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1735,1736,1737,1738,1740,1741,1742,1743,1744,1746,1748,1750,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,70,86,87,88,89,90,91,92,106,120,121,127,130,131,132,146,147,153,154,155,156,195,196,197,198,199,200,201,228,229,235,236,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,408,470,538,616,697,758,833,909,986,1064,1149,1231,1307,1383,1460,1538,1644,1750,1829,1909,1966,2024,2098,2173,2238,2304,2359,2431,2504,2571,2639,2698,2757,2816,2875,2934,2988,3042,3095,3149,3203,3257,3311,3385,3464,3537,3611,3682,3754,3826,3899,3956,4014,4087,4161,4235,4310,4382,4455,4525,4596,4656,27028,27097,27166,27236,27310,27386,27450,27527,27603,27680,27744,27809,27878,27955,28030,28099,28167,28244,28310,28407,28472,28541,28640,28711,28770,28828,28885,28944,29015,29087,29159,29231,29303,29370,29438,29506,29565,29628,29692,29782,29873,29933,29999,30066,30132,30202,30266,30319,30432,30490,30553,30618,30683,30758,30831,30903,30952,31013,31074,31135,31197,31261,31325,31389,31454,31517,31577,31638,31704,31763,31823,31885,31956,32016,32084,32170,32257,32347,32434,32522,32604,32687,32777,32868,32920,32978,33023,33089,33153,33210,33267,33321,33378,33426,33475,33526,33560,33607,33656,33702,33734,33798,33860,33920,33977,34051,34121,34199,34253,34323,34408,34456,34502,34573,34651,34729,34801,34875,34949,35023,35103,35176,35245,35317,35394,35446,35519,35593,35662,35737,35811,35885,36026,36096,36149,36227,36317,36405,36501,36591,37092,37181,37428,37709,37953,38238,38631,39108,39330,39552,39820,40047,40277,40507,40737,40967,41194,41433,41659,41904,42134,42382,42601,42884,43092,43223,43450,43696,43921,44168,44389,44634,44754,45030,45331,45655,45946,46260,46397,46528,46633,46875,47042,47246,47454,47725,47837,47949,48054,48171,48385,48531,48671,48757,49105,49193,49423,49841,50074,50156,50254,50871,50971,51207,51631,51870,51964,52053,52290,54342,54584,54686,54923,57107,67644,68895,79529,80792,82284,82820,83150,84211,85476,85732,85968,86515,87009,87614,87812,88392,88956,89331,89449,89987,90144,90340,90613,90869,91039,91180,91244,91526,91812,92488,92752,93090,93443,93537,93723,94029,94291,94416,94543,94782,94993,95112,95305,95482,95937,96118,96240,96499,96612,96799,96901,97345,97469,97744,98252,98748,99625,99919,100489,100638,101370,101542,101878,101970,102248,105434,108763,108825,109455,110069,110155,110268,110497,110657,110809,110980,111147,111390,111560,111733,111904,112178,112377,112582,112912,112996,113092,113188,113286,113386,113488,113590,113692,113794,113896,113996,114092,114204,114333,114456,114587,114718,114816,114930,115024,115164,115298,115394,115506,115606,115722,115818,115930,116030,116170,116306,116470,116600,116758,116908,117049,117193,117328,117440,117590,117718,117846,117982,118114,118244,118374,118486,118626,118772,118916,119054,119120,119210,119286,119390,119480,119582,119690,119798,119898,119978,120070,120168,120278,120356,120462,120554,120658,120768,120890,121053,121210,121290,121390,121480,121590,121684,121790,121882,121982,122094,122208,122324,122440,122534,122648,122760,122862,122982,123104,123186,123290,123410,123536,123634,123728,123816,123928,124044,124166,124278,124453,124569,124655,124747,124859,124983,125050,125176,125244,125372,125516,125644,125713,125808,125923,126036,126135,126244,126355,126466,126567,126672,126772,126902,126993,127116,127210,127322,127408,127512,127608,127696,127814,127918,128022,128148,128236,128344,128444,128534,128644,128728,128830,128914,128968,129032,129138,129248,129332,4717,6632,6736,6837,6884,7132,7324,7715,8274,8848,9168,9757,16393,16505,16666,17193,17616,18209,18430,18589,18848,20975,21414,21602,21730,21904,22685,22902,24233,24712,26085,26330,26875", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,396,397,401,405,409,414,420,427,431,435,440,444,448,452,456,460,464,468,472,476,480,484,488,493,497,500,504,508,512,516,520,524,527,531,535,539,543,547,548,549,550,553,556,559,562,566,567,568,569,570,573,575,577,579,584,585,589,595,599,600,602,613,614,618,624,628,629,630,634,661,665,666,670,698,864,886,1052,1074,1101,1108,1113,1127,1149,1154,1159,1169,1178,1187,1191,1198,1206,1213,1214,1223,1226,1229,1233,1237,1241,1244,1245,1249,1253,1263,1268,1275,1281,1282,1285,1289,1294,1296,1298,1301,1304,1306,1310,1313,1320,1323,1326,1330,1332,1336,1338,1344,1346,1350,1358,1366,1378,1384,1393,1396,1407,1410,1415,1416,1421,1463,1506,1507,1517,1526,1527,1529,1533,1536,1539,1542,1545,1549,1552,1555,1558,1562,1565,1569,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1595,1597,1598,1599,1600,1601,1602,1603,1604,1606,1607,1609,1610,1612,1614,1615,1617,1618,1619,1620,1621,1622,1624,1625,1626,1627,1628,1629,1631,1633,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1649,1650,1651,1652,1653,1654,1656,1660,1664,1665,1666,1667,1668,1669,1670,1671,1673,1675,1677,1679,1681,1682,1683,1684,1686,1688,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1704,1705,1706,1707,1709,1711,1712,1714,1715,1717,1719,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1734,1735,1736,1737,1739,1740,1741,1742,1743,1745,1747,1749,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,85,86,87,88,89,90,91,105,119,120,126,129,130,131,145,146,152,153,154,155,194,195,196,197,198,199,200,227,228,234,235,249,250", "endColumns": "54,44,48,40,54,57,61,67,77,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,54,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,63,64,68,76,74,68,67,76,65,96,64,68,98,70,58,57,56,58,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,70,77,77,71,73,73,73,79,72,68,71,76,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,91,12,12,12,61,12,12,85,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,109,83,119,1290,103,100,46,247,191,390,35,35,319,292,5296,111,160,35,422,83,220,158,258,35,438,187,127,173,780,216,375,478,458,244,35,152", "endOffsets": "155,200,249,290,345,403,465,533,611,692,753,828,904,981,1059,1144,1226,1302,1378,1455,1533,1639,1745,1824,1904,1961,2019,2093,2168,2233,2299,2354,2426,2499,2566,2634,2693,2752,2811,2870,2929,2983,3037,3090,3144,3198,3252,3306,3380,3459,3532,3606,3677,3749,3821,3894,3951,4009,4082,4156,4230,4305,4377,4450,4520,4591,4651,4712,27092,27161,27231,27305,27381,27445,27522,27598,27675,27739,27804,27873,27950,28025,28094,28162,28239,28305,28402,28467,28536,28635,28706,28765,28823,28880,28939,29010,29082,29154,29226,29298,29365,29433,29501,29560,29623,29687,29777,29868,29928,29994,30061,30127,30197,30261,30314,30427,30485,30548,30613,30678,30753,30826,30898,30947,31008,31069,31130,31192,31256,31320,31384,31449,31512,31572,31633,31699,31758,31818,31880,31951,32011,32079,32165,32252,32342,32429,32517,32599,32682,32772,32863,32915,32973,33018,33084,33148,33205,33262,33316,33373,33421,33470,33521,33555,33602,33651,33697,33729,33793,33855,33915,33972,34046,34116,34194,34248,34318,34403,34451,34497,34568,34646,34724,34796,34870,34944,35018,35098,35171,35240,35312,35389,35441,35514,35588,35657,35732,35806,35880,36021,36091,36144,36222,36312,36400,36496,36586,37087,37176,37423,37704,37948,38233,38626,39103,39325,39547,39815,40042,40272,40502,40732,40962,41189,41428,41654,41899,42129,42377,42596,42879,43087,43218,43445,43691,43916,44163,44384,44629,44749,45025,45326,45650,45941,46255,46392,46523,46628,46870,47037,47241,47449,47720,47832,47944,48049,48166,48380,48526,48666,48752,49100,49188,49418,49836,50069,50151,50249,50866,50966,51202,51626,51865,51959,52048,52285,54337,54579,54681,54918,57102,67639,68890,79524,80787,82279,82815,83145,84206,85471,85727,85963,86510,87004,87609,87807,88387,88951,89326,89444,89982,90139,90335,90608,90864,91034,91175,91239,91521,91807,92483,92747,93085,93438,93532,93718,94024,94286,94411,94538,94777,94988,95107,95300,95477,95932,96113,96235,96494,96607,96794,96896,97340,97464,97739,98247,98743,99620,99914,100484,100633,101365,101537,101873,101965,102243,105429,108758,108820,109450,110064,110150,110263,110492,110652,110804,110975,111142,111385,111555,111728,111899,112173,112372,112577,112907,112991,113087,113183,113281,113381,113483,113585,113687,113789,113891,113991,114087,114199,114328,114451,114582,114713,114811,114925,115019,115159,115293,115389,115501,115601,115717,115813,115925,116025,116165,116301,116465,116595,116753,116903,117044,117188,117323,117435,117585,117713,117841,117977,118109,118239,118369,118481,118621,118767,118911,119049,119115,119205,119281,119385,119475,119577,119685,119793,119893,119973,120065,120163,120273,120351,120457,120549,120653,120763,120885,121048,121205,121285,121385,121475,121585,121679,121785,121877,121977,122089,122203,122319,122435,122529,122643,122755,122857,122977,123099,123181,123285,123405,123531,123629,123723,123811,123923,124039,124161,124273,124448,124564,124650,124742,124854,124978,125045,125171,125239,125367,125511,125639,125708,125803,125918,126031,126130,126239,126350,126461,126562,126667,126767,126897,126988,127111,127205,127317,127403,127507,127603,127691,127809,127913,128017,128143,128231,128339,128439,128529,128639,128723,128825,128909,128963,129027,129133,129243,129327,129447,6627,6731,6832,6879,7127,7319,7710,8269,8843,9163,9752,16388,16500,16661,17188,17611,18204,18425,18584,18843,20970,21409,21597,21725,21899,22680,22897,24228,24707,26080,26325,26870,27023"}, "to": {"startLines": "3,4,5,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,121,122,123,124,125,126,127,128,129,131,132,133,134,135,136,137,138,139,140,141,142,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,244,245,246,247,248,249,250,251,252,268,269,270,271,272,273,274,275,278,279,280,281,282,285,286,287,292,293,294,295,296,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,331,333,334,335,336,337,349,356,357,361,365,369,374,380,387,391,395,400,404,408,412,416,420,424,428,432,436,440,444,448,453,457,460,464,468,472,476,480,484,487,491,495,499,503,507,508,509,510,513,516,519,522,526,527,528,529,530,533,535,537,539,544,545,549,555,559,560,562,573,574,578,584,588,589,590,594,621,625,626,630,658,823,845,1010,1032,1059,1066,1071,1085,1107,1112,1117,1127,1136,1145,1149,1156,1164,1171,1172,1181,1184,1187,1191,1195,1199,1202,1203,1207,1211,1221,1226,1233,1239,1240,1243,1247,1252,1254,1256,1259,1262,1264,1268,1271,1278,1281,1284,1288,1290,1294,1296,1302,1304,1308,1316,1324,1336,1342,1351,1354,1365,1368,1373,1374,1379,1421,1464,1465,1475,1484,1485,1487,1491,1494,1497,1500,1503,1507,1510,1513,1516,1520,1523,1527,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1564,1565,1567,1568,1570,1572,1573,1575,1576,1577,1578,1579,1580,1582,1583,1584,1585,1586,1598,1600,1602,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1618,1619,1620,1621,1622,1623,1625,1629,1633,1634,1635,1636,1637,1638,1639,1640,1642,1644,1646,1648,1650,1651,1652,1653,1655,1657,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1673,1674,1675,1676,1678,1680,1681,1683,1684,1686,1688,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1703,1704,1705,1706,1708,1709,1710,1711,1712,1714,1716,1718,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1736,1752,1753,1754,1755,1756,1757,1758,1772,1786,1787,1793,1796,1797,1798,1814,1829,1835,1836,1837,1838,1877,1878,1879,1880,1881,1882,1883,1910,1911,1917,1918,1932", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "157,212,257,4469,4510,4565,4623,4685,4753,4831,4912,4973,5048,5124,5201,5279,5364,5446,5522,5598,5675,5753,5859,5965,6044,6124,6181,6383,6457,6532,6597,6663,6718,6790,6863,6930,7055,7114,7173,7232,7291,7350,7404,7458,7511,7565,7619,7673,7859,7933,8012,8085,8159,8230,8302,8374,8447,8504,8562,8635,8709,8783,8858,8930,9003,9073,9144,9204,9265,9334,9403,9473,9547,9623,9687,9764,9840,9917,9981,10046,10115,10192,10267,10336,10404,10481,10547,10644,10709,10778,10877,10948,11007,11065,11122,11181,11252,11324,11396,11468,11540,11607,11675,11743,11802,11865,11929,12019,12110,12170,12236,12303,12369,12439,12503,12556,12669,12727,12790,12855,12920,12995,13068,13140,13189,13250,13311,13372,13434,13498,13562,13626,13691,13754,13814,13875,13941,14000,14060,14122,14193,14253,14662,14748,14835,14925,15012,15100,15182,15265,15355,16424,16476,16534,16579,16645,16709,16766,16823,17025,17082,17130,17179,17230,17334,17381,17430,17630,17662,17726,17788,17848,17975,18049,18119,18197,18251,18321,18406,18454,18500,18571,18649,18727,18799,18873,18947,19021,19101,19174,19243,19315,19392,19444,19517,19591,19660,19735,19809,19883,20024,20403,20527,20605,20695,20783,20879,21505,22006,22095,22342,22623,22867,23152,23545,24022,24244,24466,24734,24961,25191,25421,25651,25881,26108,26347,26573,26818,27048,27296,27515,27798,28006,28137,28364,28610,28835,29082,29303,29548,29668,29944,30245,30569,30860,31174,31311,31442,31547,31789,31956,32160,32368,32639,32751,32863,32968,33085,33299,33445,33585,33671,34019,34107,34337,34755,34988,35070,35168,35760,35860,36096,36520,36759,36853,36942,37179,39203,39445,39547,39784,41940,51885,53136,63178,64441,65933,66469,66799,67860,69125,69381,69617,70164,70658,71263,71461,72041,72605,72980,73098,73636,73793,73989,74262,74518,74688,74829,74893,75175,75461,76137,76401,76739,77092,77186,77372,77678,77940,78065,78192,78431,78642,78761,78954,79131,79586,79767,79889,80148,80261,80448,80550,80994,81118,81393,81901,82397,83274,83568,84138,84287,85019,85191,85527,85619,85897,89021,92288,92350,92928,93512,93598,93711,93940,94100,94252,94423,94590,94833,95003,95176,95347,95621,95820,96025,96355,96439,96535,96631,96729,96829,96931,97033,97135,97237,97339,97439,97535,97647,97776,97899,98030,98161,98259,98373,98467,98607,98741,98837,98949,99049,99165,99261,99373,99473,99613,99749,99913,100043,100201,100351,100492,100636,100771,100883,101033,101161,101289,101425,101557,101687,101817,101929,102827,102973,103117,103255,103321,103411,103487,103591,103681,103783,103891,103999,104099,104179,104271,104369,104479,104557,104663,104755,104859,104969,105091,105254,105411,105491,105591,105681,105791,105885,105991,106083,106183,106295,106409,106525,106641,106735,106849,106961,107063,107183,107305,107387,107491,107611,107737,107835,107929,108017,108129,108245,108367,108479,108654,108770,108856,108948,109060,109184,109251,109377,109445,109573,109717,109845,109914,110009,110124,110237,110336,110445,110556,110667,110768,110873,110973,111103,111194,111317,111411,111523,111609,111713,111809,111897,112015,112119,112223,112349,112437,112545,112645,112735,112845,112929,113031,113115,113169,113233,113339,113449,113533,113792,115707,115811,115912,115959,116207,116399,116790,117349,117923,118243,118832,125468,125580,125741,131093,132550,133143,133364,133523,133782,135909,136348,136536,136664,136838,137619,137836,139167,139646,141019,141264,141809", "endLines": "3,4,5,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,121,122,123,124,125,126,127,128,129,131,132,133,134,135,136,137,138,139,140,141,142,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,244,245,246,247,248,249,250,251,252,268,269,270,271,272,273,274,275,278,279,280,281,282,285,286,287,292,293,294,295,296,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,331,333,334,335,336,337,355,356,360,364,368,373,379,386,390,394,399,403,407,411,415,419,423,427,431,435,439,443,447,452,456,459,463,467,471,475,479,483,486,490,494,498,502,506,507,508,509,512,515,518,521,525,526,527,528,529,532,534,536,538,543,544,548,554,558,559,561,572,573,577,583,587,588,589,593,620,624,625,629,657,822,844,1009,1031,1058,1065,1070,1084,1106,1111,1116,1126,1135,1144,1148,1155,1163,1170,1171,1180,1183,1186,1190,1194,1198,1201,1202,1206,1210,1220,1225,1232,1238,1239,1242,1246,1251,1253,1255,1258,1261,1263,1267,1270,1277,1280,1283,1287,1289,1293,1295,1301,1303,1307,1315,1323,1335,1341,1350,1353,1364,1367,1372,1373,1378,1420,1463,1464,1474,1483,1484,1486,1490,1493,1496,1499,1502,1506,1509,1512,1515,1519,1522,1526,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1552,1554,1555,1556,1557,1558,1559,1560,1561,1563,1564,1566,1567,1569,1571,1572,1574,1575,1576,1577,1578,1579,1581,1582,1583,1584,1585,1586,1599,1601,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1617,1618,1619,1620,1621,1622,1624,1628,1632,1633,1634,1635,1636,1637,1638,1639,1641,1643,1645,1647,1649,1650,1651,1652,1654,1656,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1672,1673,1674,1675,1677,1679,1680,1682,1683,1685,1687,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1702,1703,1704,1705,1707,1708,1709,1710,1711,1713,1715,1717,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1751,1752,1753,1754,1755,1756,1757,1771,1785,1786,1792,1795,1796,1797,1811,1814,1834,1835,1836,1837,1876,1877,1878,1879,1880,1881,1882,1909,1910,1916,1917,1931,1932", "endColumns": "54,44,48,40,54,57,61,67,77,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,54,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,63,64,68,76,74,68,67,76,65,96,64,68,98,70,58,57,56,58,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,70,77,77,71,73,73,73,79,72,68,71,76,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,91,12,12,12,61,12,12,85,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,109,83,119,1290,103,100,46,247,191,390,35,35,319,292,5296,111,160,35,422,83,220,158,258,35,438,187,127,173,780,216,375,478,458,244,35,152", "endOffsets": "207,252,301,4505,4560,4618,4680,4748,4826,4907,4968,5043,5119,5196,5274,5359,5441,5517,5593,5670,5748,5854,5960,6039,6119,6176,6234,6452,6527,6592,6658,6713,6785,6858,6925,6993,7109,7168,7227,7286,7345,7399,7453,7506,7560,7614,7668,7722,7928,8007,8080,8154,8225,8297,8369,8442,8499,8557,8630,8704,8778,8853,8925,8998,9068,9139,9199,9260,9329,9398,9468,9542,9618,9682,9759,9835,9912,9976,10041,10110,10187,10262,10331,10399,10476,10542,10639,10704,10773,10872,10943,11002,11060,11117,11176,11247,11319,11391,11463,11535,11602,11670,11738,11797,11860,11924,12014,12105,12165,12231,12298,12364,12434,12498,12551,12664,12722,12785,12850,12915,12990,13063,13135,13184,13245,13306,13367,13429,13493,13557,13621,13686,13749,13809,13870,13936,13995,14055,14117,14188,14248,14316,14743,14830,14920,15007,15095,15177,15260,15350,15441,16471,16529,16574,16640,16704,16761,16818,16872,17077,17125,17174,17225,17259,17376,17425,17471,17657,17721,17783,17843,17900,18044,18114,18192,18246,18316,18401,18449,18495,18566,18644,18722,18794,18868,18942,19016,19096,19169,19238,19310,19387,19439,19512,19586,19655,19730,19804,19878,20019,20089,20451,20600,20690,20778,20874,20964,22001,22090,22337,22618,22862,23147,23540,24017,24239,24461,24729,24956,25186,25416,25646,25876,26103,26342,26568,26813,27043,27291,27510,27793,28001,28132,28359,28605,28830,29077,29298,29543,29663,29939,30240,30564,30855,31169,31306,31437,31542,31784,31951,32155,32363,32634,32746,32858,32963,33080,33294,33440,33580,33666,34014,34102,34332,34750,34983,35065,35163,35755,35855,36091,36515,36754,36848,36937,37174,39198,39440,39542,39779,41935,51880,53131,63173,64436,65928,66464,66794,67855,69120,69376,69612,70159,70653,71258,71456,72036,72600,72975,73093,73631,73788,73984,74257,74513,74683,74824,74888,75170,75456,76132,76396,76734,77087,77181,77367,77673,77935,78060,78187,78426,78637,78756,78949,79126,79581,79762,79884,80143,80256,80443,80545,80989,81113,81388,81896,82392,83269,83563,84133,84282,85014,85186,85522,85614,85892,89016,92283,92345,92923,93507,93593,93706,93935,94095,94247,94418,94585,94828,94998,95171,95342,95616,95815,96020,96350,96434,96530,96626,96724,96824,96926,97028,97130,97232,97334,97434,97530,97642,97771,97894,98025,98156,98254,98368,98462,98602,98736,98832,98944,99044,99160,99256,99368,99468,99608,99744,99908,100038,100196,100346,100487,100631,100766,100878,101028,101156,101284,101420,101552,101682,101812,101924,102064,102968,103112,103250,103316,103406,103482,103586,103676,103778,103886,103994,104094,104174,104266,104364,104474,104552,104658,104750,104854,104964,105086,105249,105406,105486,105586,105676,105786,105880,105986,106078,106178,106290,106404,106520,106636,106730,106844,106956,107058,107178,107300,107382,107486,107606,107732,107830,107924,108012,108124,108240,108362,108474,108649,108765,108851,108943,109055,109179,109246,109372,109440,109568,109712,109840,109909,110004,110119,110232,110331,110440,110551,110662,110763,110868,110968,111098,111189,111312,111406,111518,111604,111708,111804,111892,112010,112114,112218,112344,112432,112540,112640,112730,112840,112924,113026,113110,113164,113228,113334,113444,113528,113648,115702,115806,115907,115954,116202,116394,116785,117344,117918,118238,118827,125463,125575,125736,126263,131511,133138,133359,133518,133777,135904,136343,136531,136659,136833,137614,137831,139162,139641,141014,141259,141804,141957"}}, {"source": "C:\\Users\\<USER>\\Documents\\HBuilderProjects\\flarum-app\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "56", "endOffsets": "109"}, "to": {"startLines": "130", "startColumns": "4", "startOffsets": "6998", "endColumns": "56", "endOffsets": "7050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values\\values.xml", "from": {"startLines": "2,3,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,54,55,59,60,61,6,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,1245,1315,1383,1455,1525,1586,1647,1708,1770,1834,1896,1957,2025,2125,2185,2251,2324,2393,2450,2502,2564,2636,2712,2747,2782,2832,2866,2901,2936,3006,3077,3194,3395,3505,3706,3835,3907,319,880", "endLines": "2,3,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,53,54,58,59,60,61,14,18", "endColumns": "68,62,69,67,71,69,60,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,34,34,49,33,34,34,69,70,116,12,109,12,128,71,66,35,207", "endOffsets": "119,182,1310,1378,1450,1520,1581,1642,1703,1765,1829,1891,1952,2020,2120,2180,2246,2319,2388,2445,2497,2559,2631,2707,2742,2777,2827,2861,2896,2931,3001,3072,3189,3390,3500,3701,3830,3902,3969,875,1240"}, "to": {"startLines": "143,144,239,240,241,242,243,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,276,277,283,284,288,289,290,291,297,332,1587,1588,1592,1593,1597,1734,1735,1815,1824", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7727,7796,14321,14391,14459,14531,14601,15446,15507,15568,15630,15694,15756,15817,15885,15985,16045,16111,16184,16253,16310,16362,16877,16949,17264,17299,17476,17526,17560,17595,17905,20456,102069,102186,102387,102497,102698,113653,113725,131516,132077", "endLines": "143,144,239,240,241,242,243,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,276,277,283,284,288,289,290,291,297,332,1587,1591,1592,1596,1597,1734,1735,1823,1827", "endColumns": "68,62,69,67,71,69,60,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,34,34,49,33,34,34,69,70,116,12,109,12,128,71,66,35,207", "endOffsets": "7791,7854,14386,14454,14526,14596,14657,15502,15563,15625,15689,15751,15812,15880,15980,16040,16106,16179,16248,16305,16357,16419,16944,17020,17294,17329,17521,17555,17590,17625,17970,20522,102181,102382,102492,102693,102822,113720,113787,132072,132437"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-hy_values-hy.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1945", "endColumns": "100", "endOffsets": "2041"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,309,419,508,614,729,811,892,977,1084,1191,1290,1400,1507,1607,1764,1863", "endColumns": "102,100,109,88,105,114,81,80,84,106,106,98,109,106,99,156,98,81", "endOffsets": "203,304,414,503,609,724,806,887,972,1079,1186,1285,1395,1502,1602,1759,1858,1940"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-xlarge-v4_values-xlarge-v4.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-xlarge-v4\\values-xlarge-v4.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,126,197,267,337,405", "endColumns": "70,70,69,69,67,67", "endOffsets": "121,192,262,332,400,468"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ro_values-ro.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,531,643,764,849,930,1013,1125,1238,1338,1452,1557,1663,1827,1930", "endColumns": "120,103,112,87,111,120,84,80,82,111,112,99,113,104,105,163,102,82", "endOffsets": "221,325,438,526,638,759,844,925,1008,1120,1233,1333,1447,1552,1658,1822,1925,2008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2013", "endColumns": "100", "endOffsets": "2109"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ne_values-ne.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2049", "endColumns": "100", "endOffsets": "2145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,314,422,513,620,747,841,931,1019,1129,1245,1348,1463,1565,1680,1851,1963", "endColumns": "104,103,107,90,106,126,93,89,87,109,115,102,114,101,114,170,111,85", "endOffsets": "205,309,417,508,615,742,836,926,1014,1124,1240,1343,1458,1560,1675,1846,1958,2044"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ta_values-ta.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,324,439,528,635,761,839,916,998,1100,1204,1301,1411,1513,1620,1777,1877", "endColumns": "113,104,114,88,106,125,77,76,81,101,103,96,109,101,106,156,99,79", "endOffsets": "214,319,434,523,630,756,834,911,993,1095,1199,1296,1406,1508,1615,1772,1872,1952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1957", "endColumns": "100", "endOffsets": "2053"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-uk_values-uk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,317,423,509,617,735,814,894,975,1081,1188,1286,1394,1500,1609,1779,1879", "endColumns": "109,101,105,85,107,117,78,79,80,105,106,97,107,105,108,169,99,80", "endOffsets": "210,312,418,504,612,730,809,889,970,1076,1183,1281,1389,1495,1604,1774,1874,1955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1960", "endColumns": "100", "endOffsets": "2056"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-kn_values-kn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,335,448,536,643,770,847,924,1006,1122,1233,1332,1445,1549,1663,1827,1927", "endColumns": "117,111,112,87,106,126,76,76,81,115,110,98,112,103,113,163,99,81", "endOffsets": "218,330,443,531,638,765,842,919,1001,1117,1228,1327,1440,1544,1658,1822,1922,2004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2009", "endColumns": "100", "endOffsets": "2105"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-et_values-et.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,329,440,526,628,745,825,903,986,1097,1202,1301,1411,1512,1615,1781,1883", "endColumns": "116,106,110,85,101,116,79,77,82,110,104,98,109,100,102,165,101,81", "endOffsets": "217,324,435,521,623,740,820,898,981,1092,1197,1296,1406,1507,1610,1776,1878,1960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-et\\values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1965", "endColumns": "100", "endOffsets": "2061"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-cs_values-cs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1955", "endColumns": "100", "endOffsets": "2051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,509,614,731,812,893,977,1081,1190,1289,1395,1505,1612,1775,1873", "endColumns": "106,101,108,85,104,116,80,80,83,103,108,98,105,109,106,162,97,81", "endOffsets": "207,309,418,504,609,726,807,888,972,1076,1185,1284,1390,1500,1607,1770,1868,1950"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-hr_values-hr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1982", "endColumns": "100", "endOffsets": "2078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,307,417,503,607,726,810,893,979,1083,1196,1302,1407,1520,1627,1796,1893", "endColumns": "104,96,109,85,103,118,83,82,85,103,112,105,104,112,106,168,96,88", "endOffsets": "205,302,412,498,602,721,805,888,974,1078,1191,1297,1402,1515,1622,1791,1888,1977"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ky_values-ky.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,304,423,507,614,731,809,888,969,1079,1186,1284,1390,1497,1598,1759,1862", "endColumns": "103,94,118,83,106,116,77,78,80,109,106,97,105,106,100,160,102,80", "endOffsets": "204,299,418,502,609,726,804,883,964,1074,1181,1279,1385,1492,1593,1754,1857,1938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1943", "endColumns": "100", "endOffsets": "2039"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-te_values-te.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,328,439,529,634,759,841,923,1007,1120,1228,1327,1438,1540,1657,1823,1924", "endColumns": "113,108,110,89,104,124,81,81,83,112,107,98,110,101,116,165,100,81", "endOffsets": "214,323,434,524,629,754,836,918,1002,1115,1223,1322,1433,1535,1652,1818,1919,2001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-te\\values-te.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2006", "endColumns": "100", "endOffsets": "2102"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-land_values-land.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,125,189,260", "endColumns": "69,63,70,67", "endOffsets": "120,184,255,323"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-en-rIN_values-en-rIN.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1907", "endColumns": "100", "endOffsets": "2003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,793,869,951,1054,1158,1257,1362,1465,1569,1725,1825", "endColumns": "103,99,107,83,99,114,76,75,81,102,103,98,104,102,103,155,99,81", "endOffsets": "204,304,412,496,596,711,788,864,946,1049,1153,1252,1357,1460,1564,1720,1820,1902"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-zh-rHK_values-zh-rHK.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "101", "endOffsets": "152"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1810", "endColumns": "101", "endOffsets": "1907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,910,1006,1102,1197,1294,1389,1487,1638,1732", "endColumns": "94,92,99,81,96,107,75,75,77,95,95,94,96,94,97,150,93,77", "endOffsets": "195,288,388,470,567,675,751,827,905,1001,1097,1192,1289,1384,1482,1633,1727,1805"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-in_values-in.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1948", "endColumns": "100", "endOffsets": "2044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,317,422,509,613,729,811,890,976,1079,1188,1289,1393,1501,1609,1765,1864", "endColumns": "109,101,104,86,103,115,81,78,85,102,108,100,103,107,107,155,98,83", "endOffsets": "210,312,417,504,608,724,806,885,971,1074,1183,1284,1388,1496,1604,1760,1859,1943"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-it_values-it.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-it\\values-it.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1949", "endColumns": "100", "endOffsets": "2045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,314,423,507,616,741,818,894,975,1081,1189,1287,1391,1496,1603,1766,1866", "endColumns": "108,99,108,83,108,124,76,75,80,105,107,97,103,104,106,162,99,82", "endOffsets": "209,309,418,502,611,736,813,889,970,1076,1184,1282,1386,1491,1598,1761,1861,1944"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-en-rCA_values-en-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,793,869,951,1054,1158,1257,1362,1465,1569,1725,1825", "endColumns": "103,99,107,83,99,114,76,75,81,102,103,98,104,102,103,155,99,81", "endOffsets": "204,304,412,496,596,711,788,864,946,1049,1153,1252,1357,1460,1564,1720,1820,1902"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-night-v8_values-night-v8.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593", "endColumns": "69,83,83,95,101,101,93", "endOffsets": "120,204,288,384,486,588,682"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-uz_values-uz.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1965", "endColumns": "100", "endOffsets": "2061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,314,428,514,625,741,821,900,984,1093,1200,1301,1409,1514,1621,1782,1881", "endColumns": "104,103,113,85,110,115,79,78,83,108,106,100,107,104,106,160,98,83", "endOffsets": "205,309,423,509,620,736,816,895,979,1088,1195,1296,1404,1509,1616,1777,1876,1960"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ar_values-ar.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1918", "endColumns": "100", "endOffsets": "2014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,326,435,517,618,732,811,890,969,1074,1175,1271,1379,1482,1585,1740,1837", "endColumns": "116,103,108,81,100,113,78,78,78,104,100,95,107,102,102,154,96,80", "endOffsets": "217,321,430,512,613,727,806,885,964,1069,1170,1266,1374,1477,1580,1735,1832,1913"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-sl_values-sl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,319,427,514,617,736,821,905,989,1097,1206,1306,1419,1526,1630,1810,1907", "endColumns": "106,106,107,86,102,118,84,83,83,107,108,99,112,106,103,179,96,82", "endOffsets": "207,314,422,509,612,731,816,900,984,1092,1201,1301,1414,1521,1625,1805,1902,1985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1990", "endColumns": "100", "endOffsets": "2086"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-v22_values-v22.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-v22\\values-v22.xml", "from": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,553", "endLines": "2,3,8,13", "endColumns": "74,86,12,12", "endOffsets": "125,212,548,896"}, "to": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,487", "endLines": "2,3,8,13", "endColumns": "74,86,12,12", "endOffsets": "125,212,482,764"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ka_values-ka.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,318,429,517,622,735,819,901,984,1097,1204,1302,1415,1519,1623,1780,1878", "endColumns": "108,103,110,87,104,112,83,81,82,112,106,97,112,103,103,156,97,80", "endOffsets": "209,313,424,512,617,730,814,896,979,1092,1199,1297,1410,1514,1618,1775,1873,1954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1959", "endColumns": "100", "endOffsets": "2055"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-ko_values-ko.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1824", "endColumns": "100", "endOffsets": "1920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,294,396,478,576,682,761,837,917,1015,1110,1205,1305,1401,1500,1652,1746", "endColumns": "94,93,101,81,97,105,78,75,79,97,94,94,99,95,98,151,93,77", "endOffsets": "195,289,391,473,571,677,756,832,912,1010,1105,1200,1300,1396,1495,1647,1741,1819"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-v24_values-v24.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,212", "endColumns": "156,134", "endOffsets": "207,342"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-es_values-es.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1975", "endColumns": "100", "endOffsets": "2071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,335,443,528,629,757,842,924,1006,1113,1213,1312,1420,1527,1634,1793,1893", "endColumns": "116,112,107,84,100,127,84,81,81,106,99,98,107,106,106,158,99,81", "endOffsets": "217,330,438,523,624,752,837,919,1001,1108,1208,1307,1415,1522,1629,1788,1888,1970"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-v26_values-v26.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-v26\\values-v26.xml", "from": {"startLines": "2,3,4,8,12,16", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,130,217,431,657,896", "endLines": "2,3,7,11,15,16", "endColumns": "74,86,12,12,12,92", "endOffsets": "125,212,426,652,891,984"}, "to": {"startLines": "2,3,4,8,12,16", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,130,217,381,557,796", "endLines": "2,3,7,11,15,16", "endColumns": "74,86,12,12,12,92", "endOffsets": "125,212,376,552,791,884"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-my_values-my.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-my\\values-my.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2040", "endColumns": "100", "endOffsets": "2136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,319,436,529,641,769,847,926,1012,1135,1247,1349,1475,1586,1696,1856,1956", "endColumns": "108,104,116,92,111,127,77,78,85,122,111,101,125,110,109,159,99,83", "endOffsets": "209,314,431,524,636,764,842,921,1007,1130,1242,1344,1470,1581,1691,1851,1951,2035"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-es-rUS_values-es-rUS.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,628,751,836,918,1000,1112,1212,1313,1421,1528,1635,1794,1894", "endColumns": "119,108,107,84,100,122,84,81,81,111,99,100,107,106,106,158,99,81", "endOffsets": "220,329,437,522,623,746,831,913,995,1107,1207,1308,1416,1523,1630,1789,1889,1971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1976", "endColumns": "100", "endOffsets": "2072"}}]}, {"outputFile": "com.hhilan.flarum.app-debug-7:/values-en-rXC_values-en-rXC.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,310,510,719,904,1106,1321,1494,1671,1855,2059,2264,2465,2672,2874,3079,3351,3551", "endColumns": "204,199,208,184,201,214,172,176,183,203,204,200,206,201,204,271,199,178", "endOffsets": "305,505,714,899,1101,1316,1489,1666,1850,2054,2259,2460,2667,2869,3074,3346,3546,3725"}}]}]}