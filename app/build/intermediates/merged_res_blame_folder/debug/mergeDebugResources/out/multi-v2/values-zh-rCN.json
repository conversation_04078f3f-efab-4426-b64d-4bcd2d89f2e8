{"logs": [{"outputFile": "com.hhilan.flarum.app-mergeDebugResources-5:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\4d39853c039fc72c0029bb8eeb9366e7\\transformed\\appcompat-v7-27.0.2\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,296,396,478,575,681,757,833,911,1007,1103,1198,1295,1390,1488,1637,1731", "endColumns": "95,94,99,81,96,105,75,75,77,95,95,94,96,94,97,148,93,77", "endOffsets": "196,291,391,473,570,676,752,828,906,1002,1098,1193,1290,1385,1483,1632,1726,1804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\812a19fc1a353943829c2538f5539f5e\\transformed\\support-compat-27.0.2\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1809", "endColumns": "100", "endOffsets": "1905"}}]}]}