com.hhilan.flarum:styleable/ViewStubCompat = 0x7f0d0025
com.hhilan.flarum:styleable/View = 0x7f0d0023
com.hhilan.flarum:styleable/TextAppearance = 0x7f0d0021
com.hhilan.flarum:styleable/Spinner = 0x7f0d001f
com.hhilan.flarum:styleable/SearchView = 0x7f0d001e
com.hhilan.flarum:styleable/RecycleListView = 0x7f0d001d
com.hhilan.flarum:styleable/PopupWindowBackgroundState = 0x7f0d001c
com.hhilan.flarum:styleable/PopupWindow = 0x7f0d001b
com.hhilan.flarum:styleable/MenuView = 0x7f0d001a
com.hhilan.flarum:styleable/MenuItem = 0x7f0d0019
com.hhilan.flarum:styleable/MenuGroup = 0x7f0d0018
com.hhilan.flarum:styleable/LinearLayoutCompat = 0x7f0d0015
com.hhilan.flarum:styleable/ConstraintLayout_Layout = 0x7f0d000f
com.hhilan.flarum:styleable/ButtonBarLayout = 0x7f0d000c
com.hhilan.flarum:styleable/AppCompatTextView = 0x7f0d000a
com.hhilan.flarum:styleable/AppCompatTextHelper = 0x7f0d0009
com.hhilan.flarum:styleable/ActivityChooserView = 0x7f0d0005
com.hhilan.flarum:styleable/ActionMode = 0x7f0d0004
com.hhilan.flarum:styleable/ActionMenuView = 0x7f0d0003
com.hhilan.flarum:styleable/ActionBarLayout = 0x7f0d0001
com.hhilan.flarum:styleable/ActionBar = 0x7f0d0000
com.hhilan.flarum:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f0c015a
com.hhilan.flarum:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f0c0158
com.hhilan.flarum:style/Widget.AppCompat.Spinner = 0x7f0c0156
com.hhilan.flarum:style/Widget.AppCompat.SeekBar = 0x7f0c0154
com.hhilan.flarum:style/Widget.AppCompat.SearchView.ActionBar = 0x7f0c0153
com.hhilan.flarum:style/Widget.AppCompat.SearchView = 0x7f0c0152
com.hhilan.flarum:style/Widget.AppCompat.RatingBar.Small = 0x7f0c0151
com.hhilan.flarum:style/Widget.AppCompat.RatingBar.Indicator = 0x7f0c0150
com.hhilan.flarum:style/Widget.AppCompat.RatingBar = 0x7f0c014f
com.hhilan.flarum:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f0c014e
com.hhilan.flarum:style/Widget.AppCompat.ProgressBar = 0x7f0c014d
com.hhilan.flarum:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f0c014b
com.hhilan.flarum:style/Widget.AppCompat.PopupMenu = 0x7f0c014a
com.hhilan.flarum:style/Widget.AppCompat.ListView.DropDown = 0x7f0c0148
com.hhilan.flarum:style/Widget.AppCompat.ListView = 0x7f0c0147
com.hhilan.flarum:style/Widget.AppCompat.ListMenuView = 0x7f0c0145
com.hhilan.flarum:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f0c0144
com.hhilan.flarum:styleable/ListPopupWindow = 0x7f0d0017
com.hhilan.flarum:style/Widget.AppCompat.Light.SearchView = 0x7f0c0143
com.hhilan.flarum:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0c0142
com.hhilan.flarum:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f0c013e
com.hhilan.flarum:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f0c013d
com.hhilan.flarum:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f0c013c
com.hhilan.flarum:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f0c013a
com.hhilan.flarum:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f0c0136
com.hhilan.flarum:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0c0132
com.hhilan.flarum:style/Widget.AppCompat.ImageButton = 0x7f0c012e
com.hhilan.flarum:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f0c012c
com.hhilan.flarum:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f0c0129
com.hhilan.flarum:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f0c0128
com.hhilan.flarum:style/Widget.AppCompat.Button.Small = 0x7f0c0125
com.hhilan.flarum:style/Widget.AppCompat.AutoCompleteTextView = 0x7f0c011f
com.hhilan.flarum:style/Widget.AppCompat.ActionMode = 0x7f0c011d
com.hhilan.flarum:style/Widget.AppCompat.ActionButton.Overflow = 0x7f0c011c
com.hhilan.flarum:style/Widget.AppCompat.ActionButton = 0x7f0c011a
com.hhilan.flarum:style/Widget.AppCompat.ActionBar.TabText = 0x7f0c0118
com.hhilan.flarum:style/Widget.AppCompat.ActionBar.TabBar = 0x7f0c0117
com.hhilan.flarum:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0c0113
com.hhilan.flarum:style/ThemeOverlay.AppCompat.Dark = 0x7f0c0110
com.hhilan.flarum:style/ThemeOverlay.AppCompat.ActionBar = 0x7f0c010f
com.hhilan.flarum:style/Theme.AppCompat.Light.NoActionBar = 0x7f0c010c
com.hhilan.flarum:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f0c010b
com.hhilan.flarum:style/Theme.AppCompat.Light.Dialog = 0x7f0c0108
com.hhilan.flarum:style/Theme.AppCompat.Light.DarkActionBar = 0x7f0c0107
com.hhilan.flarum:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f0c0100
com.hhilan.flarum:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f0c00ff
com.hhilan.flarum:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f0c00fe
com.hhilan.flarum:style/Theme.AppCompat.DayNight.Dialog = 0x7f0c00fd
com.hhilan.flarum:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f0c00fc
com.hhilan.flarum:style/Theme.AppCompat = 0x7f0c00f9
com.hhilan.flarum:styleable/ViewBackgroundHelper = 0x7f0d0024
com.hhilan.flarum:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0c00f8
com.hhilan.flarum:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f0c0109
com.hhilan.flarum:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0c00f7
com.hhilan.flarum:style/TextAppearance.Compat.Notification.Title = 0x7f0c00f5
com.hhilan.flarum:style/TextAppearance.Compat.Notification.Time = 0x7f0c00f4
com.hhilan.flarum:style/TextAppearance.Compat.Notification = 0x7f0c00f1
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.Switch = 0x7f0c00ef
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0c00ee
com.hhilan.flarum:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f0c011b
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0c00ed
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0c00ec
com.hhilan.flarum:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f0c013b
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0c00ea
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0c00e9
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0c00e8
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f0c00e6
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f0c00e4
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0c00e3
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0c00de
com.hhilan.flarum:style/TextAppearance.AppCompat.Title.Inverse = 0x7f0c00dc
com.hhilan.flarum:style/TextAppearance.AppCompat.Title = 0x7f0c00db
com.hhilan.flarum:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f0c00da
com.hhilan.flarum:style/TextAppearance.AppCompat.Small = 0x7f0c00d7
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0c00eb
com.hhilan.flarum:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f0c00d6
com.hhilan.flarum:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0c00d5
com.hhilan.flarum:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f0c00d3
com.hhilan.flarum:style/TextAppearance.AppCompat.Medium = 0x7f0c00d2
com.hhilan.flarum:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0c00d0
com.hhilan.flarum:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f0c00cf
com.hhilan.flarum:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f0c00ce
com.hhilan.flarum:style/TextAppearance.AppCompat.Inverse = 0x7f0c00cb
com.hhilan.flarum:styleable/AppCompatImageView = 0x7f0d0007
com.hhilan.flarum:style/TextAppearance.AppCompat.Headline = 0x7f0c00ca
com.hhilan.flarum:style/TextAppearance.AppCompat.Display4 = 0x7f0c00c9
com.hhilan.flarum:style/TextAppearance.AppCompat.Display3 = 0x7f0c00c8
com.hhilan.flarum:style/TextAppearance.AppCompat.Display1 = 0x7f0c00c6
com.hhilan.flarum:style/TextAppearance.AppCompat.Button = 0x7f0c00c4
com.hhilan.flarum:style/TextAppearance.AppCompat.Body2 = 0x7f0c00c3
com.hhilan.flarum:style/ThemeOverlay.AppCompat = 0x7f0c010e
com.hhilan.flarum:style/TextAppearance.AppCompat = 0x7f0c00c1
com.hhilan.flarum:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f0c00c0
com.hhilan.flarum:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f0c00bf
com.hhilan.flarum:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f0c00be
com.hhilan.flarum:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f0c00bd
com.hhilan.flarum:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f0c00bc
com.hhilan.flarum:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f0c00bb
com.hhilan.flarum:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f0c00ba
com.hhilan.flarum:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f0c00b8
com.hhilan.flarum:styleable/CompoundButton = 0x7f0d000e
com.hhilan.flarum:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f0c00b7
com.hhilan.flarum:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f0c00b5
com.hhilan.flarum:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f0c00b3
com.hhilan.flarum:style/Platform.Widget.AppCompat.Spinner = 0x7f0c00b2
com.hhilan.flarum:style/Platform.V25.AppCompat = 0x7f0c00b0
com.hhilan.flarum:style/Platform.V21.AppCompat.Light = 0x7f0c00af
com.hhilan.flarum:style/Platform.V14.AppCompat.Light = 0x7f0c00ad
com.hhilan.flarum:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f0c00a9
com.hhilan.flarum:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f0c00a8
com.hhilan.flarum:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f0c0101
com.hhilan.flarum:style/Platform.ThemeOverlay.AppCompat = 0x7f0c00a7
com.hhilan.flarum:style/Platform.AppCompat.Light = 0x7f0c00a6
com.hhilan.flarum:style/Base.Widget.AppCompat.Toolbar = 0x7f0c00a3
com.hhilan.flarum:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f0c00a2
com.hhilan.flarum:style/Base.Widget.AppCompat.SeekBar = 0x7f0c009e
com.hhilan.flarum:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f0c009d
com.hhilan.flarum:style/Base.Widget.AppCompat.SearchView = 0x7f0c009c
com.hhilan.flarum:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f0c009b
com.hhilan.flarum:style/Base.Widget.AppCompat.RatingBar = 0x7f0c0099
com.hhilan.flarum:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f0c0098
com.hhilan.flarum:style/Base.Widget.AppCompat.PopupMenu = 0x7f0c0094
com.hhilan.flarum:style/Base.Widget.AppCompat.ListView.Menu = 0x7f0c0093
com.hhilan.flarum:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0c010a
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.Button = 0x7f0c00e7
com.hhilan.flarum:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f0c0092
com.hhilan.flarum:styleable/ConstraintSet = 0x7f0d0010
com.hhilan.flarum:style/Base.Widget.AppCompat.ListMenuView = 0x7f0c008f
com.hhilan.flarum:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0c008b
com.hhilan.flarum:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0c0089
com.hhilan.flarum:style/Widget.AppCompat.Light.ActionButton = 0x7f0c0138
com.hhilan.flarum:style/Theme.AppCompat.DayNight = 0x7f0c00fb
com.hhilan.flarum:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f0c0087
com.hhilan.flarum:style/Base.Widget.AppCompat.ImageButton = 0x7f0c0086
com.hhilan.flarum:style/Base.Widget.AppCompat.EditText = 0x7f0c0085
com.hhilan.flarum:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f0c0082
com.hhilan.flarum:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f0c0081
com.hhilan.flarum:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f0c0080
com.hhilan.flarum:style/Base.Widget.AppCompat.Button.Small = 0x7f0c007c
com.hhilan.flarum:style/Base.Widget.AppCompat.Button.Colored = 0x7f0c007b
com.hhilan.flarum:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0c007a
com.hhilan.flarum:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f0c0079
com.hhilan.flarum:style/Base.Widget.AppCompat.Button.Borderless = 0x7f0c0078
com.hhilan.flarum:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f0c0075
com.hhilan.flarum:style/Base.Widget.AppCompat.ActionMode = 0x7f0c0074
com.hhilan.flarum:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f0c0072
com.hhilan.flarum:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f0c0070
com.hhilan.flarum:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f0c006e
com.hhilan.flarum:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f0c006b
com.hhilan.flarum:style/Base.V7.Widget.AppCompat.EditText = 0x7f0c006a
com.hhilan.flarum:style/Widget.AppCompat.ActionBar.Solid = 0x7f0c0116
com.hhilan.flarum:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f0c0068
com.hhilan.flarum:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f0c0067
com.hhilan.flarum:style/Base.V7.Theme.AppCompat.Light = 0x7f0c0066
com.hhilan.flarum:style/Base.V7.Theme.AppCompat.Dialog = 0x7f0c0065
com.hhilan.flarum:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f0c00b9
com.hhilan.flarum:style/Base.V26.Theme.AppCompat = 0x7f0c0061
com.hhilan.flarum:style/Base.V23.Theme.AppCompat = 0x7f0c005f
com.hhilan.flarum:style/Base.V22.Theme.AppCompat.Light = 0x7f0c005e
com.hhilan.flarum:style/Base.V22.Theme.AppCompat = 0x7f0c005d
com.hhilan.flarum:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f0c005c
com.hhilan.flarum:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f0c005b
com.hhilan.flarum:style/Widget.AppCompat.Light.ActionBar = 0x7f0c012f
com.hhilan.flarum:style/Base.V21.Theme.AppCompat.Dialog = 0x7f0c0059
com.hhilan.flarum:style/Base.V21.Theme.AppCompat = 0x7f0c0058
com.hhilan.flarum:style/Widget.AppCompat.ListPopupWindow = 0x7f0c0146
com.hhilan.flarum:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0c00f6
com.hhilan.flarum:style/Base.V12.Widget.AppCompat.EditText = 0x7f0c0057
com.hhilan.flarum:style/Base.V11.Theme.AppCompat.Light.Dialog = 0x7f0c0054
com.hhilan.flarum:style/Base.V11.Theme.AppCompat.Dialog = 0x7f0c0053
com.hhilan.flarum:style/Base.ThemeOverlay.AppCompat.Light = 0x7f0c0052
com.hhilan.flarum:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0c004f
com.hhilan.flarum:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f0c004e
com.hhilan.flarum:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0c004a
com.hhilan.flarum:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f0c0049
com.hhilan.flarum:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f0c0043
com.hhilan.flarum:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f0c0042
com.hhilan.flarum:style/Theme.AppCompat.Dialog.Alert = 0x7f0c0103
com.hhilan.flarum:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f0c0041
com.hhilan.flarum:style/Base.Theme.AppCompat.CompactMenu = 0x7f0c003f
com.hhilan.flarum:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0c003d
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0c00df
com.hhilan.flarum:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0c003c
com.hhilan.flarum:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0c003b
com.hhilan.flarum:style/Base.V26.Theme.AppCompat.Light = 0x7f0c0062
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0c003a
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f0c0039
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0c0038
com.hhilan.flarum:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0c00a4
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0c0036
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0c0035
com.hhilan.flarum:style/Theme.AppCompat.CompactMenu = 0x7f0c00fa
com.hhilan.flarum:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0c008e
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0c0033
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0c0032
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0c002e
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0c002d
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0c002b
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f0c0029
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f0c0028
com.hhilan.flarum:styleable/FontFamily = 0x7f0d0012
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Title = 0x7f0c0027
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f0c0024
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f0c0022
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0c0021
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f0c0020
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Menu = 0x7f0c001f
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f0c001e
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Medium = 0x7f0c001d
com.hhilan.flarum:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f0c004d
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0c001c
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0c001b
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f0c001a
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Inverse = 0x7f0c0018
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Headline = 0x7f0c0017
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Display4 = 0x7f0c0016
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Display3 = 0x7f0c0015
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Display2 = 0x7f0c0014
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Caption = 0x7f0c0012
com.hhilan.flarum:style/Base.TextAppearance.AppCompat = 0x7f0c000e
com.hhilan.flarum:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f0c000d
com.hhilan.flarum:styleable/FontFamilyFont = 0x7f0d0013
com.hhilan.flarum:style/Base.DialogWindowTitle.AppCompat = 0x7f0c000c
com.hhilan.flarum:style/Base.Animation.AppCompat.Tooltip = 0x7f0c000b
com.hhilan.flarum:style/Base.Animation.AppCompat.Dialog = 0x7f0c0009
com.hhilan.flarum:style/AppTheme.NoTitleBar = 0x7f0c0006
com.hhilan.flarum:style/ThemeOverlay.AppCompat.Dialog = 0x7f0c0112
com.hhilan.flarum:style/Base.Theme.AppCompat.Light.Dialog = 0x7f0c0047
com.hhilan.flarum:style/AppTheme = 0x7f0c0005
com.hhilan.flarum:style/Animation.AppCompat.DropDownUp = 0x7f0c0003
com.hhilan.flarum:style/Animation.AppCompat.Dialog = 0x7f0c0002
com.hhilan.flarum:style/AlertDialog.AppCompat = 0x7f0c0000
com.hhilan.flarum:string/search_menu_title = 0x7f0b0021
com.hhilan.flarum:style/Animation.AppCompat.Tooltip = 0x7f0c0004
com.hhilan.flarum:string/notification_error_ssl_cert_invalid = 0x7f0b0020
com.hhilan.flarum:string/exit_app = 0x7f0b001f
com.hhilan.flarum:string/app_name = 0x7f0b001d
com.hhilan.flarum:string/abc_shareactionprovider_share_with_application = 0x7f0b001b
com.hhilan.flarum:string/abc_shareactionprovider_share_with = 0x7f0b001a
com.hhilan.flarum:string/abc_searchview_description_voice = 0x7f0b0019
com.hhilan.flarum:style/Widget.Compat.NotificationActionText = 0x7f0c015e
com.hhilan.flarum:string/abc_searchview_description_clear = 0x7f0b0015
com.hhilan.flarum:string/abc_search_hint = 0x7f0b0014
com.hhilan.flarum:string/abc_font_family_title_material = 0x7f0b0013
com.hhilan.flarum:style/Widget.AppCompat.ActionBar = 0x7f0c0115
com.hhilan.flarum:string/abc_font_family_menu_material = 0x7f0b0011
com.hhilan.flarum:string/abc_font_family_display_4_material = 0x7f0b000f
com.hhilan.flarum:string/abc_font_family_display_3_material = 0x7f0b000e
com.hhilan.flarum:string/abc_font_family_display_2_material = 0x7f0b000d
com.hhilan.flarum:string/abc_font_family_caption_material = 0x7f0b000b
com.hhilan.flarum:string/abc_font_family_body_2_material = 0x7f0b0009
com.hhilan.flarum:string/abc_font_family_body_1_material = 0x7f0b0008
com.hhilan.flarum:string/abc_capital_on = 0x7f0b0007
com.hhilan.flarum:id/split_action_bar = 0x7f070066
com.hhilan.flarum:string/abc_activitychooserview_choose_application = 0x7f0b0005
com.hhilan.flarum:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f0c008a
com.hhilan.flarum:string/abc_action_mode_done = 0x7f0b0003
com.hhilan.flarum:string/abc_action_menu_overflow_description = 0x7f0b0002
com.hhilan.flarum:string/abc_action_bar_up_description = 0x7f0b0001
com.hhilan.flarum:attr/titleTextColor = 0x7f020116
com.hhilan.flarum:string/abc_action_bar_home_description = 0x7f0b0000
com.hhilan.flarum:attr/backgroundSplit = 0x7f020032
com.hhilan.flarum:mipmap/ic_launcher_round = 0x7f0a0002
com.hhilan.flarum:mipmap/ic_launcher_foreground = 0x7f0a0001
com.hhilan.flarum:color/abc_search_url_text_normal = 0x7f04000d
com.hhilan.flarum:layout/tooltip = 0x7f090026
com.hhilan.flarum:layout/notification_template_part_time = 0x7f090021
com.hhilan.flarum:layout/notification_template_icon_group = 0x7f09001f
com.hhilan.flarum:layout/notification_template_custom_big = 0x7f09001e
com.hhilan.flarum:layout/abc_search_dropdown_item_icons_2line = 0x7f090017
com.hhilan.flarum:layout/abc_list_menu_item_layout = 0x7f09000f
com.hhilan.flarum:layout/abc_expanded_menu_layout = 0x7f09000c
com.hhilan.flarum:attr/paddingEnd = 0x7f0200cb
com.hhilan.flarum:id/contentPanel = 0x7f070029
com.hhilan.flarum:layout/abc_alert_dialog_material = 0x7f090009
com.hhilan.flarum:dimen/abc_dialog_list_padding_top_no_title = 0x7f050020
com.hhilan.flarum:layout/abc_action_bar_up_container = 0x7f090001
com.hhilan.flarum:layout/abc_action_bar_title_item = 0x7f090000
com.hhilan.flarum:attr/actionOverflowButtonStyle = 0x7f02001d
com.hhilan.flarum:attr/layout_goneMarginTop = 0x7f0200b3
com.hhilan.flarum:integer/status_bar_notification_info_maxnum = 0x7f080004
com.hhilan.flarum:styleable/AppCompatTheme = 0x7f0d000b
com.hhilan.flarum:style/TextAppearance.Compat.Notification.Info = 0x7f0c00f2
com.hhilan.flarum:integer/config_tooltipAnimTime = 0x7f080003
com.hhilan.flarum:attr/fontProviderFetchTimeout = 0x7f020075
com.hhilan.flarum:integer/cancel_button_image_alpha = 0x7f080002
com.hhilan.flarum:attr/windowMinWidthMinor = 0x7f020129
com.hhilan.flarum:integer/abc_config_activityShortDur = 0x7f080001
com.hhilan.flarum:id/useLogo = 0x7f07007c
com.hhilan.flarum:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f05000c
com.hhilan.flarum:id/uniform = 0x7f07007a
com.hhilan.flarum:dimen/notification_small_icon_background_padding = 0x7f050062
com.hhilan.flarum:id/topPanel = 0x7f070079
com.hhilan.flarum:id/textSpacerNoButtons = 0x7f070072
com.hhilan.flarum:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f0c0044
com.hhilan.flarum:id/tag_transition_group = 0x7f07006f
com.hhilan.flarum:attr/colorSwitchThumbNormal = 0x7f020053
com.hhilan.flarum:id/submit_area = 0x7f07006d
com.hhilan.flarum:id/src_over = 0x7f07006b
com.hhilan.flarum:anim/abc_slide_in_top = 0x7f010007
com.hhilan.flarum:id/textSpacerNoTitle = 0x7f070073
com.hhilan.flarum:id/search_bar = 0x7f070057
com.hhilan.flarum:drawable/abc_textfield_activated_mtrl_alpha = 0x7f06004e
com.hhilan.flarum:id/src_in = 0x7f07006a
com.hhilan.flarum:attr/alertDialogTheme = 0x7f020025
com.hhilan.flarum:id/up = 0x7f07007b
com.hhilan.flarum:attr/alpha = 0x7f020027
com.hhilan.flarum:id/showHome = 0x7f070063
com.hhilan.flarum:id/showCustom = 0x7f070062
com.hhilan.flarum:id/shortcut = 0x7f070061
com.hhilan.flarum:id/search_src_text = 0x7f07005e
com.hhilan.flarum:string/abc_font_family_subhead_material = 0x7f0b0012
com.hhilan.flarum:attr/popupMenuStyle = 0x7f0200d1
com.hhilan.flarum:layout/abc_action_menu_layout = 0x7f090003
com.hhilan.flarum:id/search_plate = 0x7f07005d
com.hhilan.flarum:drawable/abc_list_selector_disabled_holo_light = 0x7f060031
com.hhilan.flarum:id/search_go_btn = 0x7f07005b
com.hhilan.flarum:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f0c0122
com.hhilan.flarum:id/customPanel = 0x7f07002b
com.hhilan.flarum:id/search_edit_frame = 0x7f07005a
com.hhilan.flarum:id/search_close_btn = 0x7f070059
com.hhilan.flarum:string/error_message = 0x7f0b001e
com.hhilan.flarum:id/scrollIndicatorUp = 0x7f070054
com.hhilan.flarum:id/screen = 0x7f070052
com.hhilan.flarum:id/progress_circular = 0x7f07004d
com.hhilan.flarum:id/progressBar1 = 0x7f07004c
com.hhilan.flarum:id/search_voice_btn = 0x7f07005f
com.hhilan.flarum:id/notification_main_column = 0x7f070047
com.hhilan.flarum:style/Platform.V25.AppCompat.Light = 0x7f0c00b1
com.hhilan.flarum:attr/spinBars = 0x7f0200e8
com.hhilan.flarum:drawable/ic_launcher_foreground = 0x7f060055
com.hhilan.flarum:id/none = 0x7f070044
com.hhilan.flarum:id/never = 0x7f070043
com.hhilan.flarum:id/middle = 0x7f070041
com.hhilan.flarum:id/list_item = 0x7f07003f
com.hhilan.flarum:id/listMode = 0x7f07003e
com.hhilan.flarum:style/Widget.AppCompat.SeekBar.Discrete = 0x7f0c0155
com.hhilan.flarum:id/line3 = 0x7f07003d
com.hhilan.flarum:id/line1 = 0x7f07003c
com.hhilan.flarum:id/ifRoom = 0x7f070038
com.hhilan.flarum:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f0c0130
com.hhilan.flarum:id/icon = 0x7f070036
com.hhilan.flarum:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f0c0090
com.hhilan.flarum:layout/abc_action_mode_bar = 0x7f090004
com.hhilan.flarum:style/TextAppearance.AppCompat.Menu = 0x7f0c00d4
com.hhilan.flarum:id/homeAsUp = 0x7f070035
com.hhilan.flarum:style/Base.V21.Theme.AppCompat.Light = 0x7f0c005a
com.hhilan.flarum:id/scrollView = 0x7f070055
com.hhilan.flarum:dimen/abc_text_size_display_4_material = 0x7f05003f
com.hhilan.flarum:id/home = 0x7f070034
com.hhilan.flarum:id/forever = 0x7f070033
com.hhilan.flarum:id/expand_activities_button = 0x7f070031
com.hhilan.flarum:id/titleDividerNoCustom = 0x7f070076
com.hhilan.flarum:drawable/notification_bg_low = 0x7f060058
com.hhilan.flarum:id/edit_query = 0x7f07002f
com.hhilan.flarum:attr/listPopupWindowStyle = 0x7f0200ba
com.hhilan.flarum:id/disableHome = 0x7f07002e
com.hhilan.flarum:attr/checkboxStyle = 0x7f020043
com.hhilan.flarum:layout/abc_list_menu_item_radio = 0x7f090010
com.hhilan.flarum:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f0c006f
com.hhilan.flarum:layout/abc_activity_chooser_view_list_item = 0x7f090007
com.hhilan.flarum:id/showTitle = 0x7f070064
com.hhilan.flarum:id/decor_content_parent = 0x7f07002c
com.hhilan.flarum:id/collapseActionView = 0x7f070028
com.hhilan.flarum:attr/layout_constraintWidth_min = 0x7f0200ab
com.hhilan.flarum:id/text = 0x7f070070
com.hhilan.flarum:id/checkbox = 0x7f070026
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f0c0031
com.hhilan.flarum:id/buttonPanel = 0x7f070024
com.hhilan.flarum:styleable/SwitchCompat = 0x7f0d0020
com.hhilan.flarum:id/button1 = 0x7f070023
com.hhilan.flarum:style/Widget.AppCompat.PopupWindow = 0x7f0c014c
com.hhilan.flarum:style/Widget.AppCompat.ActionBar.TabView = 0x7f0c0119
com.hhilan.flarum:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f0c0084
com.hhilan.flarum:color/bright_foreground_disabled_material_light = 0x7f04001f
com.hhilan.flarum:id/bottom = 0x7f070021
com.hhilan.flarum:color/ripple_material_dark = 0x7f040049
com.hhilan.flarum:id/basic = 0x7f07001e
com.hhilan.flarum:id/async = 0x7f07001d
com.hhilan.flarum:drawable/abc_ic_menu_overflow_material = 0x7f06001b
com.hhilan.flarum:id/actions = 0x7f070017
com.hhilan.flarum:id/action_mode_bar_stub = 0x7f070014
com.hhilan.flarum:id/action_mode_bar = 0x7f070013
com.hhilan.flarum:id/time = 0x7f070074
com.hhilan.flarum:dimen/hint_pressed_alpha_material_dark = 0x7f050056
com.hhilan.flarum:id/action_menu_presenter = 0x7f070012
com.hhilan.flarum:id/action_menu_divider = 0x7f070011
com.hhilan.flarum:color/secondary_text_disabled_material_dark = 0x7f04004d
com.hhilan.flarum:id/notification_background = 0x7f070046
com.hhilan.flarum:attr/actionOverflowMenuStyle = 0x7f02001e
com.hhilan.flarum:id/action_image = 0x7f070010
com.hhilan.flarum:drawable/abc_list_selector_holo_dark = 0x7f060032
com.hhilan.flarum:id/action_context_bar = 0x7f07000e
com.hhilan.flarum:layout/abc_list_menu_item_icon = 0x7f09000e
com.hhilan.flarum:style/Base.Widget.AppCompat.Button = 0x7f0c0077
com.hhilan.flarum:attr/dividerVertical = 0x7f020066
com.hhilan.flarum:id/action_bar_activity_content = 0x7f070007
com.hhilan.flarum:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f0c0134
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Large = 0x7f0c0019
com.hhilan.flarum:id/META = 0x7f070003
com.hhilan.flarum:id/FUNCTION = 0x7f070002
com.hhilan.flarum:drawable/tooltip_frame_dark = 0x7f060062
com.hhilan.flarum:anim/abc_fade_in = 0x7f010000
com.hhilan.flarum:id/message = 0x7f070040
com.hhilan.flarum:drawable/notification_template_icon_low_bg = 0x7f06005f
com.hhilan.flarum:id/ALT = 0x7f070000
com.hhilan.flarum:drawable/notification_template_icon_bg = 0x7f06005e
com.hhilan.flarum:drawable/notification_bg_normal_pressed = 0x7f06005c
com.hhilan.flarum:drawable/notification_bg_normal = 0x7f06005b
com.hhilan.flarum:string/abc_toolbar_collapse_description = 0x7f0b001c
com.hhilan.flarum:id/action_bar_title = 0x7f07000c
com.hhilan.flarum:drawable/notification_action_background = 0x7f060056
com.hhilan.flarum:drawable/ic_launcher_background = 0x7f060054
com.hhilan.flarum:color/foreground_material_dark = 0x7f04002e
com.hhilan.flarum:drawable/abc_textfield_search_material = 0x7f060052
com.hhilan.flarum:anim/abc_slide_out_bottom = 0x7f010008
com.hhilan.flarum:drawable/abc_text_select_handle_right_mtrl_dark = 0x7f06004c
com.hhilan.flarum:drawable/abc_text_select_handle_middle_mtrl_dark = 0x7f06004a
com.hhilan.flarum:attr/imageButtonStyle = 0x7f020084
com.hhilan.flarum:attr/layout_constraintDimensionRatio = 0x7f02008f
com.hhilan.flarum:drawable/abc_text_select_handle_left_mtrl_light = 0x7f060049
com.hhilan.flarum:bool/abc_config_actionMenuItemAllCaps = 0x7f030002
com.hhilan.flarum:layout/abc_select_dialog_material = 0x7f090019
com.hhilan.flarum:layout/error = 0x7f09001b
com.hhilan.flarum:drawable/abc_tab_indicator_mtrl_alpha = 0x7f060046
com.hhilan.flarum:attr/drawableSize = 0x7f020067
com.hhilan.flarum:id/text2 = 0x7f070071
com.hhilan.flarum:id/normal = 0x7f070045
com.hhilan.flarum:attr/actionBarPopupTheme = 0x7f020002
com.hhilan.flarum:drawable/abc_tab_indicator_material = 0x7f060045
com.hhilan.flarum:dimen/abc_button_inset_vertical_material = 0x7f050013
com.hhilan.flarum:drawable/abc_switch_track_mtrl_alpha = 0x7f060044
com.hhilan.flarum:string/abc_searchview_description_query = 0x7f0b0016
com.hhilan.flarum:drawable/abc_spinner_textfield_background_material = 0x7f060042
com.hhilan.flarum:attr/font = 0x7f020070
com.hhilan.flarum:drawable/abc_seekbar_track_material = 0x7f060040
com.hhilan.flarum:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f0c0139
com.hhilan.flarum:drawable/abc_seekbar_tick_mark_material = 0x7f06003f
com.hhilan.flarum:id/submenuarrow = 0x7f07006c
com.hhilan.flarum:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f06003a
com.hhilan.flarum:layout/abc_screen_simple_overlay_action_mode = 0x7f090015
com.hhilan.flarum:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f060039
com.hhilan.flarum:layout/select_dialog_item_material = 0x7f090022
com.hhilan.flarum:id/action_mode_close_button = 0x7f070015
com.hhilan.flarum:dimen/abc_text_size_medium_material = 0x7f050042
com.hhilan.flarum:drawable/abc_ratingbar_material = 0x7f060037
com.hhilan.flarum:attr/fontStyle = 0x7f020078
com.hhilan.flarum:attr/goIcon = 0x7f02007b
com.hhilan.flarum:attr/arrowHeadLength = 0x7f020029
com.hhilan.flarum:drawable/abc_ratingbar_indicator_material = 0x7f060036
com.hhilan.flarum:color/primary_dark_material_light = 0x7f040042
com.hhilan.flarum:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f060034
com.hhilan.flarum:attr/alertDialogCenterButtons = 0x7f020023
com.hhilan.flarum:drawable/abc_list_selector_holo_light = 0x7f060033
com.hhilan.flarum:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f060051
com.hhilan.flarum:drawable/abc_list_selector_background_transition_holo_dark = 0x7f06002e
com.hhilan.flarum:drawable/abc_list_pressed_holo_light = 0x7f06002d
com.hhilan.flarum:id/notification_main_column_container = 0x7f070048
com.hhilan.flarum:drawable/abc_list_longpressed_holo = 0x7f06002b
com.hhilan.flarum:drawable/abc_scrubber_track_mtrl_alpha = 0x7f06003d
com.hhilan.flarum:drawable/abc_list_divider_mtrl_alpha = 0x7f060029
com.hhilan.flarum:drawable/abc_item_background_holo_light = 0x7f060028
com.hhilan.flarum:drawable/abc_ic_voice_search_api_material = 0x7f060026
com.hhilan.flarum:attr/displayOptions = 0x7f020062
com.hhilan.flarum:drawable/abc_ic_star_half_black_48dp = 0x7f060025
com.hhilan.flarum:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f0c0137
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0c00e5
com.hhilan.flarum:drawable/abc_ic_star_half_black_36dp = 0x7f060024
com.hhilan.flarum:id/multiply = 0x7f070042
com.hhilan.flarum:drawable/abc_ic_star_black_48dp = 0x7f060022
com.hhilan.flarum:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f06001d
com.hhilan.flarum:id/progress_horizontal = 0x7f07004e
com.hhilan.flarum:layout/abc_screen_content_include = 0x7f090013
com.hhilan.flarum:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f060019
com.hhilan.flarum:drawable/abc_ic_go_search_api_material = 0x7f060018
com.hhilan.flarum:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f060017
com.hhilan.flarum:drawable/abc_ic_clear_material = 0x7f060016
com.hhilan.flarum:drawable/abc_dialog_material_background = 0x7f060012
com.hhilan.flarum:drawable/abc_control_background_material = 0x7f060011
com.hhilan.flarum:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f0c0073
com.hhilan.flarum:drawable/abc_cab_background_top_material = 0x7f06000f
com.hhilan.flarum:id/info = 0x7f07003a
com.hhilan.flarum:drawable/abc_cab_background_internal_bg = 0x7f06000e
com.hhilan.flarum:attr/alphabeticModifiers = 0x7f020028
com.hhilan.flarum:attr/textAppearanceSmallPopupMenu = 0x7f020101
com.hhilan.flarum:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f06000d
com.hhilan.flarum:color/primary_dark_material_dark = 0x7f040041
com.hhilan.flarum:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f06001c
com.hhilan.flarum:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f06000c
com.hhilan.flarum:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f06000a
com.hhilan.flarum:drawable/abc_btn_default_mtrl_shape = 0x7f060008
com.hhilan.flarum:layout/abc_popup_menu_header_item_layout = 0x7f090011
com.hhilan.flarum:drawable/abc_btn_colored_material = 0x7f060007
com.hhilan.flarum:drawable/abc_btn_check_material = 0x7f060004
com.hhilan.flarum:attr/preserveIconSpacing = 0x7f0200d4
com.hhilan.flarum:drawable/abc_action_bar_item_background_material = 0x7f060002
com.hhilan.flarum:dimen/tooltip_y_offset_touch = 0x7f05006e
com.hhilan.flarum:dimen/tooltip_precise_anchor_threshold = 0x7f05006b
com.hhilan.flarum:dimen/tooltip_margin = 0x7f050069
com.hhilan.flarum:dimen/notification_action_text_size = 0x7f050059
com.hhilan.flarum:dimen/tooltip_horizontal_padding = 0x7f050068
com.hhilan.flarum:attr/contentInsetEnd = 0x7f020057
com.hhilan.flarum:layout/abc_dialog_title_material = 0x7f09000b
com.hhilan.flarum:dimen/tooltip_corner_radius = 0x7f050067
com.hhilan.flarum:id/alertTitle = 0x7f07001a
com.hhilan.flarum:dimen/notification_small_icon_size_as_large = 0x7f050063
com.hhilan.flarum:dimen/notification_right_icon_size = 0x7f050060
com.hhilan.flarum:dimen/notification_main_column_padding_top = 0x7f05005e
com.hhilan.flarum:dimen/notification_large_icon_width = 0x7f05005d
com.hhilan.flarum:dimen/notification_action_icon_size = 0x7f050058
com.hhilan.flarum:dimen/hint_alpha_material_light = 0x7f050055
com.hhilan.flarum:id/packed = 0x7f070049
com.hhilan.flarum:attr/actionModeWebSearchDrawable = 0x7f02001c
com.hhilan.flarum:id/image = 0x7f070039
com.hhilan.flarum:color/primary_material_light = 0x7f040044
com.hhilan.flarum:dimen/highlight_alpha_material_colored = 0x7f050051
com.hhilan.flarum:dimen/disabled_alpha_material_light = 0x7f050050
com.hhilan.flarum:color/primary_text_default_material_light = 0x7f040046
com.hhilan.flarum:dimen/disabled_alpha_material_dark = 0x7f05004f
com.hhilan.flarum:drawable/abc_list_pressed_holo_dark = 0x7f06002c
com.hhilan.flarum:style/TextAppearance.AppCompat.Caption = 0x7f0c00c5
com.hhilan.flarum:id/action_container = 0x7f07000d
com.hhilan.flarum:dimen/compat_button_padding_vertical_material = 0x7f05004d
com.hhilan.flarum:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f060050
com.hhilan.flarum:dimen/compat_button_padding_horizontal_material = 0x7f05004c
com.hhilan.flarum:attr/layout_constraintVertical_bias = 0x7f0200a6
com.hhilan.flarum:dimen/compat_button_inset_vertical_material = 0x7f05004b
com.hhilan.flarum:id/expanded_menu = 0x7f070032
com.hhilan.flarum:dimen/compat_button_inset_horizontal_material = 0x7f05004a
com.hhilan.flarum:dimen/abc_text_size_small_material = 0x7f050045
com.hhilan.flarum:dimen/abc_text_size_large_material = 0x7f050041
com.hhilan.flarum:dimen/abc_text_size_headline_material = 0x7f050040
com.hhilan.flarum:drawable/abc_ic_star_black_36dp = 0x7f060021
com.hhilan.flarum:dimen/abc_text_size_display_1_material = 0x7f05003c
com.hhilan.flarum:attr/trackTint = 0x7f02011e
com.hhilan.flarum:id/wrap_content = 0x7f070080
com.hhilan.flarum:style/Base.V7.Theme.AppCompat = 0x7f0c0064
com.hhilan.flarum:dimen/notification_right_side_padding_top = 0x7f050061
com.hhilan.flarum:color/switch_thumb_normal_material_light = 0x7f040054
com.hhilan.flarum:dimen/abc_text_size_caption_material = 0x7f05003b
com.hhilan.flarum:id/spread = 0x7f070067
com.hhilan.flarum:id/action_divider = 0x7f07000f
com.hhilan.flarum:style/ThemeOverlay.AppCompat.Light = 0x7f0c0114
com.hhilan.flarum:dimen/abc_text_size_button_material = 0x7f05003a
com.hhilan.flarum:dimen/abc_text_size_body_2_material = 0x7f050039
com.hhilan.flarum:dimen/abc_text_size_body_1_material = 0x7f050038
com.hhilan.flarum:dimen/abc_switch_padding = 0x7f050037
com.hhilan.flarum:style/Platform.V11.AppCompat.Light = 0x7f0c00ab
com.hhilan.flarum:id/action_bar_container = 0x7f070008
com.hhilan.flarum:dimen/abc_select_dialog_padding_start_material = 0x7f050036
com.hhilan.flarum:dimen/abc_seekbar_track_progress_height_material = 0x7f050035
com.hhilan.flarum:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0c00d1
com.hhilan.flarum:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f0c004b
com.hhilan.flarum:attr/layout_constraintGuide_end = 0x7f020093
com.hhilan.flarum:dimen/abc_search_view_preferred_width = 0x7f050033
com.hhilan.flarum:dimen/abc_search_view_preferred_height = 0x7f050032
com.hhilan.flarum:attr/titleMargin = 0x7f02010f
com.hhilan.flarum:dimen/abc_edit_text_inset_horizontal_material = 0x7f05002c
com.hhilan.flarum:style/Widget.AppCompat.Spinner.Underlined = 0x7f0c0159
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0c002c
com.hhilan.flarum:attr/hideOnContentScroll = 0x7f02007d
com.hhilan.flarum:dimen/abc_dropdownitem_text_padding_right = 0x7f05002a
com.hhilan.flarum:dimen/compat_control_corner_material = 0x7f05004e
com.hhilan.flarum:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f06001a
com.hhilan.flarum:dimen/abc_dropdownitem_icon_width = 0x7f050028
com.hhilan.flarum:drawable/abc_cab_background_top_mtrl_alpha = 0x7f060010
com.hhilan.flarum:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f0c007f
com.hhilan.flarum:dimen/abc_dialog_padding_top_material = 0x7f050024
com.hhilan.flarum:drawable/abc_list_selector_disabled_holo_dark = 0x7f060030
com.hhilan.flarum:id/tabMode = 0x7f07006e
com.hhilan.flarum:dimen/abc_dialog_fixed_width_major = 0x7f05001d
com.hhilan.flarum:style/Base.V11.ThemeOverlay.AppCompat.Dialog = 0x7f0c0055
com.hhilan.flarum:attr/theme = 0x7f020104
com.hhilan.flarum:dimen/abc_dialog_fixed_height_major = 0x7f05001b
com.hhilan.flarum:dimen/abc_control_padding_material = 0x7f05001a
com.hhilan.flarum:attr/showDividers = 0x7f0200e4
com.hhilan.flarum:dimen/abc_control_inset_material = 0x7f050019
com.hhilan.flarum:drawable/abc_spinner_mtrl_am_alpha = 0x7f060041
com.hhilan.flarum:dimen/abc_control_corner_material = 0x7f050018
com.hhilan.flarum:layout/abc_alert_dialog_button_bar_material = 0x7f090008
com.hhilan.flarum:dimen/abc_config_prefDialogWidth = 0x7f050017
com.hhilan.flarum:attr/textAppearanceListItemSmall = 0x7f0200fd
com.hhilan.flarum:dimen/abc_cascading_menus_min_smallest_width = 0x7f050016
com.hhilan.flarum:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f0c006d
com.hhilan.flarum:dimen/abc_button_padding_vertical_material = 0x7f050015
com.hhilan.flarum:id/action_bar = 0x7f070006
com.hhilan.flarum:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f0c0131
com.hhilan.flarum:style/Base.Animation.AppCompat.DropDownUp = 0x7f0c000a
com.hhilan.flarum:dimen/abc_button_padding_horizontal_material = 0x7f050014
com.hhilan.flarum:style/Widget.AppCompat.Spinner.DropDown = 0x7f0c0157
com.hhilan.flarum:attr/thumbTint = 0x7f020107
com.hhilan.flarum:layout/select_dialog_multichoice_material = 0x7f090023
com.hhilan.flarum:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f0c0088
com.hhilan.flarum:dimen/abc_button_inset_horizontal_material = 0x7f050012
com.hhilan.flarum:attr/tintMode = 0x7f02010d
com.hhilan.flarum:id/chains = 0x7f070025
com.hhilan.flarum:dimen/highlight_alpha_material_light = 0x7f050053
com.hhilan.flarum:dimen/abc_action_button_min_width_overflow_material = 0x7f050010
com.hhilan.flarum:dimen/abc_action_button_min_width_material = 0x7f05000f
com.hhilan.flarum:dimen/abc_action_button_min_height_material = 0x7f05000e
com.hhilan.flarum:style/Widget.AppCompat.ActivityChooserView = 0x7f0c011e
com.hhilan.flarum:color/ic_launcher_background = 0x7f040032
com.hhilan.flarum:layout/abc_screen_toolbar = 0x7f090016
com.hhilan.flarum:color/material_grey_900 = 0x7f04003e
com.hhilan.flarum:drawable/abc_switch_thumb_material = 0x7f060043
com.hhilan.flarum:dimen/abc_action_bar_stacked_tab_max_width = 0x7f05000b
com.hhilan.flarum:drawable/abc_edit_text_material = 0x7f060013
com.hhilan.flarum:attr/background = 0x7f020031
com.hhilan.flarum:dimen/notification_media_narrow_margin = 0x7f05005f
com.hhilan.flarum:dimen/abc_action_bar_stacked_max_height = 0x7f05000a
com.hhilan.flarum:attr/searchIcon = 0x7f0200de
com.hhilan.flarum:dimen/abc_action_bar_progress_bar_size = 0x7f050009
com.hhilan.flarum:dimen/abc_action_bar_overflow_padding_start_material = 0x7f050008
com.hhilan.flarum:drawable/abc_text_select_handle_left_mtrl_dark = 0x7f060048
com.hhilan.flarum:dimen/abc_action_bar_overflow_padding_end_material = 0x7f050007
com.hhilan.flarum:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f050006
com.hhilan.flarum:integer/abc_config_activityDefaultDur = 0x7f080000
com.hhilan.flarum:dimen/abc_action_bar_default_padding_end_material = 0x7f050003
com.hhilan.flarum:id/search_badge = 0x7f070056
com.hhilan.flarum:dimen/abc_action_bar_default_height_material = 0x7f050002
com.hhilan.flarum:dimen/abc_action_bar_content_inset_with_nav = 0x7f050001
com.hhilan.flarum:dimen/abc_action_bar_content_inset_material = 0x7f050000
com.hhilan.flarum:dimen/abc_action_bar_default_padding_start_material = 0x7f050004
com.hhilan.flarum:color/tooltip_background_light = 0x7f040056
com.hhilan.flarum:attr/ratingBarStyle = 0x7f0200da
com.hhilan.flarum:drawable/$ic_launcher_foreground__0 = 0x7f060000
com.hhilan.flarum:style/Base.AlertDialog.AppCompat = 0x7f0c0007
com.hhilan.flarum:color/switch_thumb_material_light = 0x7f040052
com.hhilan.flarum:color/secondary_text_disabled_material_light = 0x7f04004e
com.hhilan.flarum:layout/support_simple_spinner_dropdown_item = 0x7f090025
com.hhilan.flarum:color/secondary_text_default_material_light = 0x7f04004c
com.hhilan.flarum:color/secondary_text_default_material_dark = 0x7f04004b
com.hhilan.flarum:id/blocking = 0x7f070020
com.hhilan.flarum:string/status_bar_notification_info_overflow = 0x7f0b0022
com.hhilan.flarum:id/SYM = 0x7f070005
com.hhilan.flarum:id/search_button = 0x7f070058
com.hhilan.flarum:dimen/abc_text_size_menu_header_material = 0x7f050043
com.hhilan.flarum:color/error_color_material = 0x7f04002d
com.hhilan.flarum:color/primary_text_disabled_material_light = 0x7f040048
com.hhilan.flarum:attr/expandActivityOverflowButtonDrawable = 0x7f02006f
com.hhilan.flarum:id/title = 0x7f070075
com.hhilan.flarum:style/Theme.AppCompat.DialogWhenLarge = 0x7f0c0105
com.hhilan.flarum:drawable/abc_list_focused_holo = 0x7f06002a
com.hhilan.flarum:color/primary_text_default_material_dark = 0x7f040045
com.hhilan.flarum:string/abc_capital_off = 0x7f0b0006
com.hhilan.flarum:attr/dividerPadding = 0x7f020065
com.hhilan.flarum:attr/layout_constraintVertical_weight = 0x7f0200a8
com.hhilan.flarum:attr/backgroundStacked = 0x7f020033
com.hhilan.flarum:color/notification_action_color_filter = 0x7f04003f
com.hhilan.flarum:attr/listMenuViewStyle = 0x7f0200b9
com.hhilan.flarum:attr/autoSizeMinTextSize = 0x7f02002d
com.hhilan.flarum:id/title_template = 0x7f070077
com.hhilan.flarum:color/material_grey_800 = 0x7f04003c
com.hhilan.flarum:id/activity_chooser_view_content = 0x7f070018
com.hhilan.flarum:layout/abc_action_menu_item_layout = 0x7f090002
com.hhilan.flarum:color/material_grey_100 = 0x7f040038
com.hhilan.flarum:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0c0127
com.hhilan.flarum:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f05000d
com.hhilan.flarum:color/material_deep_teal_500 = 0x7f040037
com.hhilan.flarum:color/material_deep_teal_200 = 0x7f040036
com.hhilan.flarum:color/material_blue_grey_950 = 0x7f040035
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Button = 0x7f0c0011
com.hhilan.flarum:attr/actionBarTabBarStyle = 0x7f020006
com.hhilan.flarum:color/highlighted_text_material_light = 0x7f040031
com.hhilan.flarum:attr/colorControlHighlight = 0x7f02004e
com.hhilan.flarum:drawable/abc_popup_background_mtrl_mult = 0x7f060035
com.hhilan.flarum:color/foreground_material_light = 0x7f04002f
com.hhilan.flarum:color/dim_foreground_material_light = 0x7f04002c
com.hhilan.flarum:style/TextAppearance.Compat.Notification.Line2 = 0x7f0c00f3
com.hhilan.flarum:color/dim_foreground_disabled_material_light = 0x7f04002a
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0c00e0
com.hhilan.flarum:attr/dropdownListPreferredItemHeight = 0x7f02006a
com.hhilan.flarum:attr/layout_constraintWidth_max = 0x7f0200aa
com.hhilan.flarum:color/dim_foreground_disabled_material_dark = 0x7f040029
com.hhilan.flarum:color/colorPrimaryDark = 0x7f040028
com.hhilan.flarum:color/button_material_light = 0x7f040025
com.hhilan.flarum:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f06000b
com.hhilan.flarum:attr/tickMarkTintMode = 0x7f02010b
com.hhilan.flarum:color/bright_foreground_inverse_material_dark = 0x7f040020
com.hhilan.flarum:attr/contentInsetEndWithActions = 0x7f020058
com.hhilan.flarum:color/background_floating_material_light = 0x7f04001b
com.hhilan.flarum:color/accent_material_light = 0x7f040019
com.hhilan.flarum:color/button_material_dark = 0x7f040024
com.hhilan.flarum:color/abc_tint_switch_track = 0x7f040017
com.hhilan.flarum:dimen/tooltip_y_offset_non_touch = 0x7f05006d
com.hhilan.flarum:id/radio = 0x7f07004f
com.hhilan.flarum:attr/iconifiedByDefault = 0x7f020083
com.hhilan.flarum:color/abc_tint_spinner = 0x7f040016
com.hhilan.flarum:color/abc_tint_default = 0x7f040013
com.hhilan.flarum:color/abc_tint_btn_checkable = 0x7f040012
com.hhilan.flarum:style/Base.AlertDialog.AppCompat.Light = 0x7f0c0008
com.hhilan.flarum:drawable/abc_list_selector_background_transition_holo_light = 0x7f06002f
com.hhilan.flarum:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f060006
com.hhilan.flarum:color/abc_background_cache_hint_selector_material_dark = 0x7f040000
com.hhilan.flarum:color/abc_search_url_text = 0x7f04000c
com.hhilan.flarum:attr/titleTextStyle = 0x7f020117
com.hhilan.flarum:color/abc_primary_text_material_dark = 0x7f04000a
com.hhilan.flarum:dimen/notification_subtext_size = 0x7f050064
com.hhilan.flarum:style/Base.V23.Theme.AppCompat.Light = 0x7f0c0060
com.hhilan.flarum:attr/windowActionBar = 0x7f020121
com.hhilan.flarum:color/abc_primary_text_disable_only_material_light = 0x7f040009
com.hhilan.flarum:color/abc_primary_text_disable_only_material_dark = 0x7f040008
com.hhilan.flarum:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0c015c
com.hhilan.flarum:style/TextAppearance.AppCompat.Large = 0x7f0c00cc
com.hhilan.flarum:attr/voiceIcon = 0x7f020120
com.hhilan.flarum:attr/layout_constraintLeft_creator = 0x7f02009b
com.hhilan.flarum:color/abc_input_method_navigation_guard = 0x7f040007
com.hhilan.flarum:color/abc_hint_foreground_material_light = 0x7f040006
com.hhilan.flarum:dimen/notification_top_pad = 0x7f050065
com.hhilan.flarum:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f0c008c
com.hhilan.flarum:attr/buttonBarButtonStyle = 0x7f020038
com.hhilan.flarum:attr/layout_constraintLeft_toLeftOf = 0x7f02009c
com.hhilan.flarum:color/abc_hint_foreground_material_dark = 0x7f040005
com.hhilan.flarum:color/abc_color_highlight_material = 0x7f040004
com.hhilan.flarum:color/abc_btn_colored_borderless_text_material = 0x7f040002
com.hhilan.flarum:layout/activity_main = 0x7f09001a
com.hhilan.flarum:bool/abc_config_showMenuShortcutsWhenKeyboardPresent = 0x7f030004
com.hhilan.flarum:attr/textColorAlertDialogListItem = 0x7f020102
com.hhilan.flarum:attr/tickMarkTint = 0x7f02010a
com.hhilan.flarum:color/abc_background_cache_hint_selector_material_light = 0x7f040001
com.hhilan.flarum:style/Platform.V21.AppCompat = 0x7f0c00ae
com.hhilan.flarum:bool/abc_allow_stacked_button_bar = 0x7f030001
com.hhilan.flarum:attr/windowFixedWidthMinor = 0x7f020127
com.hhilan.flarum:color/dim_foreground_material_dark = 0x7f04002b
com.hhilan.flarum:attr/layout_constraintBottom_toBottomOf = 0x7f02008d
com.hhilan.flarum:attr/actionDropDownStyle = 0x7f02000c
com.hhilan.flarum:attr/windowFixedWidthMajor = 0x7f020126
com.hhilan.flarum:drawable/notification_bg = 0x7f060057
com.hhilan.flarum:attr/colorButtonNormal = 0x7f02004c
com.hhilan.flarum:id/beginning = 0x7f07001f
com.hhilan.flarum:attr/multiChoiceItemLayout = 0x7f0200c4
com.hhilan.flarum:attr/windowFixedHeightMinor = 0x7f020125
com.hhilan.flarum:style/Widget.AppCompat.Button = 0x7f0c0120
com.hhilan.flarum:drawable/notification_bg_low_pressed = 0x7f06005a
com.hhilan.flarum:attr/windowFixedHeightMajor = 0x7f020124
com.hhilan.flarum:style/Base.Widget.AppCompat.ListView = 0x7f0c0091
com.hhilan.flarum:attr/windowActionModeOverlay = 0x7f020123
com.hhilan.flarum:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f0c0133
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f0c0026
com.hhilan.flarum:drawable/notification_icon_background = 0x7f06005d
com.hhilan.flarum:id/search_mag_icon = 0x7f07005c
com.hhilan.flarum:dimen/notification_top_pad_large_text = 0x7f050066
com.hhilan.flarum:attr/windowActionBarOverlay = 0x7f020122
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0c0037
com.hhilan.flarum:attr/actionBarSize = 0x7f020003
com.hhilan.flarum:drawable/abc_ic_star_black_16dp = 0x7f060020
com.hhilan.flarum:style/Widget.AppCompat.Button.Colored = 0x7f0c0124
com.hhilan.flarum:color/abc_search_url_text_selected = 0x7f04000f
com.hhilan.flarum:attr/trackTintMode = 0x7f02011f
com.hhilan.flarum:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f06003c
com.hhilan.flarum:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f06003b
com.hhilan.flarum:attr/paddingTopNoTitle = 0x7f0200cd
com.hhilan.flarum:attr/track = 0x7f02011d
com.hhilan.flarum:attr/tooltipText = 0x7f02011c
com.hhilan.flarum:dimen/abc_alert_dialog_button_bar_height = 0x7f050011
com.hhilan.flarum:attr/actionViewClass = 0x7f020020
com.hhilan.flarum:id/withText = 0x7f07007e
com.hhilan.flarum:bool/abc_action_bar_embed_tabs = 0x7f030000
com.hhilan.flarum:attr/iconTintMode = 0x7f020082
com.hhilan.flarum:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0c007e
com.hhilan.flarum:attr/titleTextAppearance = 0x7f020115
com.hhilan.flarum:attr/autoSizeTextType = 0x7f020030
com.hhilan.flarum:attr/ratingBarStyleSmall = 0x7f0200dc
com.hhilan.flarum:color/primary_text_disabled_material_dark = 0x7f040047
com.hhilan.flarum:dimen/abc_dialog_min_width_major = 0x7f050021
com.hhilan.flarum:attr/icon = 0x7f020080
com.hhilan.flarum:attr/contentInsetStart = 0x7f02005b
com.hhilan.flarum:attr/thumbTextPadding = 0x7f020106
com.hhilan.flarum:attr/titleMarginTop = 0x7f020113
com.hhilan.flarum:attr/listItemLayout = 0x7f0200b7
com.hhilan.flarum:id/icon_group = 0x7f070037
com.hhilan.flarum:attr/titleMarginStart = 0x7f020112
com.hhilan.flarum:bool/abc_config_closeDialogWhenTouchOutside = 0x7f030003
com.hhilan.flarum:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0c0123
com.hhilan.flarum:attr/autoSizePresetSizes = 0x7f02002e
com.hhilan.flarum:attr/titleMarginEnd = 0x7f020111
com.hhilan.flarum:attr/actionModeSplitBackground = 0x7f02001a
com.hhilan.flarum:attr/title = 0x7f02010e
com.hhilan.flarum:attr/tickMark = 0x7f020109
com.hhilan.flarum:color/abc_secondary_text_material_dark = 0x7f040010
com.hhilan.flarum:attr/navigationIcon = 0x7f0200c6
com.hhilan.flarum:attr/singleChoiceItemLayout = 0x7f0200e7
com.hhilan.flarum:attr/thumbTintMode = 0x7f020108
com.hhilan.flarum:dimen/abc_list_item_padding_horizontal_material = 0x7f05002f
com.hhilan.flarum:drawable/abc_seekbar_thumb_material = 0x7f06003e
com.hhilan.flarum:style/Widget.AppCompat.ListView.Menu = 0x7f0c0149
com.hhilan.flarum:style/Base.Widget.AppCompat.Spinner = 0x7f0c00a0
com.hhilan.flarum:attr/thickness = 0x7f020105
com.hhilan.flarum:anim/abc_slide_out_top = 0x7f010009
com.hhilan.flarum:style/Base.Widget.AppCompat.ActionBar = 0x7f0c006c
com.hhilan.flarum:attr/activityChooserViewStyle = 0x7f020021
com.hhilan.flarum:attr/textColorSearchUrl = 0x7f020103
com.hhilan.flarum:drawable/abc_text_select_handle_right_mtrl_light = 0x7f06004d
com.hhilan.flarum:attr/fontProviderPackage = 0x7f020076
com.hhilan.flarum:attr/textAppearanceSearchResultTitle = 0x7f020100
com.hhilan.flarum:attr/fontProviderAuthority = 0x7f020072
com.hhilan.flarum:attr/textAppearanceSearchResultSubtitle = 0x7f0200ff
com.hhilan.flarum:attr/textAppearanceListItem = 0x7f0200fb
com.hhilan.flarum:attr/textAppearanceLargePopupMenu = 0x7f0200fa
com.hhilan.flarum:attr/suggestionRowLayout = 0x7f0200f4
com.hhilan.flarum:attr/subtitleTextColor = 0x7f0200f2
com.hhilan.flarum:attr/textAppearancePopupMenuHeader = 0x7f0200fe
com.hhilan.flarum:id/parent = 0x7f07004a
com.hhilan.flarum:styleable/LinearConstraintLayout = 0x7f0d0014
com.hhilan.flarum:attr/subtitle = 0x7f0200f0
com.hhilan.flarum:layout/abc_list_menu_item_checkbox = 0x7f09000d
com.hhilan.flarum:attr/layout_constraintHeight_min = 0x7f020097
com.hhilan.flarum:style/Widget.AppCompat.DrawerArrowToggle = 0x7f0c012b
com.hhilan.flarum:attr/layout_constraintRight_toRightOf = 0x7f0200a0
com.hhilan.flarum:id/custom = 0x7f07002a
com.hhilan.flarum:attr/subMenuArrow = 0x7f0200ee
com.hhilan.flarum:attr/elevation = 0x7f02006e
com.hhilan.flarum:color/colorPrimary = 0x7f040027
com.hhilan.flarum:attr/titleMarginBottom = 0x7f020110
com.hhilan.flarum:style/TextAppearance.AppCompat.Tooltip = 0x7f0c00dd
com.hhilan.flarum:style/Base.Theme.AppCompat.Dialog = 0x7f0c0040
com.hhilan.flarum:attr/state_above_anchor = 0x7f0200ed
com.hhilan.flarum:attr/layout_constraintHorizontal_bias = 0x7f020098
com.hhilan.flarum:id/default_activity_button = 0x7f07002d
com.hhilan.flarum:dimen/abc_floating_window_z = 0x7f05002e
com.hhilan.flarum:attr/splitTrack = 0x7f0200eb
com.hhilan.flarum:color/background_material_light = 0x7f04001d
com.hhilan.flarum:id/parentPanel = 0x7f07004b
com.hhilan.flarum:attr/dropDownListViewStyle = 0x7f020069
com.hhilan.flarum:style/Widget.AppCompat.EditText = 0x7f0c012d
com.hhilan.flarum:color/highlighted_text_material_dark = 0x7f040030
com.hhilan.flarum:attr/spinnerStyle = 0x7f0200ea
com.hhilan.flarum:color/switch_thumb_disabled_material_light = 0x7f040050
com.hhilan.flarum:string/abc_activity_chooser_view_see_all = 0x7f0b0004
com.hhilan.flarum:dimen/abc_text_size_title_material_toolbar = 0x7f050049
com.hhilan.flarum:attr/layout_goneMarginLeft = 0x7f0200b0
com.hhilan.flarum:color/abc_tint_edittext = 0x7f040014
com.hhilan.flarum:attr/progressBarStyle = 0x7f0200d6
com.hhilan.flarum:attr/layout_constraintHeight_default = 0x7f020095
com.hhilan.flarum:attr/showText = 0x7f0200e5
com.hhilan.flarum:attr/selectableItemBackgroundBorderless = 0x7f0200e2
com.hhilan.flarum:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f0c008d
com.hhilan.flarum:attr/tooltipFrameBackground = 0x7f02011b
com.hhilan.flarum:attr/layout_constraintHorizontal_weight = 0x7f02009a
com.hhilan.flarum:dimen/notification_big_circle_margin = 0x7f05005a
com.hhilan.flarum:attr/searchHintIcon = 0x7f0200dd
com.hhilan.flarum:attr/layout_constraintRight_toLeftOf = 0x7f02009f
com.hhilan.flarum:attr/backgroundTintMode = 0x7f020035
com.hhilan.flarum:color/material_grey_600 = 0x7f04003b
com.hhilan.flarum:attr/radioButtonStyle = 0x7f0200d9
com.hhilan.flarum:attr/collapseContentDescription = 0x7f020047
com.hhilan.flarum:attr/queryHint = 0x7f0200d8
com.hhilan.flarum:attr/queryBackground = 0x7f0200d7
com.hhilan.flarum:attr/popupWindowStyle = 0x7f0200d3
com.hhilan.flarum:attr/borderlessButtonStyle = 0x7f020037
com.hhilan.flarum:styleable/LinearLayoutCompat_Layout = 0x7f0d0016
com.hhilan.flarum:attr/actionProviderClass = 0x7f02001f
com.hhilan.flarum:attr/popupTheme = 0x7f0200d2
com.hhilan.flarum:id/action_text = 0x7f070016
com.hhilan.flarum:attr/panelBackground = 0x7f0200ce
com.hhilan.flarum:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f05001f
com.hhilan.flarum:attr/paddingStart = 0x7f0200cc
com.hhilan.flarum:attr/paddingBottomNoButtons = 0x7f0200ca
com.hhilan.flarum:attr/navigationMode = 0x7f0200c7
com.hhilan.flarum:string/abc_font_family_button_material = 0x7f0b000a
com.hhilan.flarum:attr/allowStacking = 0x7f020026
com.hhilan.flarum:dimen/abc_disabled_alpha_material_light = 0x7f050027
com.hhilan.flarum:attr/divider = 0x7f020063
com.hhilan.flarum:attr/dividerHorizontal = 0x7f020064
com.hhilan.flarum:attr/navigationContentDescription = 0x7f0200c5
com.hhilan.flarum:attr/logo = 0x7f0200c0
com.hhilan.flarum:attr/customNavigationLayout = 0x7f02005e
com.hhilan.flarum:id/scrollIndicatorDown = 0x7f070053
com.hhilan.flarum:attr/commitIcon = 0x7f020054
com.hhilan.flarum:id/end = 0x7f070030
com.hhilan.flarum:attr/layout_editor_absoluteX = 0x7f0200ac
com.hhilan.flarum:attr/listPreferredItemPaddingLeft = 0x7f0200be
com.hhilan.flarum:attr/listPreferredItemHeightSmall = 0x7f0200bd
com.hhilan.flarum:string/abc_font_family_display_1_material = 0x7f0b000c
com.hhilan.flarum:attr/fontProviderQuery = 0x7f020077
com.hhilan.flarum:attr/fontWeight = 0x7f020079
com.hhilan.flarum:dimen/abc_disabled_alpha_material_dark = 0x7f050026
com.hhilan.flarum:attr/listDividerAlertDialog = 0x7f0200b6
com.hhilan.flarum:attr/layout_constraintHorizontal_chainStyle = 0x7f020099
com.hhilan.flarum:attr/layout_constraintBottom_creator = 0x7f02008c
com.hhilan.flarum:attr/layout_optimizationLevel = 0x7f0200b4
com.hhilan.flarum:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f060015
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Small = 0x7f0c0023
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Display1 = 0x7f0c0013
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Body2 = 0x7f0c0010
com.hhilan.flarum:attr/listLayout = 0x7f0200b8
com.hhilan.flarum:dimen/abc_dialog_padding_material = 0x7f050023
com.hhilan.flarum:attr/layout_goneMarginStart = 0x7f0200b2
com.hhilan.flarum:attr/itemPadding = 0x7f020088
com.hhilan.flarum:attr/layout_goneMarginBottom = 0x7f0200ae
com.hhilan.flarum:attr/selectableItemBackground = 0x7f0200e1
com.hhilan.flarum:style/Base.Theme.AppCompat = 0x7f0c003e
com.hhilan.flarum:id/add = 0x7f070019
com.hhilan.flarum:style/Base.Widget.AppCompat.ActionButton = 0x7f0c0071
com.hhilan.flarum:attr/backgroundTint = 0x7f020034
com.hhilan.flarum:dimen/notification_content_margin_start = 0x7f05005b
com.hhilan.flarum:attr/layout_editor_absoluteY = 0x7f0200ad
com.hhilan.flarum:id/action_bar_subtitle = 0x7f07000b
com.hhilan.flarum:drawable/abc_ic_ab_back_material = 0x7f060014
com.hhilan.flarum:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0c0051
com.hhilan.flarum:id/action_bar_spinner = 0x7f07000a
com.hhilan.flarum:style/TextAppearance.AppCompat.Body1 = 0x7f0c00c2
com.hhilan.flarum:attr/closeItemLayout = 0x7f020046
com.hhilan.flarum:attr/actionBarWidgetTheme = 0x7f02000a
com.hhilan.flarum:attr/buttonBarStyle = 0x7f02003c
com.hhilan.flarum:attr/dialogPreferredPadding = 0x7f020060
com.hhilan.flarum:attr/layout_constraintVertical_chainStyle = 0x7f0200a7
com.hhilan.flarum:layout/abc_activity_chooser_view = 0x7f090006
com.hhilan.flarum:color/ripple_material_light = 0x7f04004a
com.hhilan.flarum:attr/windowMinWidthMajor = 0x7f020128
com.hhilan.flarum:dimen/hint_pressed_alpha_material_light = 0x7f050057
com.hhilan.flarum:attr/arrowShaftLength = 0x7f02002a
com.hhilan.flarum:attr/actionBarTheme = 0x7f020009
com.hhilan.flarum:attr/layout_constraintTop_toBottomOf = 0x7f0200a4
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Subhead = 0x7f0c0025
com.hhilan.flarum:attr/layout_constraintStart_toStartOf = 0x7f0200a2
com.hhilan.flarum:style/Base.ThemeOverlay.AppCompat = 0x7f0c004c
com.hhilan.flarum:color/notification_icon_bg_color = 0x7f040040
com.hhilan.flarum:attr/layout_constraintEnd_toStartOf = 0x7f020091
com.hhilan.flarum:attr/switchPadding = 0x7f0200f6
com.hhilan.flarum:style/Widget.Compat.NotificationActionContainer = 0x7f0c015d
com.hhilan.flarum:style/Theme.AppCompat.Dialog = 0x7f0c0102
com.hhilan.flarum:attr/layout_constraintStart_toEndOf = 0x7f0200a1
com.hhilan.flarum:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f0c0140
com.hhilan.flarum:attr/autoSizeMaxTextSize = 0x7f02002c
com.hhilan.flarum:style/Widget.AppCompat.Toolbar = 0x7f0c015b
com.hhilan.flarum:drawable/abc_ic_search_api_material = 0x7f06001f
com.hhilan.flarum:color/abc_btn_colored_text_material = 0x7f040003
com.hhilan.flarum:id/right_side = 0x7f070051
com.hhilan.flarum:color/bright_foreground_inverse_material_light = 0x7f040021
com.hhilan.flarum:attr/actionBarItemBackground = 0x7f020001
com.hhilan.flarum:attr/height = 0x7f02007c
com.hhilan.flarum:attr/layout_constraintGuide_begin = 0x7f020092
com.hhilan.flarum:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f0c0069
com.hhilan.flarum:layout/abc_action_mode_close_item_material = 0x7f090005
com.hhilan.flarum:dimen/tooltip_precise_anchor_extra_offset = 0x7f05006a
com.hhilan.flarum:style/Widget.AppCompat.CompoundButton.Switch = 0x7f0c012a
com.hhilan.flarum:dimen/abc_text_size_menu_material = 0x7f050044
com.hhilan.flarum:color/material_blue_grey_800 = 0x7f040033
com.hhilan.flarum:attr/controlBackground = 0x7f02005d
com.hhilan.flarum:dimen/abc_progress_bar_height_material = 0x7f050031
com.hhilan.flarum:attr/layout_goneMarginRight = 0x7f0200b1
com.hhilan.flarum:attr/subtitleTextStyle = 0x7f0200f3
com.hhilan.flarum:style/Base.Widget.AppCompat.ProgressBar = 0x7f0c0097
com.hhilan.flarum:attr/numericModifiers = 0x7f0200c8
com.hhilan.flarum:color/material_grey_850 = 0x7f04003d
com.hhilan.flarum:id/spread_inside = 0x7f070068
com.hhilan.flarum:id/action_bar_root = 0x7f070009
com.hhilan.flarum:color/colorAccent = 0x7f040026
com.hhilan.flarum:attr/layout_constraintEnd_toEndOf = 0x7f020090
com.hhilan.flarum:dimen/abc_edit_text_inset_bottom_material = 0x7f05002b
com.hhilan.flarum:attr/actionBarDivider = 0x7f020000
com.hhilan.flarum:style/Theme.AppCompat.NoActionBar = 0x7f0c010d
com.hhilan.flarum:id/always = 0x7f07001c
com.hhilan.flarum:attr/layout_constraintHeight_max = 0x7f020096
com.hhilan.flarum:attr/buttonBarNegativeButtonStyle = 0x7f020039
com.hhilan.flarum:attr/layout_constraintBottom_toTopOf = 0x7f02008e
com.hhilan.flarum:attr/seekBarStyle = 0x7f0200e0
com.hhilan.flarum:attr/actionMenuTextAppearance = 0x7f02000e
com.hhilan.flarum:style/Platform.V14.AppCompat = 0x7f0c00ac
com.hhilan.flarum:string/abc_font_family_headline_material = 0x7f0b0010
com.hhilan.flarum:attr/contentDescription = 0x7f020056
com.hhilan.flarum:attr/layout_constraintBaseline_toBaselineOf = 0x7f02008b
com.hhilan.flarum:drawable/notification_bg_low_normal = 0x7f060059
com.hhilan.flarum:attr/layout_constraintBaseline_creator = 0x7f02008a
com.hhilan.flarum:attr/searchViewStyle = 0x7f0200df
com.hhilan.flarum:drawable/abc_ic_star_half_black_16dp = 0x7f060023
com.hhilan.flarum:styleable/Toolbar = 0x7f0d0022
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0c00e2
com.hhilan.flarum:attr/initialActivityCount = 0x7f020086
com.hhilan.flarum:attr/textAllCaps = 0x7f0200f9
com.hhilan.flarum:attr/colorPrimary = 0x7f020051
com.hhilan.flarum:layout/select_dialog_singlechoice_material = 0x7f090024
com.hhilan.flarum:style/Platform.AppCompat = 0x7f0c00a5
com.hhilan.flarum:layout/notification_template_part_chronometer = 0x7f090020
com.hhilan.flarum:id/chronometer = 0x7f070027
com.hhilan.flarum:attr/overlapAnchor = 0x7f0200c9
com.hhilan.flarum:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f0c00b6
com.hhilan.flarum:drawable/abc_textfield_default_mtrl_alpha = 0x7f06004f
com.hhilan.flarum:color/switch_thumb_material_dark = 0x7f040051
com.hhilan.flarum:attr/drawerArrowStyle = 0x7f020068
com.hhilan.flarum:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f0c0095
com.hhilan.flarum:dimen/abc_text_size_title_material = 0x7f050048
com.hhilan.flarum:attr/gapBetweenBars = 0x7f02007a
com.hhilan.flarum:attr/actionModeShareDrawable = 0x7f020019
com.hhilan.flarum:dimen/hint_alpha_material_dark = 0x7f050054
com.hhilan.flarum:dimen/abc_text_size_subhead_material = 0x7f050046
com.hhilan.flarum:layout/abc_alert_dialog_title_material = 0x7f09000a
com.hhilan.flarum:color/bright_foreground_disabled_material_dark = 0x7f04001e
com.hhilan.flarum:attr/srcCompat = 0x7f0200ec
com.hhilan.flarum:id/spacer = 0x7f070065
com.hhilan.flarum:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f0c0083
com.hhilan.flarum:drawable/notify_panel_notification_icon_bg = 0x7f060061
com.hhilan.flarum:attr/indeterminateProgressStyle = 0x7f020085
com.hhilan.flarum:id/select_dialog_listview = 0x7f070060
com.hhilan.flarum:attr/titleMargins = 0x7f020114
com.hhilan.flarum:dimen/abc_dialog_fixed_height_minor = 0x7f05001c
com.hhilan.flarum:attr/alertDialogButtonGroupStyle = 0x7f020022
com.hhilan.flarum:color/switch_thumb_disabled_material_dark = 0x7f04004f
com.hhilan.flarum:attr/layout_goneMarginEnd = 0x7f0200af
com.hhilan.flarum:attr/actionButtonStyle = 0x7f02000b
com.hhilan.flarum:attr/fontFamily = 0x7f020071
com.hhilan.flarum:layout/abc_popup_menu_item_layout = 0x7f090012
com.hhilan.flarum:attr/panelMenuListWidth = 0x7f0200d0
com.hhilan.flarum:attr/switchTextAppearance = 0x7f0200f8
com.hhilan.flarum:attr/listPreferredItemHeightLarge = 0x7f0200bc
com.hhilan.flarum:id/button = 0x7f070022
com.hhilan.flarum:attr/listPreferredItemPaddingRight = 0x7f0200bf
com.hhilan.flarum:color/background_material_dark = 0x7f04001c
com.hhilan.flarum:id/wrap = 0x7f07007f
com.hhilan.flarum:dimen/abc_dialog_title_divider_material = 0x7f050025
com.hhilan.flarum:color/material_blue_grey_900 = 0x7f040034
com.hhilan.flarum:attr/buttonTintMode = 0x7f020042
com.hhilan.flarum:styleable/ActionMenuItemView = 0x7f0d0002
com.hhilan.flarum:attr/editTextStyle = 0x7f02006d
com.hhilan.flarum:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f0c0076
com.hhilan.flarum:string/abc_searchview_description_search = 0x7f0b0017
com.hhilan.flarum:color/abc_tint_seek_thumb = 0x7f040015
com.hhilan.flarum:attr/tint = 0x7f02010c
com.hhilan.flarum:style/TextAppearance.AppCompat.Large.Inverse = 0x7f0c00cd
com.hhilan.flarum:mipmap/ic_launcher = 0x7f0a0000
com.hhilan.flarum:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f0c00a1
com.hhilan.flarum:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f060001
com.hhilan.flarum:color/accent_material_dark = 0x7f040018
com.hhilan.flarum:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f0c0046
com.hhilan.flarum:attr/switchStyle = 0x7f0200f7
com.hhilan.flarum:attr/layout_constraintTop_creator = 0x7f0200a3
com.hhilan.flarum:color/material_grey_50 = 0x7f04003a
com.hhilan.flarum:attr/tooltipForegroundColor = 0x7f02011a
com.hhilan.flarum:drawable/abc_vector_test = 0x7f060053
com.hhilan.flarum:dimen/abc_action_bar_elevation_material = 0x7f050005
com.hhilan.flarum:attr/actionBarTabTextStyle = 0x7f020008
com.hhilan.flarum:drawable/abc_btn_radio_material = 0x7f060009
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0c002f
com.hhilan.flarum:attr/layout_constraintLeft_toRightOf = 0x7f02009d
com.hhilan.flarum:color/material_grey_300 = 0x7f040039
com.hhilan.flarum:attr/editTextColor = 0x7f02006c
com.hhilan.flarum:attr/barLength = 0x7f020036
com.hhilan.flarum:attr/dialogTheme = 0x7f020061
com.hhilan.flarum:id/src_atop = 0x7f070069
com.hhilan.flarum:attr/defaultQueryHint = 0x7f02005f
com.hhilan.flarum:attr/submitBackground = 0x7f0200ef
com.hhilan.flarum:style/Widget.AppCompat.ButtonBar = 0x7f0c0126
com.hhilan.flarum:style/AlertDialog.AppCompat.Light = 0x7f0c0001
com.hhilan.flarum:dimen/abc_dropdownitem_text_padding_left = 0x7f050029
com.hhilan.flarum:attr/maxButtonHeight = 0x7f0200c2
com.hhilan.flarum:layout/abc_search_view = 0x7f090018
com.hhilan.flarum:attr/contentInsetStartWithNavigation = 0x7f02005c
com.hhilan.flarum:attr/listPreferredItemHeight = 0x7f0200bb
com.hhilan.flarum:attr/colorBackgroundFloating = 0x7f02004b
com.hhilan.flarum:string/abc_searchview_description_submit = 0x7f0b0018
com.hhilan.flarum:attr/homeAsUpIndicator = 0x7f02007e
com.hhilan.flarum:attr/actionModeCloseDrawable = 0x7f020012
com.hhilan.flarum:id/webView = 0x7f07007d
com.hhilan.flarum:dimen/notification_large_icon_height = 0x7f05005c
com.hhilan.flarum:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f0c0063
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0c002a
com.hhilan.flarum:dimen/abc_dialog_min_width_minor = 0x7f050022
com.hhilan.flarum:attr/contentInsetRight = 0x7f02005a
com.hhilan.flarum:attr/buttonStyleSmall = 0x7f020040
com.hhilan.flarum:attr/contentInsetLeft = 0x7f020059
com.hhilan.flarum:attr/buttonGravity = 0x7f02003d
com.hhilan.flarum:color/abc_primary_text_material_light = 0x7f04000b
com.hhilan.flarum:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0c0111
com.hhilan.flarum:attr/constraintSet = 0x7f020055
com.hhilan.flarum:drawable/abc_item_background_holo_dark = 0x7f060027
com.hhilan.flarum:styleable/AlertDialog = 0x7f0d0006
com.hhilan.flarum:attr/actionModeCopyDrawable = 0x7f020013
com.hhilan.flarum:id/right_icon = 0x7f070050
com.hhilan.flarum:attr/colorError = 0x7f020050
com.hhilan.flarum:attr/colorControlNormal = 0x7f02004f
com.hhilan.flarum:attr/switchMinWidth = 0x7f0200f5
com.hhilan.flarum:style/Theme.AppCompat.Dialog.MinWidth = 0x7f0c0104
com.hhilan.flarum:attr/colorControlActivated = 0x7f02004d
com.hhilan.flarum:color/tooltip_background_dark = 0x7f040055
com.hhilan.flarum:attr/color = 0x7f020049
com.hhilan.flarum:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f0c0050
com.hhilan.flarum:color/abc_search_url_text_pressed = 0x7f04000e
com.hhilan.flarum:dimen/tooltip_vertical_padding = 0x7f05006c
com.hhilan.flarum:attr/logoDescription = 0x7f0200c1
com.hhilan.flarum:attr/actionModePasteDrawable = 0x7f020016
com.hhilan.flarum:anim/tooltip_enter = 0x7f01000a
com.hhilan.flarum:id/SHIFT = 0x7f070004
com.hhilan.flarum:drawable/abc_text_cursor_material = 0x7f060047
com.hhilan.flarum:attr/measureWithLargestChild = 0x7f0200c3
com.hhilan.flarum:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f0c013f
com.hhilan.flarum:attr/editTextBackground = 0x7f02006b
com.hhilan.flarum:attr/autoCompleteTextViewStyle = 0x7f02002b
com.hhilan.flarum:attr/subtitleTextAppearance = 0x7f0200f1
com.hhilan.flarum:dimen/abc_seekbar_track_background_height_material = 0x7f050034
com.hhilan.flarum:attr/closeIcon = 0x7f020045
com.hhilan.flarum:dimen/abc_text_size_subtitle_material_toolbar = 0x7f050047
com.hhilan.flarum:dimen/abc_dialog_fixed_width_minor = 0x7f05001e
com.hhilan.flarum:attr/buttonTint = 0x7f020041
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0c00f0
com.hhilan.flarum:attr/layout_constraintRight_creator = 0x7f02009e
com.hhilan.flarum:color/switch_thumb_normal_material_dark = 0x7f040053
com.hhilan.flarum:attr/actionModeCutDrawable = 0x7f020014
com.hhilan.flarum:id/CTRL = 0x7f070001
com.hhilan.flarum:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f0c00b4
com.hhilan.flarum:dimen/highlight_alpha_material_dark = 0x7f050052
com.hhilan.flarum:attr/buttonBarNeutralButtonStyle = 0x7f02003a
com.hhilan.flarum:color/bright_foreground_material_light = 0x7f040023
com.hhilan.flarum:styleable/DrawerArrowToggle = 0x7f0d0011
com.hhilan.flarum:attr/toolbarNavigationButtonStyle = 0x7f020118
com.hhilan.flarum:anim/tooltip_exit = 0x7f01000b
com.hhilan.flarum:attr/colorAccent = 0x7f02004a
com.hhilan.flarum:attr/listChoiceBackgroundIndicator = 0x7f0200b5
com.hhilan.flarum:style/TextAppearance.AppCompat.Small.Inverse = 0x7f0c00d8
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Body1 = 0x7f0c000f
com.hhilan.flarum:dimen/abc_text_size_display_3_material = 0x7f05003e
com.hhilan.flarum:style/Base.Theme.AppCompat.Light = 0x7f0c0045
com.hhilan.flarum:attr/panelMenuListTheme = 0x7f0200cf
com.hhilan.flarum:attr/checkedTextViewStyle = 0x7f020044
com.hhilan.flarum:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0c00e1
com.hhilan.flarum:attr/collapseIcon = 0x7f020048
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0c0034
com.hhilan.flarum:attr/isLightTheme = 0x7f020087
com.hhilan.flarum:drawable/abc_text_select_handle_middle_mtrl_light = 0x7f06004b
com.hhilan.flarum:attr/buttonStyle = 0x7f02003f
com.hhilan.flarum:style/Base.Widget.AppCompat.PopupWindow = 0x7f0c0096
com.hhilan.flarum:attr/layout = 0x7f020089
com.hhilan.flarum:attr/homeLayout = 0x7f02007f
com.hhilan.flarum:color/primary_material_dark = 0x7f040043
com.hhilan.flarum:attr/actionModeBackground = 0x7f020010
com.hhilan.flarum:anim/abc_popup_exit = 0x7f010004
com.hhilan.flarum:attr/autoSizeStepGranularity = 0x7f02002f
com.hhilan.flarum:style/Base.V12.Widget.AppCompat.AutoCompleteTextView = 0x7f0c0056
com.hhilan.flarum:attr/layout_constraintGuide_percent = 0x7f020094
com.hhilan.flarum:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f0c0048
com.hhilan.flarum:attr/textAppearanceListItemSecondary = 0x7f0200fc
com.hhilan.flarum:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.hhilan.flarum:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.hhilan.flarum:attr/iconTint = 0x7f020081
com.hhilan.flarum:attr/actionModeSelectAllDrawable = 0x7f020018
com.hhilan.flarum:style/Platform.V11.AppCompat = 0x7f0c00aa
com.hhilan.flarum:attr/actionModePopupWindowStyle = 0x7f020017
com.hhilan.flarum:styleable/AppCompatSeekBar = 0x7f0d0008
com.hhilan.flarum:attr/showAsAction = 0x7f0200e3
com.hhilan.flarum:style/Theme.AppCompat.Light = 0x7f0c0106
com.hhilan.flarum:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f0c009a
com.hhilan.flarum:id/top = 0x7f070078
com.hhilan.flarum:attr/actionModeFindDrawable = 0x7f020015
com.hhilan.flarum:attr/colorPrimaryDark = 0x7f020052
com.hhilan.flarum:attr/actionMenuTextColor = 0x7f02000f
com.hhilan.flarum:attr/layout_constraintTop_toTopOf = 0x7f0200a5
com.hhilan.flarum:attr/actionBarTabStyle = 0x7f020007
com.hhilan.flarum:style/Base.Widget.AppCompat.ButtonBar = 0x7f0c007d
com.hhilan.flarum:attr/actionBarStyle = 0x7f020005
com.hhilan.flarum:attr/layout_constraintWidth_default = 0x7f0200a9
com.hhilan.flarum:drawable/notification_tile_bg = 0x7f060060
com.hhilan.flarum:color/abc_secondary_text_material_light = 0x7f040011
com.hhilan.flarum:drawable/abc_ratingbar_small_material = 0x7f060038
com.hhilan.flarum:styleable/ColorStateListItem = 0x7f0d000d
com.hhilan.flarum:style/TextAppearance.AppCompat.Subhead = 0x7f0c00d9
com.hhilan.flarum:anim/abc_slide_in_bottom = 0x7f010006
com.hhilan.flarum:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f060005
com.hhilan.flarum:attr/buttonPanelSideLayout = 0x7f02003e
com.hhilan.flarum:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0c0135
com.hhilan.flarum:id/all = 0x7f07001b
com.hhilan.flarum:dimen/abc_text_size_display_2_material = 0x7f05003d
com.hhilan.flarum:style/Widget.AppCompat.Light.PopupMenu = 0x7f0c0141
com.hhilan.flarum:attr/actionLayout = 0x7f02000d
com.hhilan.flarum:style/TextAppearance.AppCompat.Display2 = 0x7f0c00c7
com.hhilan.flarum:drawable/tooltip_frame_light = 0x7f060063
com.hhilan.flarum:attr/actionModeCloseButtonStyle = 0x7f020011
com.hhilan.flarum:drawable/abc_btn_borderless_material = 0x7f060003
com.hhilan.flarum:dimen/abc_edit_text_inset_top_material = 0x7f05002d
com.hhilan.flarum:attr/actionBarSplitStyle = 0x7f020004
com.hhilan.flarum:anim/abc_fade_out = 0x7f010001
com.hhilan.flarum:attr/showTitle = 0x7f0200e6
com.hhilan.flarum:attr/fontProviderFetchStrategy = 0x7f020074
com.hhilan.flarum:attr/windowNoTitle = 0x7f02012a
com.hhilan.flarum:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f0c009f
com.hhilan.flarum:attr/fontProviderCerts = 0x7f020073
com.hhilan.flarum:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0c0030
com.hhilan.flarum:color/background_floating_material_dark = 0x7f04001a
com.hhilan.flarum:layout/notification_action = 0x7f09001c
com.hhilan.flarum:attr/buttonBarPositiveButtonStyle = 0x7f02003b
com.hhilan.flarum:layout/abc_screen_simple = 0x7f090014
com.hhilan.flarum:attr/spinnerDropDownItemStyle = 0x7f0200e9
com.hhilan.flarum:dimen/abc_panel_menu_list_width = 0x7f050030
com.hhilan.flarum:attr/actionModeStyle = 0x7f02001b
com.hhilan.flarum:layout/notification_action_tombstone = 0x7f09001d
com.hhilan.flarum:attr/alertDialogStyle = 0x7f020024
com.hhilan.flarum:attr/ratingBarStyleIndicator = 0x7f0200db
com.hhilan.flarum:attr/toolbarStyle = 0x7f020119
com.hhilan.flarum:style/Widget.AppCompat.Button.Borderless = 0x7f0c0121
com.hhilan.flarum:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f06001e
com.hhilan.flarum:attr/progressBarPadding = 0x7f0200d5
com.hhilan.flarum:color/bright_foreground_material_dark = 0x7f040022
com.hhilan.flarum:id/italic = 0x7f07003b
com.hhilan.flarum:anim/abc_popup_enter = 0x7f010003
