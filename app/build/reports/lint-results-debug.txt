C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle:5: Warning: A newer version of compileSdkVersion than 27 is available: 36 [GradleDependency]
    compileSdkVersion 27
    ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle:32: Warning: A newer version of com.android.support:appcompat-v7 than 27.0.2 is available: 28.0.0 [GradleDependency]
    implementation 'com.android.support:appcompat-v7:27.0.2'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle:33: Warning: A newer version of com.android.support.constraint:constraint-layout than 1.0.2 is available: 2.0.4 [GradleDependency]
    implementation 'com.android.support.constraint:constraint-layout:1.0.2'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle:35: Warning: A newer version of com.android.support.test:runner than 1.0.1 is available: 1.0.2 [GradleDependency]
    androidTestImplementation 'com.android.support.test:runner:1.0.1'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle:36: Warning: A newer version of com.android.support.test.espresso:espresso-core than 3.0.1 is available: 3.0.2 [GradleDependency]
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.1'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle:34: Warning: A newer version of junit:junit than 4.12 is available: 4.13.2 [NewerVersionAvailable]
    testImplementation 'junit:junit:4.12'
                       ~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NewerVersionAvailable":
   This detector checks with a central repository to see if there are newer
   versions available for the dependencies used by this project. This is
   similar to the GradleDependency check, which checks for newer versions
   available in the Android SDK tools and libraries, but this works with any
   MavenCentral dependency, and connects to the library every time, which
   makes it more flexible but also much slower.

C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\strings.xml:3: Warning: "occured" is a common misspelling; did you mean "occurred"? [Typos]
    <string name="error_message">Error occured, please check your internet connecion.</string>
                                       ~~~~~~~

   Explanation for issues of type "Typos":
   This check looks through the string definitions, and if it finds any words
   that look like likely misspellings, they are flagged.

C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml:11: Warning: As of Android 12, android:exported must be set; use true to make the activity available to other apps, and false otherwise. For launcher activities, this should be set to true. [IntentFilterExportedReceiver]
        <activity android:name="com.hhilan.flarum.MainActivity"
         ~~~~~~~~

   Explanation for issues of type "IntentFilterExportedReceiver":
   Apps targeting Android 12 and higher are required to specify an explicit
   value for android:exported when the corresponding component has an intent
   filter defined. Otherwise, installation will fail. Set it to true to make
   this activity accessible to other apps, and false to limit it to be used
   only by this app or the OS. For launch activities, this should be set to
   true; otherwise, the app will fail to launch.

   Previously, android:exported for components without any intent filters
   present used to default to false, and when intent filters were present, the
   default was true. Defaults which change value based on other values are
   confusing and lead to apps accidentally exporting components as a
   side-effect of adding intent filters. This is a security risk, and we have
   made this change to avoid introducing accidental vulnerabilities.

   While the default without intent filters remains unchanged, it is now
   required to explicitly specify a value when intent filters are present. Any
   app failing to meet this requirement will fail to install on any Android
   version after Android 11.

   We recommend setting android:exported to false (even on previous versions
   of Android prior to this requirement) unless you have a good reason to
   export a particular component.

   https://goo.gle/IntentFilterExportedReceiver

C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\java\com\hhilan\flarum\MainActivity.java:65: Warning: Permitting connections with SSL-related errors could allow eavesdroppers to intercept data sent by your app, which impacts the privacy of your users. Consider canceling the connections by invoking SslErrorHandler#cancel(). [WebViewClientOnReceivedSslError]
                    handler.proceed();
                    ~~~~~~~~~~~~~~~~~

   Explanation for issues of type "WebViewClientOnReceivedSslError":
   This check looks for onReceivedSslError implementations that invoke
   SslErrorHandler#proceed.

   https://goo.gle/WebViewClientOnReceivedSslError

C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle:9: Error: Google Play requires that apps target API level 33 or higher. [ExpiredTargetSdkVersion]
        targetSdkVersion 27
        ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ExpiredTargetSdkVersion":
   Configuring your app or sdk to target a recent API level ensures that users
   benefit from significant security and performance improvements, while still
   allowing your app to run on older Android versions (down to the
   minSdkVersion).

   To update your targetSdkVersion, follow the steps from "Meeting Google Play
   requirements for target API level",
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://support.google.com/googleplay/android-developer/answer/113469#targetsdk
   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.colorPrimary appears to be unused [UnusedResources]
    <color name="colorPrimary">#3F51B5</color>
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.colorPrimaryDark appears to be unused [UnusedResources]
    <color name="colorPrimaryDark">#303F9F</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\colors.xml:5: Warning: The resource R.color.colorAccent appears to be unused [UnusedResources]
    <color name="colorAccent">#FF4081</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\drawable\ic_launcher_background.xml:2: Warning: The resource R.drawable.ic_launcher_background appears to be unused [UnusedResources]
<vector
^
C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\drawable-v24\ic_launcher_foreground.xml:1: Warning: The resource R.drawable.ic_launcher_foreground appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\styles.xml:4: Warning: The resource R.style.AppTheme appears to be unused [UnusedResources]
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
           ~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml:2: Warning: The application adaptive icon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml:2: Warning: The application adaptive roundIcon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^

   Explanation for issues of type "MonochromeLauncherIcon":
   If android:roundIcon and android:icon are both in your manifest, you must
   either remove the reference to android:roundIcon if it is not needed; or,
   supply the monochrome icon in the drawable defined by the android:roundIcon
   and android:icon attribute.

   For example, if android:roundIcon and android:icon are both in the
   manifest, a launcher might choose to use android:roundIcon over
   android:icon to display the adaptive app icon. Therefore, your themed
   application iconwill not show if your monochrome attribute is not also
   specified in android:roundIcon.

C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\layout\error.xml:24: Warning: Hardcoded string "Try Again", should use @string resource [HardcodedText]
        android:text="Try Again"
        ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

1 error, 18 warnings
