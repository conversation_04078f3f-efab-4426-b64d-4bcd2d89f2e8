<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.12.0">

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of `compileSdkVersion` than 27 is available: 36"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    compileSdkVersion 27"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.android.support:appcompat-v7 than 27.0.2 is available: 28.0.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;com.android.support:appcompat-v7:27.0.2&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle"
            line="32"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.android.support.constraint:constraint-layout than 1.0.2 is available: 2.0.4"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;com.android.support.constraint:constraint-layout:1.0.2&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle"
            line="33"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.android.support.test:runner than 1.0.1 is available: 1.0.2"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    androidTestImplementation &apos;com.android.support.test:runner:1.0.1&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle"
            line="35"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.android.support.test.espresso:espresso-core than 3.0.1 is available: 3.0.2"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    androidTestImplementation &apos;com.android.support.test.espresso:espresso-core:3.0.1&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle"
            line="36"
            column="31"/>
    </issue>

    <issue
        id="NewerVersionAvailable"
        severity="Warning"
        message="A newer version of junit:junit than 4.12 is available: 4.13.2"
        category="Correctness"
        priority="4"
        summary="Newer Library Versions Available"
        explanation="This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the `GradleDependency` check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also **much** slower."
        errorLine1="    testImplementation &apos;junit:junit:4.12&apos;"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle"
            line="34"
            column="24"/>
    </issue>

    <issue
        id="Typos"
        severity="Warning"
        message="&quot;occured&quot; is a common misspelling; did you mean &quot;occurred&quot;?"
        category="Correctness:Messages"
        priority="7"
        summary="Spelling error"
        explanation="This check looks through the string definitions, and if it finds any words that look like likely misspellings, they are flagged."
        errorLine1="    &lt;string name=&quot;error_message&quot;>Error occured, please check your internet connecion.&lt;/string>"
        errorLine2="                                       ~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\strings.xml"
            line="3"
            column="40"/>
    </issue>

    <issue
        id="IntentFilterExportedReceiver"
        severity="Warning"
        message="As of Android 12, `android:exported` must be set; use `true` to make the activity available to other apps, and `false` otherwise. For launcher activities, this should be set to `true`."
        category="Security"
        priority="5"
        summary="Unspecified `android:exported` in manifest"
        explanation="Apps targeting Android 12 and higher are required to specify an explicit value for `android:exported` when the corresponding component has an intent filter defined. Otherwise, installation will fail. Set it to `true` to make this activity accessible to other apps, and `false` to limit it to be used only by this app or the OS. For launch activities, this should be set to true; otherwise, the app will fail to launch.&#xA;&#xA;Previously, `android:exported` for components without any intent filters present used to default to `false`, and when intent filters were present, the default was `true`. Defaults which change value based on other values are confusing and lead to apps accidentally exporting components as a side-effect of adding intent filters. This is a security risk, and we have made this change to avoid introducing accidental vulnerabilities.&#xA;&#xA;While the default without intent filters remains unchanged, it is now required to explicitly specify a value when intent filters are present. Any app failing to meet this requirement will fail to install on any Android version after Android 11.&#xA;&#xA;We recommend setting `android:exported` to false (even on previous versions of Android prior to this requirement) unless you have a good reason to export a particular component."
        url="https://goo.gle/IntentFilterExportedReceiver"
        urls="https://goo.gle/IntentFilterExportedReceiver"
        errorLine1="        &lt;activity android:name=&quot;com.hhilan.flarum.MainActivity&quot;"
        errorLine2="         ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\AndroidManifest.xml"
            line="11"
            column="10"/>
    </issue>

    <issue
        id="WebViewClientOnReceivedSslError"
        severity="Warning"
        message="Permitting connections with SSL-related errors could allow eavesdroppers to intercept data sent by your app, which impacts the privacy of your users. Consider canceling the connections by invoking `SslErrorHandler#cancel()`."
        category="Security"
        priority="5"
        summary="Proceeds with the HTTPS connection despite SSL errors"
        explanation="This check looks for `onReceivedSslError` implementations that invoke `SslErrorHandler#proceed`."
        url="https://goo.gle/WebViewClientOnReceivedSslError"
        urls="https://goo.gle/WebViewClientOnReceivedSslError"
        errorLine1="                    handler.proceed();"
        errorLine2="                    ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\java\com\hhilan\flarum\MainActivity.java"
            line="65"
            column="21"/>
    </issue>

    <issue
        id="ExpiredTargetSdkVersion"
        severity="Fatal"
        message="Google Play requires that apps target API level 33 or higher."
        category="Compliance"
        priority="8"
        summary="TargetSdkVersion No Longer Supported"
        explanation="Configuring your app or sdk to target a recent API level ensures that users benefit from significant security and performance improvements, while still allowing your app to run on older Android versions (down to the `minSdkVersion`).&#xA;&#xA;To update your `targetSdkVersion`, follow the steps from &quot;Meeting Google Play requirements for target API level&quot;, https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        url="https://support.google.com/googleplay/android-developer/answer/113469#targetsdk"
        urls="https://support.google.com/googleplay/android-developer/answer/113469#targetsdk,https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        errorLine1="        targetSdkVersion 27"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\build.gradle"
            line="9"
            column="9"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.colorPrimary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;colorPrimary&quot;>#3F51B5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.colorPrimaryDark` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;colorPrimaryDark&quot;>#303F9F&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.colorAccent` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;colorAccent&quot;>#FF4081&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_launcher_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\drawable\ic_launcher_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_launcher_foreground` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\drawable-v24\ic_launcher_foreground.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.AppTheme` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;style name=&quot;AppTheme&quot; parent=&quot;Theme.AppCompat.Light.DarkActionBar&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\values\styles.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        severity="Warning"
        message="The application adaptive icon is missing a monochrome tag"
        category="Usability:Icons"
        priority="6"
        summary="Monochrome icon is not defined"
        explanation="If `android:roundIcon` and `android:icon` are both in your manifest, you must either remove the reference to `android:roundIcon` if it is not needed; or, supply the monochrome icon in the drawable defined by the `android:roundIcon` and `android:icon` attribute.&#xA;&#xA;For example, if `android:roundIcon` and `android:icon` are both in the manifest, a launcher might choose to use `android:roundIcon` over `android:icon` to display the adaptive app icon. Therefore, your themed application iconwill not show if your monochrome attribute is not also specified in `android:roundIcon`."
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        severity="Warning"
        message="The application adaptive roundIcon is missing a monochrome tag"
        category="Usability:Icons"
        priority="6"
        summary="Monochrome icon is not defined"
        explanation="If `android:roundIcon` and `android:icon` are both in your manifest, you must either remove the reference to `android:roundIcon` if it is not needed; or, supply the monochrome icon in the drawable defined by the `android:roundIcon` and `android:icon` attribute.&#xA;&#xA;For example, if `android:roundIcon` and `android:icon` are both in the manifest, a launcher might choose to use `android:roundIcon` over `android:icon` to display the adaptive app icon. Therefore, your themed application iconwill not show if your monochrome attribute is not also specified in `android:roundIcon`."
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Try Again&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Try Again&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Documents\HBuilderProjects\flarum-app\app\src\main\res\layout\error.xml"
            line="24"
            column="9"/>
    </issue>

</issues>
