apply plugin: 'com.android.application'

android {
    namespace 'net.smbuhs.bbs'
    compileSdk 33
    defaultConfig {
        applicationId "net.smbuhs.bbs"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 2
        versionName "2.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    // 签名配置
    signingConfigs {
        release {
            storeFile file('../smbuhs-bbs.keystore')
            storePassword 'smbuhs123'
            keyAlias 'smbuhs-bbs'
            keyPassword 'smbuhs123'
            // 启用V1、V2和V3签名
            v1SigningEnabled true
            v2SigningEnabled true
            v3SigningEnabled true
        }
    }

    //Lint配置
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
        disable 'MissingTranslation'
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // 使用发布签名配置
            signingConfig signingConfigs.release

            // 性能优化
            debuggable false
            jniDebuggable false
            zipAlignEnabled true

            // 启用R8完全模式
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            debuggable true
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
            signingConfig signingConfigs.debug
        }
    }

    // 编译优化
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    // 构建功能
    buildFeatures {
        buildConfig true
    }

    // 打包优化
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // AndroidX 依赖
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.activity:activity:1.8.2'
    implementation 'androidx.fragment:fragment:1.6.2'

    // WebView 相关
    implementation 'androidx.webkit:webkit:1.8.0'

    // 网络和安全
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'

    // 测试依赖
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
}
